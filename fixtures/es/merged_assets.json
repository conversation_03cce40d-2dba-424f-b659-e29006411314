[{"model": "assets.MergedAsset", "source": {"merged_data": {"hostname": "host1", "fqdn": ["host1.falcon_domain"], "public_ip": ["***********"], "private_ip": [], "mac_address": [], "universal_mac_address": [], "local_mac_address": ["ff:ff:ff:ff:ff:ff"], "group_name": ["group1"], "criticality": "tier0", "primary_ip_address": "***********", "primary_mac_address": "ff:ff:ff:ff:ff:ff", "internet_exposure": "unknown", "os": {"family": "windows", "name": "Windows 10", "host_type": "workstation"}, "aad_id": null, "owner": []}, "overrides": {"criticality": "tier3"}, "source_data": {"crowdstrike_falcon": {"attributes": {"criticality": "tier2", "group_name": ["group1"], "hostname": "host1", "fqdn": ["host1.falcon_domain"], "public_ip": ["***********"], "private_ip": [], "mac_address": ["ff:ff:ff:ff:ff:ff"], "primary_ip_address": "***********", "primary_mac_address": "ff:ff:ff:ff:ff:ff", "internet_exposure": "unknown", "os": {"family": "windows", "name": "Windows 10", "host_type": "workstation"}, "aad_id": null, "owner": []}, "metadata": {"integration": {"technology_id": "crowdstrike_falcon", "category_id": "endpoint_security", "category_name": "Endpoint Security", "id": "bc47f69a-c1d3-4031-a781-45ea5b431fb7"}, "asset": {"type": "host", "last_seen": "now", "source_id": "hbk6VG6v2vddEW"}}}}, "metadata": {"account_id": "28efb657-46bb-48c2-a102-1ed8a75e5f31", "account_alias": "ABCPlumbingAccount", "organization_id": "50ec5e1d-061d-442d-aa8d-68c8b04929db", "organization_alias": "ABCPlumbing", "asset": {"type": "host", "last_seen": "now", "technologies": {"count": 1, "all": ["crowdstrike_falcon"], "all_combined": "crowdstrike_falcon", "endpoint_security": ["crowdstrike_falcon"], "endpoint_security_combined": "crowdstrike_falcon", "backup_agent": [], "backup_agent_combined": "", "full_coverage": true}}, "created": "now", "updated": "now"}}}, {"model": "assets.MergedAsset", "source": {"merged_data": {"hostname": "host1", "fqdn": ["host1.sentinel_domain"], "public_ip": ["***********"], "private_ip": [], "mac_address": [], "universal_mac_address": [], "local_mac_address": ["ff:ff:ff:ff:ff:fe"], "group_name": ["group1"], "criticality": "tier2", "primary_ip_address": "***********", "primary_mac_address": "ff:ff:ff:ff:ff:fe", "internet_exposure": "internet_facing", "os": {"family": "linux", "name": null, "host_type": "unknown"}, "aad_id": null, "owner": []}, "source_data": {"sentinel_one": {"attributes": {"criticality": "tier2", "group_name": ["group1"], "hostname": "host1", "fqdn": ["host1.sentinel_domain"], "public_ip": ["***********"], "private_ip": [], "mac_address": ["ff:ff:ff:ff:ff:fe"], "primary_ip_address": "***********", "primary_mac_address": "ff:ff:ff:ff:ff:fe", "internet_exposure": "internet_facing", "os": {"family": "windows", "name": null, "host_type": "unknown"}, "aad_id": null, "owner": []}, "metadata": {"integration": {"technology_id": "sentinel_one", "category_id": "endpoint_security", "category_name": "Endpoint Security", "id": "bc47f69a-c1d3-4031-a781-45ea5b431fb6"}, "asset": {"type": "host", "last_seen": "now", "source_id": "JFfrUKHkuHty6jb"}}}}, "metadata": {"account_id": "28efb657-46bb-48c2-a102-1ed8a75e5f31", "account_alias": "ABCPlumbingAccount", "organization_id": "50ec5e1d-061d-442d-aa8d-68c8b04929db", "organization_alias": "ABCPlumbing", "asset": {"type": "host", "last_seen": "now", "technologies": {"count": 1, "all": ["sentinel_one"], "all_combined": "sentinel_one", "endpoint_security": ["sentinel_one"], "endpoint_security_combined": "sentinel_one", "backup_agent": [], "backup_agent_combined": "", "full_coverage": true}}, "created": "now", "updated": "now"}}}, {"model": "assets.MergedAsset", "source": {"merged_data": {"hostname": "host3", "fqdn": [], "public_ip": [], "private_ip": [], "mac_address": [], "universal_mac_address": [], "local_mac_address": [], "group_name": [], "criticality": null, "primary_ip_address": null, "primary_mac_address": null, "internet_exposure": "unknown", "os": {"family": "unknown", "name": null, "host_type": "unknown"}, "aad_id": null, "owner": []}, "source_data": {"tenable_io": {"attributes": {"criticality": null, "hostname": "host3", "fqdn": [], "public_ip": [], "private_ip": [], "mac_address": [], "group_name": [], "primary_ip_address": null, "primary_mac_address": null, "internet_exposure": "unknown", "os": {"family": "unknown", "name": null, "host_type": "unknown"}, "aad_id": null, "owner": []}, "metadata": {"integration": {"technology_id": "tenable_io", "category_id": "asset_source", "category_name": "Asset Sources", "id": "54f566f2-5fae-4a25-b6b7-6a9d0b3ed526"}, "asset": {"type": "host", "last_seen": "now", "source_id": "JFfrW3KFwU4FrEF"}}}}, "metadata": {"account_id": "28efb657-46bb-48c2-a102-1ed8a75e5f31", "account_alias": "ABCPlumbingAccount", "organization_id": "50ec5e1d-061d-442d-aa8d-68c8b04929db", "organization_alias": "ABCPlumbing", "asset": {"type": "host", "last_seen": "now", "technologies": {"count": 1, "all": ["tenable_io"], "all_combined": "tenable_io", "endpoint_security": [], "endpoint_security_combined": "", "backup_agent": [], "backup_agent_combined": "", "full_coverage": false}}, "created": "now", "updated": "now"}}}]