SHELL := /bin/bash
.ONESHELL:

VIRTUALENV_NAME := $(shell basename ${PWD})

.PHONY: install init clean force_install serve
.PHONY: test test-base test-demo

all: install

${WORKON_HOME}/${VIRTUALENV_NAME}/bin/activate:
	@set -e
	source ${PROJECTS_ROOT}/helpers/bash/virtualenvwrapper_functions.sh
	mkvirtualenv --python=python3.11 ${VIRTUALENV_NAME}
	direnv allow
	direnv reload

.installed/pip-tools: ${WORKON_HOME}/${VIRTUALENV_NAME}/bin/activate
	@set -e
	mkdir -p .installed
	pip install pip-tools
	touch .installed/pip-tools

.installed/requirements.txt: requirements.txt
	@set -e
	pip install -r requirements.txt
	mkdir -p .installed
	touch .installed/requirements.txt

.installed/requirements_dev.txt: requirements_dev.txt
	@set -e
	pip install -r requirements_dev.txt --no-deps
	mkdir -p .installed
	touch .installed/requirements_dev.txt

install: .installed/requirements.txt .installed/requirements_dev.txt

init: install
	@set -e
	git config --local blame.ignoreRevsFile .git-blame-ignore-revs
	docker compose up -d --wait
	./manage.py collectstatic --noinput
	./manage.py migrate
	./manage.py superuserexists || ./manage.py createsuperuser
	./manage.py es_create_templates
	./manage.py es_create_indices
	./manage.py sync_merging_rules
	docker compose down

requirements.txt: requirements.in .installed/pip-tools
	pip-compile --no-emit-index-url --strip-extras requirements.in

requirements_dev.txt: requirements.txt requirements_dev.in .installed/pip-tools
	pip-compile --no-emit-index-url --strip-extras requirements_dev.in

test: test-base test-demo

test-base:
	pytest

test-demo:
	pytest -c pytest_demo.ini

serve:
	uvicorn core.asgi:app --reload

clean:
	rm -rf .installed

force_install: clean install
