import os

try:  # pragma: no cover
    import sentry_sdk

    SENTRY_ENVIRONMENT = os.environ["ENVIRONMENT"]
    SENTRY_RELEASE = os.environ["VERSION_TAG"]
    SENTRY_DSN = os.environ["SENTRY_DSN"]

    sentry_sdk.init(
        dsn=SENTRY_DSN,
        environment=SENTRY_ENVIRONMENT,
        release=SENTRY_RELEASE,
        in_app_exclude=[
            "newrelic.",
        ],
    )

except (ImportError, KeyError):
    # dont enable Sentry if it's not configured
    pass
