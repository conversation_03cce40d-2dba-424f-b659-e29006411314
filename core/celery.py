from logging.config import dictConfig

from ata_common.file_utils.read_env import read_env
from celery import Celery
from celery.app.task import Task
from celery.signals import setup_logging
from celery.utils.log import get_task_logger
from django.conf import settings

logger = get_task_logger(__name__)

read_env()


class BaseTask(Task):
    """
    Overriding Celery's Task for logging exceptions. Should be used for every
    @shared_task as this: @shared_task(base=BaseTask)
    """

    ignore_result = True

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """
        Log exceptions in a way that's easily parsable, since we send these in
        emails
        """
        msg = f'"{self.name}","{args}","{kwargs}","{task_id}"'
        logger.error(msg, exc_info=einfo)


app = Celery("core", task_cls=BaseTask)

app.config_from_object("django.conf:settings", namespace="CELERY")
app.autodiscover_tasks()
app.autodiscover_tasks(["apps.service_bus", "apps.stats"])


@setup_logging.connect
def configure_logging(*args, **kwargs):  # pragma: no cover
    dictConfig(settings.LOGGING)
