"""
Django settings for core project.

Generated by 'django-admin startproject' using Django 4.2.4.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
import sys
from pathlib import Path

import dj_database_url
from celery.schedules import crontab
from corsheaders.defaults import default_headers
from criticalstart.django.admin.themes.utils import theme_template_dir
from fastapi.middleware.cors import CORSMiddleware

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ["DJANGO_SECRET_KEY"]
AUTH_JWT_SECRET_KEYS = os.environ["AUTH_JWT_SECRET_KEYS"]

TESTING = (
    sys.argv[1:2] == ["test"]
    or sys.argv[:1] == ["pytest"]
    or sys.argv
    and sys.argv[0].endswith("/pytest")
)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get("DEBUG") == "True" and not TESTING

ENVIRONMENT = os.environ.get("ENVIRONMENT", "local")
DEMO_ENVIRONMENT = os.environ.get("DEMO_ENVIRONMENT") == "True"
# FIXME: remove after official release
DISABLE_FEATURES = ENVIRONMENT in ("production", "demo")

if os.environ.get("ALLOWED_HOSTS"):
    ALLOWED_HOSTS = os.environ["ALLOWED_HOSTS"].split(",")

if os.environ.get("INTERNAL_IPS"):
    INTERNAL_IPS = os.environ["INTERNAL_IPS"].split(",")


# Don't allow django to redirect malformed API requests
APPEND_SLASH = False

# auth microservice
ATA_AUTH_MICROSERVICE_URL = os.environ.get(
    "ATA_AUTH_MICROSERVICE_URL", "http://docker:8002"
)
# keep the default value in sync with ata_portal_auth's
ATA_AUTH_MICROSERVICE_SECRET_KEY = os.environ.get(
    "ATA_AUTH_MICROSERVICE_SECRET_KEY", "HS512:VRqNqmU+i+7WWELdqkSz+AwoadLouWhd"
)
ACCOUNTS_MICROSERVICE_URL = os.environ.get(
    "ACCOUNTS_MICROSERVICE_URL", "http://localhost:8012"
)
CONNECTORS_SERVICE_URL = os.environ.get("CONNECTORS_SERVICE_URL", "http://docker:8011")

FRONTEND_URL = os.environ.get("FRONTEND_URL", "http://localhost:9010")
BRANDED_URL = os.environ.get("BRANDED_URL", "http://localhost:9010")
EXTERNAL_DOMAIN = os.environ["EXTERNAL_DOMAIN"]

OPENSEARCH_HOSTS = os.environ["ELASTICSEARCH_HOSTS"].split(",")
OPENSEARCH_SLOWLOG_THRESHOLD = "300ms"

CONFIGCAT_SDK_KEY = os.environ.get("CONFIGCAT_SDK_KEY", None)

# CORS configuration - restrictive by default - only allow our own front end(s) to connect
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_ORIGIN_REGEXES = os.environ.get(
    "CORS_ALLOW_ORIGIN_REGEXES", r"^http://localhost(:\d+)?$"
).split(",")
CORS_ALLOWED_ORIGINS = os.environ.get(
    "CORS_ALLOWED_ORIGINS",
    f"{FRONTEND_URL.rstrip('/')},{BRANDED_URL.rstrip('/')},https://swagger.{EXTERNAL_DOMAIN}",
).split(",")

CORS_ALLOW_HEADERS = list(default_headers) + [
    "X-Organization",
    "X-CS-Organization",
    "X-CS-ScopedView",
]
CORS_ALLOW_METHODS = ["*"]
CORS_ALLOW_CREDENTIALS = True

SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_PATH = "/admin"  # only send Django session cookies to /admin
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_AGE = 60 * 60  # 1 hr
SESSION_EXPIRE_AT_BROWSER_CLOSE = True

# Application definition
EXTERNAL_DOMAIN = os.environ["EXTERNAL_DOMAIN"]
PORT = os.environ.get("PORT", "8000")
BACKEND_URL = os.environ["BACKEND_URL"]
SITE_ID = 1
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.humanize",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.sites",
    # Third Party
    "django_extensions",
    "django_celery_beat",
    "custom_user",
    "corsheaders",
    # Project apps
    "apps.accounts",
    "apps.connectors",
    "apps.assets",
    "apps.integrations",
    "apps.es",
]

if DEMO_ENVIRONMENT:
    INSTALLED_APPS.append("apps.demo")

MIDDLEWARE = [
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "criticalstart.django.admin.sso.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

# This is middleware which will be used by FastAPI
ASGI_MIDDLEWARE = [
    (
        CORSMiddleware,
        {
            # Settings for middleware
            "allow_origins": CORS_ALLOWED_ORIGINS,
            "allow_credentials": CORS_ALLOW_CREDENTIALS,
            "allow_methods": CORS_ALLOW_METHODS,
            "allow_headers": CORS_ALLOW_HEADERS,
            # FIXME align with CORS_ALLOW_ORIGIN_REGEXES
            "allow_origin_regex": r"^http://localhost(:\d+)?$",
        },
    )
]


ROOT_URLCONF = "core.django"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [
            theme_template_dir(),
            os.path.join(BASE_DIR, "templates"),
            os.path.join(BASE_DIR, "tests/data"),
            os.path.join(BASE_DIR, "apps/integrations/admin/templates"),
        ],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.contrib.auth.context_processors.auth",
                "django.template.context_processors.i18n",
                "django.template.context_processors.request",
                "django.template.context_processors.media",
                "django.contrib.messages.context_processors.messages",
                "django.template.context_processors.static",
                "django.template.context_processors.debug",
            ],
        },
    },
]

WSGI_APPLICATION = "core.wsgi.application"

# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {"default": dj_database_url.config()}
DATABASES["default"].update(
    {
        # 'OPTIONS': {'sslmode': 'require'},
        "ATOMIC_REQUESTS": True,
        "CONN_MAX_AGE": 36000,
    }
)

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
        "OPTIONS": {
            "min_length": 9,
        },
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
]

AUTHENTICATION_BACKENDS = ("django.contrib.auth.backends.ModelBackend",)

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "serve_static/")
STORAGES = {
    "default": {
        "BACKEND": "django.core.files.storage.FileSystemStorage",
    },
    "staticfiles": {
        "BACKEND": "whitenoise.storage.CompressedManifestStaticFilesStorage",
    },
}

FIXTURE_DIRS = (os.path.join(BASE_DIR, "fixtures"),)

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Django Admin
ADMIN_OIDC_ISSUER = os.environ.get("AUTH0_ISSUER")
ADMIN_OIDC_CLIENT_ID = os.environ.get("AUTH0_ADMIN_ID")
ADMIN_OIDC_CLIENT_SECRET = os.environ.get("AUTH0_ADMIN_SECRET")
ADMIN_OIDC_AUDIENCE = os.environ.get("ADMIN_OIDC_AUDIENCE", ADMIN_OIDC_CLIENT_ID)
ADMIN_OIDC_CREATE_AS_SUPERUSER = (
    os.environ.get("ADMIN_OIDC_CREATE_AS_SUPERUSER", "True").lower() == "true"
)
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")  # for AWS LB

# Celery
CELERY_BROKER_URL = os.environ["CELERY_BROKER_URL"].split(";")

CELERY_TIMEZONE = "UTC"
CELERY_WORKER_DISABLE_RATE_LIMITS = True

CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"

CELERY_TASK_TIME_LIMIT = 2 * 60 * 60
CELERY_TASK_SOFT_TIME_LIMIT = 10 * 60 * 60
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1024
CELERY_SEND_TASK_ERROR_EMAILS = True

# see https://docs.celeryq.dev/en/stable/userguide/configuration.html for details
CELERY_TASK_ACKS_LATE = True
CELERY_TASK_REJECT_ON_WORKER_LOST = True
CELERY_WORKER_CANCEL_LONG_RUNNING_TASKS_ON_CONNECTION_LOSS = True

CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"
# Feb 29 of every year. Clearly celery thinks Feb 31st takes 100% CPU to
# compute. Hopefully 29th will be easier to compute
NEVER = crontab("0", "2", day_of_month="29", month_of_year="2")

CELERY_BEAT_SCHEDULE = {
    "Fetch Assets": {
        "task": "apps.assets.tasks.invoke_fetch_assets",
        "schedule": crontab(minute="*/60") if not DEBUG else NEVER,
    },
    "Record Endpoint Coverage Gaps": {
        "task": "apps.stats.tasks.record_endpoint_gaps",
        "schedule": crontab(minute="0", hour="*/4") if not DEMO_ENVIRONMENT else NEVER,
    },
    "Record Daily Coverage Gaps": {
        "task": "apps.stats.tasks.record_daily_coverage_gaps",
        "schedule": crontab(minute="30", hour="*/4") if not DEBUG else NEVER,
    },
    "Sync Organizations": {
        "task": "apps.accounts.tasks.sync_organizations",
        "schedule": crontab(minute="0", hour="*/1") if not DEBUG else NEVER,
    },
    "Delete Old Sync Tasks": {
        "task": "apps.assets.tasks.delete_old_sync_tasks",
        "schedule": crontab(minute="0", hour="6", day_of_week="1")
        if not DEBUG
        else NEVER,
    },
}

CONNECTOR_SERVICE_TOKEN = os.environ.get("CONNECTOR_SERVICE_TOKEN")

# FastAPI Settings
SERVICE_NAME = os.environ.get("SERVICE_NAME", "asset_inventory")
VERSION_TAG = os.environ.get("VERSION_TAG", "local")
THEME_EMOJI = "\U0001F4CB"  # Clipboard
OPENAPI_URL = os.environ.get("OPENAPI_URL", "/openapi.json")

# Raw Assets
RAW_ASSET_FILE_SYSTEM_URL = os.environ.get(
    "RAW_ASSET_FILE_SYSTEM_URL",
    "~/.cache/criticalstart/asset_inventory/raw_assets/",
)


# Logging
def get_formatters():
    formatters = {
        "json": {"()": "ata_common.logging.formatters.json.JSONFormatter"},
    }
    if DEBUG:
        formatters["colored"] = {
            "class": "colorlog.ColoredFormatter",
            "format": "%(log_color)s%(levelname)s %(asctime)s %(name)s %(message)s",
        }
    return formatters


JSON_DEBUG = os.environ.get("JSON_DEBUG") == "True"

# Logging
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": get_formatters(),
    "handlers": {
        "null": {
            "level": "INFO",
            "class": "logging.NullHandler",
        },
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "json" if not DEBUG or JSON_DEBUG else "colored",
        },
    },
    "root": {
        "handlers": ["console"],
        "level": "INFO",
    },
    "loggers": {
        "botocore": {"handlers": ["null"], "level": "INFO"},
        "pika": {"handlers": ["null"], "level": "INFO"},
        "criticalstart": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "kombu": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "django.server": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "django": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "apps": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "ssoLogger": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "celery": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "uvicorn": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "gunicorn": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "fastapi": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
    },
}
