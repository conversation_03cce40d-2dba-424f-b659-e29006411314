from unittest.mock import patch

from billiard.einfo import ExceptionInfo
from celery import shared_task
from django.test import TestCase


@shared_task()
def test_error():
    pass


class TestCeleryBaseTask(TestCase):
    @patch("core.celery.logger")
    def test_base_task_error(self, mlogger):
        # Create an exception and ExceptionInfo object
        # to pass to the on_failure method
        try:
            raise Exception("Test exception")
        except Exception as e:
            exception = e
            einfo = ExceptionInfo()

        task_id = "test_task_id"

        test_error.on_failure(exception, task_id, [], {}, einfo)
        mlogger.error.assert_called_once_with(
            f'"base_task.test_error","[]","{{}}","test_task_id"',
            exc_info=einfo,
        )
