"""
ASGI config for core project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/asgi/
"""

import os

from django.conf import settings
from django.core.wsgi import get_wsgi_application
from fastapi import FastAPI
from fastapi.middleware.wsgi import WSGIMiddleware
from fastapi.routing import APIRoute
from fastapi.staticfiles import StaticFiles

# importing fastapi router will load Django models, which will raise
# django.core.exceptions.AppRegistryNotReady if django.setup() is not called
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings")
django_app = get_wsgi_application()  # trigger django.setup()


def generate_operation_id(route: APIRoute):
    """
    This method generates operation_id for each FastAPI route.
    """
    # if operation_id was explicitly set, use that
    if route.operation_id:
        return route.operation_id

    # prefix internal routes with 'internal_'
    if route.path.startswith("/internal/"):
        return f"internal_{route.name}"

    # otherwise, use the route name (i.e., method name)
    return route.name


# Fast API
from core.router import router

app = FastAPI(
    title=settings.SERVICE_NAME,
    openapi_url=settings.OPENAPI_URL,
    debug=settings.DEBUG,
    generate_unique_id_function=generate_operation_id,
)

for middleware, options in settings.ASGI_MIDDLEWARE:
    app.add_middleware(middleware_class=middleware, **options)

from apps.api.exception_handlers import add_exception_handlers

add_exception_handlers(app)

# Include all FastAPI endpoints
app.include_router(router)

# Mounts Django Views
app.mount("/static", StaticFiles(directory=settings.STATIC_ROOT), name="static")
app.mount("/admin", WSGIMiddleware(django_app))
