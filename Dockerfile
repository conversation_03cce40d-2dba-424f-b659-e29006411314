ARG BUILD_CONTAINER_HASH
FROM 818476207984.dkr.ecr.us-west-2.amazonaws.com/advancedthreatanalytics/asset_inventory:$BUILD_CONTAINER_HASH AS cached_build

FROM 818476207984.dkr.ecr.us-west-2.amazonaws.com/advancedthreatanalytics/python-slim:3.11-build AS build
ARG PIP_INDEX_URL

COPY requirements.txt .
RUN pip install -r requirements.txt --no-deps

FROM 818476207984.dkr.ecr.us-west-2.amazonaws.com/advancedthreatanalytics/python-slim:3.11-base AS deploy
WORKDIR /opt/app

COPY --from=cached_build /opt/venv /opt/venv
COPY ./ /opt/app/

ENV ALLOWED_HOSTS=docker,localhost \
    DISABLE_ENVRC_LOADING=1 \
    DEBUG=False \
    DJANGO_SETTINGS_MODULE=core.settings \
    PYTHONPATH=/opt/app \
    PYTHONUNBUFFERED=true \
    TERM=xterm \
    NEW_RELIC_CONFIG_FILE=newrelic.ini \
    VERSION_TAG=${TAG} \
    EXTERNAL_DOMAIN=ataplatform.io \
    BACKEND_URL=http://localhost:8000 \
    AUTH_JWT_SECRET_KEYS=HS512:26D31b6DwAiINRod6Uq9UPLFEnPbIRrBCdEPLK1P

RUN DJANGO_SECRET_KEY=DUMMY_VALUE \
    CELERY_BROKER_URL=DUMMY_VALUE \
    ELASTICSEARCH_HOSTS=DUMMY_VALUE \
    python manage.py collectstatic --noinput


EXPOSE 8000
CMD ["python", "manage.py", "migrate"]

HEALTHCHECK --interval=5s --timeout=1s --start-period=2s \
    CMD curl --fail localhost:8000/api/v1/version/

FROM cached_build AS test

COPY --from=deploy /opt/app /opt/app
ENV DISABLE_ENVRC_LOADING=1
WORKDIR /opt/app

COPY requirements_dev.txt .
RUN pip install -r requirements_dev.txt
