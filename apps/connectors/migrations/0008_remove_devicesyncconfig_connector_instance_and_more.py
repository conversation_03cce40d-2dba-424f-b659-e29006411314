# Generated by Django 4.2.5 on 2023-09-20 07:42

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("assets", "0002_synctask_delete_device"),
        ("connectors", "0007_connectorinstanceextension_connector_template_category"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="devicesyncconfig",
            name="connector_instance",
        ),
        migrations.RemoveField(
            model_name="synctask",
            name="content_type",
        ),
        migrations.RemoveField(
            model_name="usersyncconfig",
            name="connector_instance",
        ),
        migrations.DeleteModel(
            name="ConnectorInstanceExtension",
        ),
        migrations.DeleteModel(
            name="DeviceSyncConfig",
        ),
        migrations.DeleteModel(
            name="SyncTask",
        ),
        migrations.DeleteModel(
            name="UserSyncConfig",
        ),
    ]
