# Generated by Django 3.2.19 on 2023-07-28 10:22

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("connectors", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="devicesyncconfig",
            name="type_key",
            field=models.CharField(default="", max_length=255),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="devicesyncconfig",
            name="connector_instance",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to="connectors.connectorinstanceextension",
            ),
        ),
        migrations.CreateModel(
            name="UserSyncConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("enabled", models.<PERSON><PERSON>anField(default=False)),
                ("type_key", models.CharField(max_length=255)),
                (
                    "connector_instance",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="connectors.connectorinstanceextension",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="SyncTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("task_id", models.CharField(max_length=255, unique=True)),
                (
                    "status",
                    models.CharField(
                        choices=[("started", "Started"), ("completed", "Completed")],
                        max_length=255,
                    ),
                ),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("finished_at", models.DateTimeField(blank=True, null=True)),
                ("object_id", models.PositiveIntegerField()),
                (
                    "content_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
            ],
        ),
    ]
