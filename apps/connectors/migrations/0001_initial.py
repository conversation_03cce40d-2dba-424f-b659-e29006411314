# Generated by Django 3.2.19 on 2023-07-07 10:46

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="ConnectorInstanceExtension",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("connector_instance_uid", models.Char<PERSON>ield(max_length=255)),
                ("connector_template_uid", models.<PERSON>r<PERSON>ield(max_length=255)),
                ("account_uid", models.Char<PERSON>ield(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name="DeviceSyncConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("enabled", models.Boolean<PERSON>ield(default=False)),
                (
                    "connector_instance",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="device_sync_config",
                        to="connectors.connectorinstanceextension",
                    ),
                ),
            ],
        ),
    ]
