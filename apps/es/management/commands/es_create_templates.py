from django.core.management.base import BaseCommand

from apps.es import es_manager


class Command(BaseCommand):
    help = "Configure Elasticsearch templates."

    def add_arguments(self, parser):
        parser.add_argument(
            "--develop",
            action="store_true",
            dest="develop",
            default=False,
            help="Are we running in develop",
        )

    def handle(self, *args, **options):
        es_manager.configure_mappings(options["develop"])
