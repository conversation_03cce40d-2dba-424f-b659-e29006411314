from django.core.management.base import BaseCommand

from apps.assets.models import MergedAsset, SourceHost
from apps.es import es_service


class Command(BaseCommand):
    """
    Create indices for asset data
    """

    help = "Create indices for asset data"

    def handle(self, *args, **options):
        if not es_service.es.indices.exists(index=MergedAsset.documents.INDEX_WRITE):
            es_service.es.indices.create(index=MergedAsset.documents.INDEX_WRITE)

        if not es_service.es.indices.exists(index=SourceHost.documents.INDEX_WRITE):
            es_service.es.indices.create(index=SourceHost.documents.INDEX_WRITE)
