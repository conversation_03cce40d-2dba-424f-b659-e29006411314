from unittest import TestCase

from django.core.management import call_command

from apps.assets.models import MergedAsset, SourceHost
from apps.assets.tests.es_base import ESCaseMixin
from apps.es import es_service


class ESCreateIndicesCommandTestCase(ESCaseMixin, TestCase):
    def test_es_create_indices(self):
        indices = [MergedAsset.documents.INDEX_WRITE, SourceHost.documents.INDEX_WRITE]

        for index in indices:
            if es_service.es.indices.exists(index=index):
                es_service.es.indices.delete(index=index)

        call_command("es_create_indices")

        for index in indices:
            self.assertTrue(
                es_service.es.indices.exists(index=index),
                f"Index {index} does not exist",
            )
