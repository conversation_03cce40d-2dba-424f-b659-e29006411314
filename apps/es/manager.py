from .service import es_service
from .templates import daily_coverage_gaps, merged_assets, source_host, stats

MODULES = (merged_assets, stats, daily_coverage_gaps)
DOCUMENTS = (source_host.SourceHost,)


class ESManager:
    def configure_mappings(self, develop=False):
        self._put_mappings(MODULES, es_service.es, develop)
        self._put_document_templates(DOCUMENTS, es_service.es, develop)

    @staticmethod
    def _put_mappings(modules, client, develop):
        templates = {}

        for module in modules:
            templates.update(module.get_templates(develop=develop))

        for name, template in templates.items():
            client.indices.put_template(name, body=template)

    @staticmethod
    def _put_document_templates(documents, client, develop):
        for document in documents:
            document.init_template()


es_manager = ESManager()
