from copy import deepcopy

from ata_common.utils.dict import update_nested_dict
from django.conf import settings

OPENSEARCH_PRIMARY_SHARDS = 4
OPENSEARCH_REPLICAS_SHARDS = 1

MERGED_ASSET_INDEX_SETTINGS = {
    "index_patterns": "merged_asset-*",
    "settings": {
        "index.refresh_interval": "1s",
        "index.analysis.analyzer": {
            "analyzer_lowercase": {"tokenizer": "keyword", "filter": "lowercase"}
        },
        "index.analysis.normalizer": {
            "lowercase_normalizer": {
                "type": "custom",
                "char_filter": [],
                "filter": ["lowercase"],
            }
        },
        "index.mapping.total_fields.limit": 25344,
        "index.max_regex_length": 8192,
        "index.search.slowlog.threshold.query.warn": settings.OPENSEARCH_SLOWLOG_THRESHOLD,
        "index.search.slowlog.threshold.fetch.warn": settings.OPENSEARCH_SLOWLOG_THRESHOLD,
        "index.indexing.slowlog.threshold.index.warn": settings.OPENSEARCH_SLOWLOG_THRESHOLD,
    },
    "mappings": {
        "dynamic_templates": [
            {
                "objects": {
                    "match_mapping_type": "object",
                    "mapping": {
                        "type": "object",
                    },
                }
            },
            {
                "string": {
                    "match_mapping_type": "*",
                    "mapping": {
                        "type": "text",
                        "analyzer": "analyzer_lowercase",
                        "fields": {
                            "keyword": {
                                "type": "keyword",
                                "ignore_above": 1024,
                                "normalizer": "lowercase_normalizer",
                            }
                        },
                    },
                }
            },
        ],
        "properties": {
            "@timestamp": {"type": "date"},
            "metadata": {
                "type": "object",
                "properties": {
                    "organization_id": {"type": "keyword"},
                    "asset": {
                        "type": "object",
                        "properties": {
                            "type": {"type": "keyword"},
                            "last_seen": {"type": "date"},
                            "last_seen_endpoint_security": {"type": "date"},
                            "last_seen_vulnerability_management": {"type": "date"},
                            "technologies": {
                                "type": "object",
                                "properties": {
                                    "count": {"type": "integer"},
                                    "all": {
                                        "type": "keyword",
                                        "ignore_above": 1024,
                                        "normalizer": "lowercase_normalizer",
                                    },
                                    "all_combined": {
                                        "type": "keyword",
                                        "ignore_above": 1024,
                                        "normalizer": "lowercase_normalizer",
                                    },
                                    "endpoint_security": {
                                        "type": "keyword",
                                        "ignore_above": 1024,
                                        "normalizer": "lowercase_normalizer",
                                    },
                                    "endpoint_security_combined": {
                                        "type": "keyword",
                                        "ignore_above": 1024,
                                        "normalizer": "lowercase_normalizer",
                                    },
                                    "vulnerability_management": {
                                        "type": "keyword",
                                        "ignore_above": 1024,
                                        "normalizer": "lowercase_normalizer",
                                    },
                                    "vulnerability_management_combined": {
                                        "type": "keyword",
                                        "ignore_above": 1024,
                                        "normalizer": "lowercase_normalizer",
                                    },
                                    "backup_agent": {
                                        "type": "keyword",
                                        "ignore_above": 1024,
                                        "normalizer": "lowercase_normalizer",
                                    },
                                    "backup_agent_combined": {
                                        "type": "keyword",
                                        "ignore_above": 1024,
                                        "normalizer": "lowercase_normalizer",
                                    },
                                    "full_coverage": {
                                        "type": "boolean",
                                    },
                                },
                            },
                        },
                    },
                    "created": {"type": "date"},
                    "updated": {"type": "date"},
                },
            },
            "overrides": {
                "type": "object",
                "properties": {
                    "criticality": {
                        "type": "text",
                        "analyzer": "analyzer_lowercase",
                        "fields": {
                            "keyword": {
                                "type": "keyword",
                                "ignore_above": 1024,
                                "normalizer": "lowercase_normalizer",
                            }
                        },
                    },
                },
            },
        },
    },
}


def get_templates(develop=False):
    template = update_nested_dict(
        deepcopy(MERGED_ASSET_INDEX_SETTINGS),
        {
            "settings": {
                "index.number_of_shards": 1 if develop else OPENSEARCH_PRIMARY_SHARDS,
                "index.number_of_replicas": 0
                if develop
                else OPENSEARCH_REPLICAS_SHARDS,
            },
        },
    )

    return {"merged_asset": template}
