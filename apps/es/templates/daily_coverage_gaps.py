from copy import deepcopy

from ata_common.utils.dict import update_nested_dict
from django.conf import settings

OPENSEARCH_PRIMARY_SHARDS = 4
OPENSEARCH_REPLICAS_SHARDS = 1

DAILY_COVERAGE_GAPS_INDEX_SETTINGS = {
    "index_patterns": "daily_coverage_gaps-*",
    "settings": {
        "index.refresh_interval": "30s",
        "index.mapping.total_fields.limit": 8192,
        "index.max_regex_length": 8192,
        "index.search.slowlog.threshold.query.warn": settings.OPENSEARCH_SLOWLOG_THRESHOLD,
        "index.search.slowlog.threshold.fetch.warn": settings.OPENSEARCH_SLOWLOG_THRESHOLD,
        "index.indexing.slowlog.threshold.index.warn": settings.OPENSEARCH_SLOWLOG_THRESHOLD,
    },
}


def get_templates(develop=False):
    template = update_nested_dict(
        deepcopy(DAILY_COVERA<PERSON>_GAPS_INDEX_SETTINGS),
        {
            "settings": {
                "index.number_of_shards": 1 if develop else OPENSEARCH_PRIMARY_SHARDS,
                "index.number_of_replicas": 0
                if develop
                else OPENSEARCH_REPLICAS_SHARDS,
            },
        },
    )

    return {"daily_coverage_gaps": template}
