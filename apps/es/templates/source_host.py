from opensearchpy import Date, Document, InnerDoc, Keyword, <PERSON>a<PERSON><PERSON>, Object, Text
from opensearchpy.helpers.analysis import (
    CustomAnalyzer,
    CustomNormalizer,
)

from apps.es.service import es_service

lowercase_analyzer = CustomAnalyzer(
    "lowercase_analyzer",
    tokenizer="keyword",
    filter=["lowercase"],
)

lowercase_normalizer = CustomNormalizer(
    "lowercase_normalizer",
    char_filter=[],
    filter=["lowercase"],
)

standard_keyword = Keyword(normalizer=lowercase_normalizer)

standard_text = Text(
    analyzer=lowercase_analyzer,
    fields={"keyword": Keyword(ignore_above=1024, normalizer=lowercase_normalizer)},
)


class OsAttributes(InnerDoc):
    host_type = standard_keyword
    family = standard_keyword
    name = standard_keyword


class OwnerAttributes(InnerDoc):
    name = standard_keyword
    email = standard_keyword


class HostAttributes(InnerDoc):
    criticality = standard_keyword
    last_seen = Date()

    hostname = standard_keyword
    fqdns = standard_keyword
    os = Object(OsAttributes)

    public_ips = standard_keyword
    private_ips = standard_keyword
    primary_ip_address = standard_keyword
    universal_mac_addresses = standard_keyword
    local_mac_addresses = standard_keyword
    primary_mac_address = standard_keyword
    internet_exposure = standard_keyword

    owner = Object(OwnerAttributes)
    group_names = standard_keyword

    aad_id = standard_keyword


class IntegrationMeta(InnerDoc):
    id = standard_keyword
    organization_id = standard_keyword
    technology_id = standard_keyword
    category_id = standard_keyword
    vulnerability_coverage_mode = standard_keyword
    endpoint_coverage_mode = standard_keyword


class MergedAssetMeta(InnerDoc):
    id = standard_keyword
    created = Date()
    manually_merged = standard_keyword


class SourceHost(Document):
    class Index:
        name = "source_host-current"
        using = es_service.es

    class Meta:
        dynamic = MetaField("false")

    source_id = standard_keyword
    type = standard_keyword

    created = Date()
    updated = Date()

    attributes = Object(HostAttributes)
    integration = Object(IntegrationMeta)
    merged_asset = Object(MergedAssetMeta)

    @classmethod
    def init_template(cls):
        # run as part of application setup, during deploy/migrations:
        logs = cls._index.as_template("source_host", "source_host-*", order=0)
        logs.save()
