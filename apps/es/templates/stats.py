from copy import deepcopy

from ata_common.utils.dict import update_nested_dict
from django.conf import settings

OPENSEARCH_PRIMARY_SHARDS = 4
OPENSEARCH_REPLICAS_SHARDS = 1

STATS_INDEX_SETTINGS = {
    "index_patterns": "persistent_stats-*",
    "settings": {
        "index.refresh_interval": "30s",
        "index.analysis.analyzer": {
            "analyzer_lowercase": {"tokenizer": "keyword", "filter": "lowercase"}
        },
        "index.analysis.normalizer": {
            "lowercase_normalizer": {
                "type": "custom",
                "char_filter": [],
                "filter": ["lowercase"],
            }
        },
        "index.mapping.total_fields.limit": 8192,
        "index.max_regex_length": 8192,
        "index.search.slowlog.threshold.query.warn": settings.OPENSEARCH_SLOWLOG_THRESHOLD,
        "index.search.slowlog.threshold.fetch.warn": settings.OPENSEARCH_SLOWLOG_THRESHOLD,
        "index.indexing.slowlog.threshold.index.warn": settings.OPENSEARCH_SLOWLOG_THRESHOLD,
    },
    "mappings": {
        "dynamic_templates": [
            {
                "objects": {
                    "match_mapping_type": "object",
                    "mapping": {
                        "type": "object",
                    },
                }
            },
            {
                "string": {
                    "match_mapping_type": "*",
                    "mapping": {
                        "type": "text",
                        "analyzer": "analyzer_lowercase",
                        "fields": {
                            "keyword": {
                                "type": "keyword",
                                "ignore_above": 1024,
                                "normalizer": "lowercase_normalizer",
                            }
                        },
                    },
                }
            },
        ],
        "properties": {
            "metadata": {
                "type": "object",
                "properties": {
                    "organization_id": {"type": "keyword"},
                    "stat": {
                        "type": "object",
                        "properties": {
                            "type": {"type": "keyword"},
                        },
                    },
                    "created": {"type": "date"},
                },
            },
        },
    },
}


def get_templates(develop=False):
    template = update_nested_dict(
        deepcopy(STATS_INDEX_SETTINGS),
        {
            "settings": {
                "index.number_of_shards": 1 if develop else OPENSEARCH_PRIMARY_SHARDS,
                "index.number_of_replicas": 0
                if develop
                else OPENSEARCH_REPLICAS_SHARDS,
            },
        },
    )

    return {"stats": template}
