import binascii
from base64 import b64decode, b64encode

from django.conf import settings
from opensearchpy import OpenSearch

TIMEOUT_SEARCH = "30s"
OPENSEARCH_CONNECTION_TIMEOUT = 300


class ESService:
    def __init__(self, host=settings.OPENSEARCH_HOSTS):
        self.es = OpenSearch(
            host, retry_on_timeout=True, timeout=OPENSEARCH_CONNECTION_TIMEOUT
        )

    @staticmethod
    def encode_locator(index, typ, eid):
        """
        Generate locator from its parts. A locator in our system is nothing
        more than 3 parts together
        1. `index` where data is stored
        2. `typ` - which used to be `type` before ES6. Now this is mostly `_doc`
        3. `eid` - The Id generated by OpenSearch when storing the document

        We like to keep locators to things not just IDs as IDs don't tell us
        _which index_ has our data. Locators are everywhere in our code and they
        """

        parts = bytes("$".join([index, typ, eid]), encoding="utf-8")
        return str(b64encode(parts), encoding="utf-8")

    @staticmethod
    def decode_locator(locator):
        """
        Decode locator into its parts. Locator is stored everywhere (ES and PG)
        as Strings. This method takes care of encoding them to bytes and going
        thorough base64 circus. Upstream always receives tuple of the different
        parts of the locator
        """
        if not isinstance(locator, bytes):
            locator = bytes(locator, encoding="utf-8")
        try:
            locator = b64decode(locator)
        except binascii.Error:
            return None, None, None

        locator = str(locator, encoding="utf-8")
        return locator.split("$")

    def get_locator(self, doc):
        """
        Returns a unique string that can be used to locate the document in
        OpenSearch across any index.
        """

        return self.encode_locator(doc["_index"], "_doc", doc["_id"])

    @staticmethod
    def get_seq_no(doc):
        """
        Returns the sequence number of the document
        """
        return doc.get("_seq_no")

    @staticmethod
    def get_primary_term(doc):
        """
        Returns the primary term of the document
        """
        return doc.get("_primary_term")


es_service = ESService()
