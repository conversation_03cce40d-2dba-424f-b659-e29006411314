from contextlib import contextmanager
from json import JSONDecodeError, loads
from unittest.mock import patch
from uuid import UUID

from criticalstart.auth.v2.integrations.fastapi import UserSession
from django.template.loader import get_template, render_to_string
from django.test.testcases import TestCase, TransactionTestCase
from fastapi.testclient import TestClient

from apps.accounts.models import Organization
from apps.api.dependencies import session
from apps.feature_flag import feature_flag_service
from core.asgi import app


class ApiCaseMixin:
    def setUp(self):
        super().setUp()

        # Users of this mixin should set self.auth_response to a valid
        # UserSessionModel object to ensure api functions get a correct session
        self.auth_response = None

        async def override_session():
            return self.auth_response

        app.dependency_overrides[session] = override_session

        self.api_client = TestClient(
            app,
        )

        # Ensure we are authenticated and the session provides valid data the to APIs.
        # For now,   we are providing a consistent set of data back to all test.  It is
        # worth considering at some point if each test or fixture should really be
        # responsible for providing the data it want.
        #
        # For now, the orgs returned (matching the ones in the accounts.json fixture)
        # are:
        # Organization Context is test_organization1
        # Organization structure is
        #   test_organization1
        #       test_organization2

        self.auth_response = UserSession.model_validate(
            {
                "user": {
                    "id": self.user_id,
                    "name": "Org User (test123)",
                    "email": "<EMAIL>",
                    "phone": "",
                    "enabled": True,
                    "created": "2021-01-01T00:00:00.000Z",
                },
                "organization": {
                    "id": self.organization.id,
                    "alias": self.organization.alias,
                    "name": "Testing Org 1",
                    "account_id": self.organization.account_id,
                    "account_alias": self.organization.account.alias,
                },
                "organizations": [
                    {
                        "organization": {
                            "id": self.organization.id,
                            "alias": self.organization.alias,
                            "name": "Testing Org 1",
                            "account_id": self.organization.account_id,
                            "account_alias": self.organization.account.alias,
                        },
                        "permissions": [],
                    },
                    {
                        "organization": {
                            "id": self.child_organization.id,
                            "alias": self.child_organization.alias,
                            "name": "Testing Org 2",
                            "account_id": self.child_organization.account_id,
                            "account_alias": self.child_organization.account.alias,
                        },
                        "permissions": [],
                    },
                ],
            }
        )

    def auth_as_child_org(self):
        child_org = self.auth_response.organizations[1]
        self.auth_response.organization = child_org.organization
        self.auth_response.organizations = [child_org]

    def set_org_permissions(self, organization, permissions):
        for org in self.auth_response.organizations:
            if org.organization.id == organization.id:
                org.permissions = set(permissions)
                return
        raise Exception(
            "No organization found with id {}".format(organization.id)
        )  # pragma: no cover


class BaseCaseMixin:
    fixtures = ["accounts.json", "integrations.json"]
    maxDiff = None

    def setUp(self):
        super().setUp()
        self.user_id = UUID("50de79d4-07a9-40de-9a35-4624d843e33a")
        self.organization = Organization.objects.get(alias="test_organization1")
        self.child_organization = Organization.objects.get(alias="test_organization2")
        self.patch_dict(
            feature_flag_service.flag_defaults, {"Asset_Inventory_Setting_Test": True}
        )

    @contextmanager
    def data(self, fixture_name, render=True, replace=None):  # pragma: no cover
        template = (
            render_to_string(fixture_name)
            if render
            else get_template(fixture_name).template.source
        )
        try:
            template = loads(template)
            if replace:
                template.update(replace)
        except JSONDecodeError:
            pass
        yield template

    def patch(self, *args, **kwargs):
        patcher = patch(*args, **kwargs)
        mock = patcher.start()
        self.addCleanup(patcher.stop)
        return mock

    def patch_object(self, *args, **kwargs):
        patcher = patch.object(*args, **kwargs)
        mock = patcher.start()
        self.addCleanup(patcher.stop)
        return mock

    def patch_dict(self, *args, **kwargs):
        patcher = patch.dict(*args, **kwargs)
        mock = patcher.start()
        self.addCleanup(patcher.stop)
        return mock


class BaseTestCase(ApiCaseMixin, BaseCaseMixin, TestCase):
    fixtures = BaseCaseMixin.fixtures


# TestClient runs request output in another thread. This opens
# a separate connection to the database which makes transactions from
# outside that thread unvisible. Since TestCase runs the entire test
# inside a transaction, this makes any DB edits (including fixtures)
# invisible during request processing.
# https://github.com/tiangolo/fastapi/discussions/8938
# FIXME: Share database connection between threads
#  Note that django has built-in protections around this. see
#  * django.db.backends.base.base.BaseDatabaseWrapper.allow_thread_sharing
#  * django.db.utils.ConnectionHandler.thread_critical
#  * asgiref.Local
class ApiDatabaseTestCase(ApiCaseMixin, BaseCaseMixin, TransactionTestCase):
    """Any test that interacts with the database through the API"""

    fixtures = BaseCaseMixin.fixtures
