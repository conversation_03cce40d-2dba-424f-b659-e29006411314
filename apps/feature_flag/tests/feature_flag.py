import time
from unittest.mock import MagicMock

from django.test import TestCase, override_settings

from apps.feature_flag.feature_flag import (
    FeatureFlagService,
)
from factories.organization import OrganizationFactory


@override_settings(CONFIGCAT_SDK_KEY="a" * 22 + "/" + "b" * 22)
class FeatureFlagServiceTests(TestCase):
    def setUp(self):
        super().setUp()
        self.organization = OrganizationFactory.create()

    def test_returns_default_value_when_client_is_none(self):
        service = FeatureFlagService()
        service.flag_defaults["Feature_Flag_Test"] = False
        service.client = None
        result = service.get_value("Feature_Flag_Test", self.organization)
        self.assertFalse(result)

    def test_returns_flag_value_when_client_is_available(self):
        service = FeatureFlagService()
        service.flag_defaults["Feature_Flag_Test"] = False
        service.client = MagicMock()
        service.client.get_value = MagicMock(return_value=True)
        result = service.get_value("Feature_Flag_Test", self.organization)
        self.assertTrue(result)

    def test_returns_default_value_when_circuit_breaker_is_active(self):
        service = FeatureFlagService()
        service.flag_defaults["Feature_Flag_Test"] = False
        service.client.get_hooks().invoke_on_error("error")
        result = service.get_value("Feature_Flag_Test", self.organization)
        self.assertFalse(result)

    def test_resets_circuit_breaker_after_timeout(self):
        service = FeatureFlagService()
        service.flag_defaults["Feature_Flag_Test"] = False
        service.circuit_breaker_start = time.time() - 1000
        service.client = MagicMock()
        service.client.set_online = MagicMock()
        service.get_value("Feature_Flag_Test", self.organization)
        service.client.set_online.assert_called_once()
        self.assertIsNone(service.circuit_breaker_start)
