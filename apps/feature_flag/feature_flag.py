import logging
import time

import configcatclient
from configcatclient.user import User as ConfigCatUser
from django.conf import settings

logger = logging.getLogger(__name__)

CACHE_REFRESH_INTERVAL_SECONDS = 180
CIRCUIT_BREAKER_SECONDS = 300

# Feature flags registered in ConfigCat


class FeatureFlagService:
    flag_defaults = {}

    def __init__(self):
        self.circuit_breaker_start = None

        # Only create the client if an SDK key exists.
        # Creation of the ConfigCat client does not validate the SDK key so no
        # meaningful exceptions are thrown here.  While retrieving flag values, if
        # the SDK key is invalid, the client logs API errors and returns flag defaults.
        if settings.CONFIGCAT_SDK_KEY:
            self.client = configcatclient.get(
                settings.CONFIGCAT_SDK_KEY,
                configcatclient.ConfigCatOptions(
                    # The default polling mode operates on a different thread and does not work for Celery tasks.
                    # See the *caution* in https://configcat.com/docs/sdk-reference/python/#auto-polling-default.
                    polling_mode=configcatclient.PollingMode.lazy_load(
                        cache_refresh_interval_seconds=CACHE_REFRESH_INTERVAL_SECONDS
                    )
                ),
            )

            # The ConfigCat SDK has a (configurable) connection and read timeout.  If the TTL has expired, the SDK
            # will attempt to reconnect on every request until successful.  We need to circuit break this behavior
            # so there isn't a massive slowdown in critical sections of the code.  We do this by setting the client
            # to offline mode, which will cause the client to always return the cached values.
            def on_client_error(error):
                logger.exception("ConfigCat client error", extra={"error": error})
                self.circuit_breaker_start = time.time()
                self.client.set_offline()

            self.client.get_hooks().add_on_error(on_client_error)

        else:
            self.client = None

    def get_value(self, key, org, default=None):
        # Check if the circuit breaker is active and reset it if the time has elapsed.
        if self.circuit_breaker_start:
            elapsed = time.time() - self.circuit_breaker_start
            if elapsed > CIRCUIT_BREAKER_SECONDS:
                logger.info("ConfigCat client circuit breaker reset")
                self.client.set_online()
                self.circuit_breaker_start = None

        # This is a fake user that represents the organization, for cases where we want to target features
        # by org instead of by user. The "Organization" parameter is used for consistency when writing our
        # targeting rules.
        user = ConfigCatUser(
            org.alias,
            custom={
                "Organization": org.alias,
            },
        )

        default = default or self.flag_defaults.get(key)
        return (
            self.client.get_value(key, default_value=default, user=user)
            if self.client
            else default
        )


feature_flag_service = FeatureFlagService()
