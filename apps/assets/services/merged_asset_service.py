import logging

from apps.assets.models import AssetType, CoverageCategory, MergedAsset
from apps.assets.models.merged_asset import (
    MergedAssetSourcesMetadata,
    MergedAssetTechnologiesMetadata,
)
from apps.assets.services import coverage_exclusion_rules

logger = logging.getLogger(__name__)


class MergedAssetService:
    def rebuild_sources_metadata(self, merged_assets: list[MergedAsset]):
        for merged_asset in merged_assets:
            merged_asset.metadata.asset = self.build_sources_metadata(merged_asset)

    @staticmethod
    def build_sources_metadata(merged_asset: MergedAsset):
        def get_coverage(coverage: CoverageCategory):
            ids = merged_asset.source_data.technology_ids(coverage)
            return list(ids), ",".join(sorted(ids))

        endpoint_securities = get_coverage(CoverageCategory.ENDPOINT_SECURITY)
        backup_agents = get_coverage(CoverageCategory.BACKUP_AGENT)
        vul_mgnt = get_coverage(CoverageCategory.VULNERABILITY_MANAGEMENT)

        endpoint_security_excluded = (
            coverage_exclusion_rules.is_endpoint_security_excluded(merged_asset)
        )
        vul_mgnt_excluded = (
            coverage_exclusion_rules.is_vulnerability_management_excluded(merged_asset)
        )

        return MergedAssetSourcesMetadata(
            type=AssetType.HOST,
            technologies=MergedAssetTechnologiesMetadata(
                count=merged_asset.source_data.count(),
                all=list(merged_asset.source_data.technology_ids()),
                endpoint_security=endpoint_securities[0],
                all_combined=",".join(
                    sorted(merged_asset.source_data.technology_ids())
                ),
                endpoint_security_combined=endpoint_securities[1],
                vulnerability_management=vul_mgnt[0],
                vulnerability_management_combined=vul_mgnt[1],
                backup_agent=backup_agents[0],
                backup_agent_combined=backup_agents[1],
                full_coverage=MergedAssetSourcesMetadata.full_coverage(
                    merged_asset.source_data
                ),
                endpoint_security_excluded=endpoint_security_excluded,
                vulnerability_management_excluded=vul_mgnt_excluded,
            ),
            last_seen=merged_asset.source_data.max_last_seen(),
            last_seen_endpoint_security=merged_asset.source_data.max_last_seen(
                CoverageCategory.ENDPOINT_SECURITY
            ),
            last_seen_vulnerability_management=merged_asset.source_data.max_last_seen(
                CoverageCategory.VULNERABILITY_MANAGEMENT
            ),
        )


merged_asset_service = MergedAssetService()
