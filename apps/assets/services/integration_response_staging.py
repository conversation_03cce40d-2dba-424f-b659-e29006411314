from datetime import datetime
from io import BytesIO
from json import loads
from os import path

from django.conf import settings

from apps.fs.filesystem import use_fs


class IntegrationResponseStagingWriter:
    def __init__(self, staging_fs, key):
        self.staging_fs = staging_fs
        self.key = key

    def write_raw(self, raw: bytes):
        self.buffer.write(raw)

    def __enter__(self):
        self.buffer = BytesIO()
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self.buffer.seek(0)
        dirname = path.dirname(self.key)
        if not self.staging_fs.exists(dirname):
            self.staging_fs.makedirs(dirname)
        self.staging_fs.write_file(self.key, self.buffer)


class IntegrationResponseStagingReader:
    def __init__(self, staging_fs, key):
        self.staging_fs = staging_fs
        self.key = key

    def readlines(self):
        buffer = BytesIO()
        self.staging_fs.read_file(self.key, buffer)
        buffer.seek(0)
        for line in buffer:
            yield loads(line.decode("utf-8"))

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        pass


class IntegrationResponseStaging:
    """
    Service to manage reads/write from the specified storage type.
    """

    def __init__(self, file_system_url):
        self.staging_fs = use_fs(file_system_url)

    @staticmethod
    def build_key(organization_id, technology_id, integration_id, correlation_id):
        partitions = {"year": "%Y", "month": "%m", "day": "%d"}
        timestamp = datetime.now()
        key_parts = [
            f"organization_id={str(organization_id)}",
            f"technology_id={str(technology_id)}",
            f"integration_id={str(integration_id)}",
            *tuple(
                f"{key}={timestamp.strftime(value)}"
                for key, value in partitions.items()
            ),
            f"{correlation_id}.jsonl",
        ]

        key = path.join(*key_parts)
        return key

    def write_raw(self, key: str, raw: bytes):
        with self.get_writer(key) as writer:
            writer.write_raw(raw)

    def read_raw(self, key: str):
        with self.get_reader(key) as reader:
            return [line for line in reader.readlines()]

    def get_writer(self, key):
        return IntegrationResponseStagingWriter(self.staging_fs, key)

    def get_reader(self, key):
        return IntegrationResponseStagingReader(self.staging_fs, key)


integration_response_staging = IntegrationResponseStaging(
    settings.RAW_ASSET_FILE_SYSTEM_URL
)
