from apps.assets.models.organization_setting import OrganizationSetting
from apps.assets.models.setting import Setting, SettingKey


class PurgeAutomationSettings:
    def __init__(self):
        self.days = None
        self.source_deleted = False

    @staticmethod
    def for_organization(organization_id):
        settings = OrganizationSetting.objects.get_settings_list(
            organization_id=organization_id
        ).filter(category=Setting.Category.PURGE_AUTOMATION)

        purge_settings = PurgeAutomationSettings()
        for setting in settings:
            value = setting.overridden_value or setting.default_value
            if not value["enabled"]:
                continue

            if setting.key == SettingKey.LAST_SEEN_GTR_X_DAYS:
                purge_settings.days = value["days"]

            if setting.key == SettingKey.SOURCE_DELETED:
                purge_settings.source_deleted = value["enabled"]

        return purge_settings
