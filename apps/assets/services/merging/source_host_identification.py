import itertools
import logging
from collections import defaultdict
from uuid import uuid4

import networkx as nx
from django.utils import timezone

from apps.accounts.models import Organization
from apps.assets.models import (
    AssetType,
    IdentificationRule,
    IdentifierEntry,
    MergedAsset,
)
from apps.assets.models.merged_asset import MergedAssetFieldGroups
from apps.assets.models.source_host import (
    HostAttributes,
    MergedAssetMeta,
    SourceHost,
)

logger = logging.getLogger(__name__)


class IdentificationResult:
    def __init__(self, created_ids, updated_ids, deleted_ids):
        self.created_ids = created_ids
        self.updated_ids = updated_ids
        self.deleted_ids = deleted_ids
        self.source_hosts_updated = 0
        self.source_hosts_total = 0
        self.merged_assets_created = 0
        self.merged_assets_deleted = 0
        self.merged_asset_errors = None

    @property
    def total_merged_asset_ids(self):
        return len(self.created_ids) + len(self.updated_ids)


class SourceHostIdentification:
    """
    Service for identifying source hosts for merging based on identification rules.
    """

    def __init__(
        self,
        org: Organization,
        id_rule_entries: list[(IdentificationRule, IdentifierEntry)],
        excluded_field_values: dict[str, list[str]],
    ):
        """
        Initialize the service with the organization, identification rule entries,
        and excluded field values.

        :param org: The organization for which the service is being run.
        :param id_rule_entries: List of tuples containing identification rules and identifier entries.
        :param excluded_field_values: Dictionary of field names and their excluded values.
        """
        self.org = org
        self.id_values = IdentificationValues(id_rule_entries, excluded_field_values)

        # graph and lookup
        self.graph = nx.Graph()
        self.value_set_lookup = defaultdict(set)
        self.merged_asset_sources = defaultdict(set)
        self.created_times = {}
        self.manually_merged_ids = set()

        self.created_time = None
        self.result: IdentificationResult | None = None

    def update_merged_asset_ids(self):
        """
        Update the merged asset IDs for the source hosts in the organization.

        :return: A set of updated merged asset IDs.
        """
        # Populate the graph with value sets
        for host in SourceHost.documents.get_by_organization(self.org.id):
            self.__add_node(host)

        if not self.graph.number_of_nodes():
            # When there are no source hosts, we can delete all merged assets.
            self.__delete_all_merged_assets()
            return

        # Calculate the connected nodes
        self.__add_edges()
        # Determine the merged asset ids
        self.__calculate_merged_asset_ids()

        # Write the merged asset IDs back to the source hosts
        self.__write_source_hosts()

        # Write the created and deleted merged assets
        self.__create_delete_merged_assets()
        return self.result.created_ids + self.result.updated_ids

    def manually_merge(self, merged_asset_ids: list[str]):
        """
        Manually merge the source hosts with the given merged asset IDs.

        :param merged_asset_ids: The merged asset IDs to manually merge.
        :return: A set of updated merged asset IDs.
        """
        # Populate the graph with value sets
        for host in SourceHost.documents.get_by_merged_assets(merged_asset_ids):
            self.__add_node(host)

        # Add the selected merged asset ID to the manually merged set
        # so that the sources are updated with the manually merged flag.
        selected_id = merged_asset_ids[0]
        self.manually_merged_ids.add(selected_id)

        # Set the calculated merged asset ID manually (no calculation)
        for node, data in self.graph.nodes(data=True):
            data["calculated_merged_asset_id"] = selected_id

        self.result = self.__get_calculate_result()

        # Write the results
        self.__write_source_hosts()

        self.__delete_merged_assets(self.result.deleted_ids)

        # Return the updated merged asset IDs
        return self.result.updated_ids

    def manually_unmerge_preview(self, merged_asset_id: str):
        """
        Preview unmerge of the merged asset with the given ID.

        :param merged_asset_id: The merged asset ID to preview the unmerge for.
        :return: A list of source hosts that would be unmerged.
        """
        # Populate the graph with value sets and save for later
        source_hosts_by_loc = {}
        for host in SourceHost.documents.get_by_merged_assets([merged_asset_id]):
            source_hosts_by_loc[host.locator] = host
            self.__add_node(host)

        # Clear the manually merged set. This will ensure that the source hosts are
        # not marked as manually merged during calculate.
        self.manually_merged_ids.clear()

        # Calculate the connected nodes
        self.__add_edges()
        # Determine the merged asset ids
        self.__calculate_merged_asset_ids()

        source_hosts = []
        for source_host in self.__get_updates():
            host = source_hosts_by_loc[source_host.locator]

            # Populate the returned source hosts with
            # data needed to reconcile the merged asset.
            source_host.source_id = host.source_id
            source_host.created = host.created
            source_host.updated = host.updated
            source_host.attributes = host.attributes
            source_host.integration = host.integration
            source_hosts.append(source_host)

        return source_hosts

    def manually_unmerge(self, merged_asset_id: str):
        self.manually_unmerge_preview(merged_asset_id)

        self.__write_source_hosts()

        to_create = []
        for _id in self.result.created_ids:
            to_create.append(
                MergedAsset.create_shell(
                    self.org,
                    _id,
                    AssetType.HOST,
                    self.created_time,
                )
            )

        bulk_result = MergedAsset.documents.create_delete_bulk(
            assets_to_create=to_create,
            assets_to_delete=[],
            refresh="true",
        )

        self.result.merged_assets_created = bulk_result.created
        self.result.merged_asset_errors = bulk_result.errors
        return self.result.created_ids + self.result.updated_ids

    def __add_node(self, source):
        """
        Add a node to the graph for the given source host.

        :param source: The source host to add to the graph.
        """
        self.graph.add_node(
            source.locator,
            values_for_find=self.id_values.get_all_values_sets(source.attributes, True),
            merged_asset=source.merged_asset,
            calculated_merged_asset_id=None,
        )
        for vs in self.id_values.get_all_values_sets(source.attributes, False):
            self.value_set_lookup[vs].add(source.locator)

        if existing_ma := source.merged_asset:
            self.merged_asset_sources[existing_ma.id].add(source.locator)
            self.created_times[existing_ma.id] = existing_ma.created
            if existing_ma.manually_merged:
                self.manually_merged_ids.add(existing_ma.id)

    def __add_edges(self):
        """
        Add edges between nodes that have matching values. This will create a graph
        where connected components are merged assets.
        """
        edges_to_add = set()
        for node, data in self.graph.nodes(data=True):
            values_for_find = data["values_for_find"]
            for vs in values_for_find:
                for locator in self.value_set_lookup[vs]:
                    if locator == node:
                        continue
                    edges_to_add.add((node, locator))
            merged_asset = data["merged_asset"]
            if merged_asset and merged_asset.id in self.manually_merged_ids:
                for other in self.merged_asset_sources[merged_asset.id]:
                    if other == node:
                        continue
                    edges_to_add.add((node, other))
        if edges_to_add:
            self.graph.add_edges_from(edges_to_add)

    def __get_connected_nodes(self):
        """
        Get all connected components in the graph. Each connected component is a list
        of nodes that are connected by edges. These connected components are the source
        assets that should be merged together.

        :return: A generator yielding dictionaries of connected nodes.
        """
        for connected in nx.connected_components(self.graph):
            yield {n: self.graph.nodes[n] for n in connected}

    def __calculate_merged_asset_ids(self):
        """
        Calculate the merged asset IDs for the connected components in the graph.
        """
        taken_merged_asset_ids = set()
        for connected in self.__get_connected_nodes():
            connected_locators = set(connected.keys())
            old_connected = {
                m.id: self.merged_asset_sources[m.id]
                for n in connected.values()
                if (m := n["merged_asset"]) and m.id not in taken_merged_asset_ids
            }

            best = self.__best_merged_asset_id(old_connected, connected_locators)
            if not best:
                best = str(uuid4())

            taken_merged_asset_ids.add(best)
            for data in connected.values():
                data["calculated_merged_asset_id"] = best
                if merged_asset := data["merged_asset"]:
                    # If this node is part of a manually merged asset, we want to keep
                    # it that way. Add `best` to the manually_merged set.
                    if merged_asset.id in self.manually_merged_ids:
                        self.manually_merged_ids.add(best)

        self.result = self.__get_calculate_result()

    @staticmethod
    def __best_merged_asset_id(old_components, connected_component):
        """
        Determine the best merged asset ID for a connected component based on
        overlap and Jaccard similarity.

        :param old_components: Dictionary of old component IDs and their locators.
        :param connected_component: Set of locators in the new connected component.
        :return: The best merged asset ID or None if no suitable ID is found.
        """
        # Initialize variables to keep track of the best match
        best_overlap = 0
        best_uuid = None
        best_old_comp = None
        tie_best = None
        tie_old = None

        for old_uuid, old_comp in old_components.items():
            overlap = len(old_comp & connected_component)
            if overlap > best_overlap:
                # Found a better match
                best_overlap = overlap
                best_uuid = old_uuid
                best_old_comp = old_comp
            elif overlap == best_overlap and overlap > 0:
                # Tie-breaker: use Jaccard similarity
                # We want the merge helper to choose the uuid with the highest
                # Jaccard similarity when two old components have the save
                # intersection length. This is to ensure the other old component
                # remains available for another connected component.
                union_size_best = len(best_old_comp | connected_component)
                union_size_current = len(old_comp | connected_component)
                jaccard_best = best_overlap / union_size_best
                jaccard_current = overlap / union_size_current

                if jaccard_current > jaccard_best:
                    best_uuid = old_uuid
                    best_old_comp = old_comp
                elif jaccard_current == jaccard_best:
                    # Log a warning to see if this is happening.
                    # Maybe we should consider a deterministic tie-breaker.
                    tie_best = best_uuid
                    tie_old = old_uuid

        if tie_best and tie_best == best_uuid:
            logger.warning(f"Jaccard tie: {tie_best} vs {tie_old}")

        return best_uuid if best_overlap > 0 else None

    def __write_source_hosts(self):
        """
        Write the calculated merged asset IDs back to the source hosts.
        """

        self.created_time = timezone.now()

        _, updated, _ = SourceHost.documents.create_update_delete_bulk(
            create=[],
            update=self.__get_updates(),
            delete=[],
            fields=SourceHost.documents.get_update_merged_asset_meta_fields(),
        )
        self.result.source_hosts_updated = updated
        self.result.source_hosts_total = self.graph.number_of_nodes()

    def __get_updates(self):
        for node, data in self.graph.nodes(data=True):
            merged_asset_id = data["calculated_merged_asset_id"]
            manually_merged = merged_asset_id in self.manually_merged_ids
            created = self.created_times.get(merged_asset_id, self.created_time)
            if merged_asset := data["merged_asset"]:
                if merged_asset.id == merged_asset_id:
                    if merged_asset.manually_merged == manually_merged:
                        continue
            yield SourceHost(
                locator=node,
                merged_asset=MergedAssetMeta(
                    id=merged_asset_id,
                    created=created,
                    manually_merged=manually_merged,
                ),
            )

    def __get_calculate_result(self):
        """
        Get the updated merged asset IDs for the source hosts.

        :return: A IdentificationResult with created/updated/deleted merged asset IDs.
        """

        # We are not tracking which merged asset IDs were updated and deleted, so
        # we are returning all merged asset IDs involved in the calculation; both
        # the old and new ones.
        calculated = set()
        existing = set()
        for _, data in self.graph.nodes(data=True):
            if calculated_merged_asset_id := data["calculated_merged_asset_id"]:
                calculated.add(calculated_merged_asset_id)
            if merged_asset := data["merged_asset"]:
                existing.add(merged_asset.id)

        created_ids = list(calculated - existing)
        updated_ids = list(calculated & existing)
        deleted_ids = list(existing - calculated)
        return IdentificationResult(created_ids, updated_ids, deleted_ids)

    def __create_delete_merged_assets(self):
        """
        Create and delete merged assets based on the identification result.
        We must create/delete merged assets in a single bulk operation to
        ensure consistency.
        """

        # Start with all the merged asset IDs that were created or updated.
        ensure_created = set(self.result.created_ids + self.result.updated_ids)

        to_delete = []
        for ma in MergedAsset.documents.get_ids(self.org.id, AssetType.HOST):
            if ma.id in ensure_created:
                # Remove from ensure_created so we don't create it again.
                # Checking all documents makes the process self-healing.
                ensure_created.remove(ma.id)
            else:  # pragma: no cover
                # Three cases:
                # 1. The merged asset was deleted in the source host identification.
                # 2. The merged asset was deleted in source host updater.
                # 3. The merged asset was deleted and the task was interrupted.
                to_delete.append(ma)

        to_create = []
        for _id in ensure_created:
            to_create.append(
                MergedAsset.create_shell(
                    self.org,
                    _id,
                    AssetType.HOST,
                    self.created_time,
                )
            )

        bulk_result = MergedAsset.documents.create_delete_bulk(
            assets_to_create=to_create,
            assets_to_delete=to_delete,
            refresh="true",
            raise_on_exception=False,
        )

        self.result.merged_assets_created = bulk_result.created
        self.result.merged_assets_deleted = bulk_result.deleted
        self.result.merged_asset_errors = bulk_result.errors

    def __delete_merged_assets(self, merged_asset_ids):
        to_delete = MergedAsset.documents.get_by_ids(
            merged_asset_ids,
            fields=MergedAssetFieldGroups(includes=["id"]),
        )

        bulk_result = MergedAsset.documents.create_delete_bulk(
            assets_to_create=[],
            assets_to_delete=to_delete,
            refresh="true",
            raise_on_exception=False,
        )

        self.result.merged_assets_deleted = bulk_result.deleted
        self.result.merged_asset_errors = bulk_result.errors

    def __delete_all_merged_assets(self):
        """
        Delete all merged assets for the organization.
        """
        merged_assets = MergedAsset.documents.get_ids(self.org.id, AssetType.HOST)
        bulk_result = MergedAsset.documents.create_delete_bulk(
            assets_to_create=[],
            assets_to_delete=list(merged_assets),
            refresh="true",
            raise_on_exception=False,
        )

        self.result = IdentificationResult([], [], [])
        self.result.merged_assets_deleted = bulk_result.deleted
        self.result.merged_asset_errors = bulk_result.errors


class IdentificationValues:
    """
    Helper class for managing identification values and excluded field values.
    """

    def __init__(
        self,
        id_rule_entries: list[(IdentificationRule, IdentifierEntry)],
        excluded_field_values: dict[str, list[str]],
    ):
        """
        Initialize the helper with identification rule entries and excluded field values.

        :param id_rule_entries: List of tuples containing identification rules and identifier entries.
        :param excluded_field_values: Dictionary of field names and their excluded values.
        """
        self.id_rule_entries = id_rule_entries
        self.excluded_field_values = excluded_field_values

    def get_all_values_sets(self, attrs: HostAttributes, values_for_find):
        """
        Get all value sets for the given attributes.

        :param attrs: The host attributes.
        :param values_for_find: Boolean indicating whether to include values for finding assets.
        :return: A set of value sets.
        """
        return {
            (rule.id, entry.id, value_set)
            for rule, entry in self.id_rule_entries
            for value_set in self.get_value_sets(entry, attrs, values_for_find)
        }

    @staticmethod
    def get_nand_exists_values_for_entry(entry: IdentifierEntry, attrs):
        """
        Get NAND exists values for the given entry and attributes.

        :param entry: The identifier entry.
        :param attrs: The host attributes.
        :return: A list of boolean values indicating the existence of NAND fields.
        """
        return [bool(getattr(attrs, f)) for f in entry.nand_exists_fields]

    def get_value_sets(
        self,
        entry: IdentifierEntry,
        attrs,
        values_for_find,
    ):
        """
        Get a sequence of every possible combination of values for the given set
        of fields in the given attributes.

        :param entry: The identifier entry.
        :param attrs: The host attributes.
        :param values_for_find: Boolean indicating whether to include values for finding assets.
        :return: A generator yielding value sets.
        """
        values = self.get_values_for_entry(entry, attrs)
        if not all(values):
            return

        nan_exists_values = self.get_nand_exists_values_for_entry(entry, attrs)
        if values_for_find:
            # When finding assets, we want to include the proper values to satisfy the
            # nand (not-and) condition. If a field is empty, we want to include both True
            # and False values for the nand_exists_values.
            def get_values_for_find(value):
                if value is False:
                    return [True, False]
                if value is True:
                    return [False]

            nan_exists_values = [get_values_for_find(v) for v in nan_exists_values]

        value_lists = [
            v if isinstance(v, list) else [v] for v in values + nan_exists_values
        ]
        yield from itertools.product(*value_lists)

    def get_values_for_entry(self, entry: IdentifierEntry, attrs):
        """
        Get values for the given entry and attributes, excluding specified values.

        :param entry: The identifier entry.
        :param attrs: The host attributes.
        :return: A list of values for the entry.
        """

        def not_excluded(field, value):
            for excluded in self.excluded_field_values.get(field, []):
                if value.startswith(excluded):
                    return False
            return True

        values = []

        for f in entry.fields:
            v = getattr(attrs, f)

            if isinstance(v, list):
                if f in self.excluded_field_values:
                    values.append([x for x in v if not_excluded(f, x)])
                else:
                    values.append(v)
            else:
                values.append(v if not_excluded(f, v) else None)

        return values
