import logging
import time
from itertools import chain
from typing import Callable

from ata_common.utils.advisory_lock import advisory_lock

from apps.accounts.models import Organization
from apps.assets.merging_rules.reconciliation.rules import (
    reconciliation_rules as static_reconciliation_rules,
)
from apps.assets.models import (
    AssetField,
    IdentificationRule,
    MergedAsset,
    ReconciliationRule,
)
from apps.assets.models.merged_asset import MergedAssetFieldGroups
from apps.assets.models.merged_source_asset import (
    AssetAttributes,
    AssetType,
)
from apps.assets.models.source_host import SourceHost
from apps.assets.services.criticality_settings import AssetCriticalitySettings
from apps.assets.services.merged_asset_service import merged_asset_service
from apps.assets.services.purge_automation_settings import (
    PurgeAutomationSettings,
)
from apps.integrations.converter import SourceAssetConverter
from apps.integrations.models import Integration
from apps.integrations.utils import get_external_technology_ids

from .merged_asset_reconciliation import MergedAssetReconciliation
from .source_host_identification import SourceHostIdentification
from .source_host_updater import SourceHostUpdater

logger = logging.getLogger(__name__)


class MergeService:
    class LockError(Exception):
        pass

    def merge_staged_assets(
        self,
        integration: Integration,
        staged_assets: list,
        sync_type: str,
    ):
        org = integration.organization
        self.update_source_assets(integration, staged_assets, sync_type)
        merged_asset_ids = self.update_merged_asset_ids(org, sync_type)
        if not merged_asset_ids:
            return
        self.reconcile_merged_assets(org.id, merged_asset_ids)

    def update_source_assets(
        self,
        integration: Integration,
        staged_assets: list,
        sync_type: str,
    ):
        start = time.time()
        # Convert the sync assets
        converter = SourceAssetConverter.get_converter(sync_type)
        assets = converter.try_convert(integration, staged_assets)
        total_staged_assets = len(staged_assets)
        total_assets = len(assets)

        logger.info(
            f"Updating {len(assets)} of {total_assets} source assets ({total_staged_assets} staged assets) "
            f"for {integration}",
            extra=integration.log_extra(),
        )

        # Lock the operation to load, merge, and update assets for the
        # current organization and asset type to avoid concurrency issues.
        asset_type = converter.asset_type
        org = integration.organization
        lock_key = self._asset_modify_lock_key(org, asset_type)
        with advisory_lock(lock_key, wait=False) as acquired:
            if not acquired:
                msg = f"Failed to acquire lock for {integration} and {asset_type}. Retrying later."
                logger.warning(msg)
                raise self.LockError()

            self._update_source_assets(org, integration, assets)

            logger.info(
                f"Updated source assets for {org} in {time.time() - start:.3f} seconds",
                extra=org.log_extra(),
            )

    def update_merged_asset_ids(
        self,
        org: Organization,
        sync_type: str,
    ):
        start = time.time()

        converter = SourceAssetConverter.get_converter(sync_type)
        asset_type = converter.asset_type

        # Lock the operation to load, merge, and update assets for the
        # current organization and asset type to avoid concurrency issues.
        lock_key = self._asset_modify_lock_key(org, asset_type)
        with advisory_lock(lock_key, wait=False) as acquired:
            if not acquired:
                msg = f"Failed to acquire lock for {org} and {asset_type}. Retrying later."
                logger.warning(msg)
                raise self.LockError()

            merged_asset_ids = self._update_merged_asset_ids(org, asset_type)
            logger.info(
                f"Updated merged asset ids for {org} in {time.time() - start:.3f} seconds",
                extra=org.log_extra(),
            )
            return merged_asset_ids

    def reconcile_merged_assets(self, organization_id, merged_asset_ids: list):
        org = Organization.objects.get(id=organization_id)

        rec_service = self._get_rec_service(org, AssetType.HOST)
        result = rec_service.reconcile_merged_assets(merged_asset_ids)
        logger.info(f"Reconciled {result.updated} merged assets", extra=org.log_extra())
        if result.errors:  # pragma: no cover
            logger.warning(
                f"Errors occurred while reconciling merged assets. errors: {result.errors}",
                extra=org.log_extra(),
            )
        return result

    def remove_sources_for_integration(
        self,
        integration: Integration,
        asset_type: AssetType,
    ):
        lock_key = self._asset_modify_lock_key(integration.organization, asset_type)
        with advisory_lock(lock_key):
            org = integration.organization
            SourceHost.documents.delete_by_integration(integration.id)
            return self._update_merged_asset_ids(org, asset_type)

    def manually_merge_preview(self, org, merged_asset_ids: list[str]):
        rec_service = self._get_rec_service(org, AssetType.HOST)
        return rec_service.manually_merge_preview(merged_asset_ids)

    def manually_merge(self, org, merged_asset_ids: list[str]):
        lock_key = self._asset_modify_lock_key(org, AssetType.HOST)
        with advisory_lock(lock_key, wait=False) as acquired:
            if not acquired:
                msg = f"Failed to acquire lock for {org} and {AssetType.HOST}. Retrying later."
                logger.warning(msg)
                raise self.LockError()
            identification = self._get_identification_service(org, AssetType.HOST)
            merged_asset_ids_for_rec = identification.manually_merge(merged_asset_ids)

            result = identification.result
            logger.info(
                f"Manually Merge {len(merged_asset_ids)} merged asset ids "
                f"(deleted:{result.merged_assets_deleted}) "
                f"for {org}",
                extra=org.log_extra(),
            )
            if result.merged_asset_errors:  # pragma: no cover
                logger.warning(
                    f"Errors occurred while identifying merged asset ids for {org}. errors: {result.merged_asset_errors}",
                    extra=org.log_extra(),
                )

        rec_service = self._get_rec_service(org, AssetType.HOST)
        rec_service.reconcile_merged_assets(merged_asset_ids_for_rec)
        return rec_service.processed[0]

    def manually_unmerge_preview(self, org, merged_asset_id: str):
        identification = self._get_identification_service(org, AssetType.HOST)
        source_hosts = identification.manually_unmerge_preview(merged_asset_id)

        rec_service = self._get_rec_service(org, AssetType.HOST)
        return rec_service.manually_unmerge_preview(source_hosts)

    def manually_unmerge(self, org, merged_asset_id: str):
        lock_key = self._asset_modify_lock_key(org, AssetType.HOST)
        with advisory_lock(lock_key, wait=False) as acquired:
            if not acquired:
                msg = f"Failed to acquire lock for {org} and {AssetType.HOST}. Retrying later."
                logger.warning(msg)
                raise self.LockError()
            identification = self._get_identification_service(org, AssetType.HOST)
            merged_asset_ids_for_rec = identification.manually_unmerge(merged_asset_id)

            result = identification.result
            logger.info(
                f"Manually Unmerge {merged_asset_id} "
                f"(create:{len(result.created_ids)} update:{len(result.updated_ids)}) "
                f"(created:{result.merged_assets_created}) "
                f"for {org}",
                extra=org.log_extra(),
            )
            if result.merged_asset_errors:  # pragma: no cover
                logger.warning(
                    f"Errors occurred while identifying merged asset ids for {org}. errors: {result.merged_asset_errors}",
                    extra=org.log_extra(),
                )

        rec_service = self._get_rec_service(org, AssetType.HOST)
        rec_service.reconcile_merged_assets(merged_asset_ids_for_rec)
        return rec_service.processed

    @staticmethod
    def _asset_modify_lock_key(org: Organization, asset_type: AssetType):
        return ".".join(("update_assets", str(org.id), asset_type))

    def _update_source_assets(self, org, integration, assets):
        start = time.time()

        purge_automation_settings = PurgeAutomationSettings.for_organization(org.id)
        updater = SourceHostUpdater(org, purge_automation_settings)
        updater.update_integration_source_assets(integration, assets)
        upsert, deleted = len(updater.upsert), len(updater.delete)

        logger.info(
            f"Updated {upsert + deleted} of {len(assets)} source assets (upserted:{upsert} deleted:{deleted}) "
            f"for {integration} in {time.time() - start:.3f} seconds",
            extra=integration.log_extra(),
        )

    def _update_merged_asset_ids(self, org, asset_type):
        start = time.time()

        identification = self._get_identification_service(org, asset_type)
        merged_asset_ids = identification.update_merged_asset_ids()

        result = identification.result
        logger.info(
            f"Identified {result.total_merged_asset_ids} merged asset ids "
            f"(create:{len(result.created_ids)} delete:{len(result.deleted_ids)}) "
            f"(created:{result.merged_assets_created} deleted:{result.merged_assets_deleted}) "
            f"Source assets (total:{result.source_hosts_total} updated:{result.source_hosts_updated}) "
            f"for {org} in {time.time() - start:.3f} seconds",
            extra=org.log_extra(),
        )
        if result.merged_asset_errors:  # pragma: no cover
            logger.warning(
                f"Errors occurred while identifying merged asset ids for {org}. errors: {result.merged_asset_errors}",
                extra=org.log_extra(),
            )

        return merged_asset_ids

    def _get_identification_service(self, org, asset_type):
        rule_entries = self.get_id_rule_entries(org, asset_type)
        excluded_field_values = AssetField.objects.get_excluded_values(asset_type)

        return SourceHostIdentification(
            org,
            rule_entries,
            excluded_field_values,
        )

    def _get_rec_service(self, org, asset_type):
        rule_entries = self.get_rec_rule_entries(org, asset_type)
        criticality_settings = AssetCriticalitySettings.for_organization(org.id)

        return MergedAssetReconciliation(org, rule_entries, criticality_settings)

    @staticmethod
    def get_id_rule_entries(
        organization: Organization,
        asset_type: AssetType,
    ):
        identification_rules = IdentificationRule.objects.filter(asset_type=asset_type)

        org_rule = identification_rules.filter(organization=organization)
        # if no org specific rule, use global rules
        if not org_rule.exists():
            org_rule = identification_rules.filter(organization=None)

        identification_rule = org_rule.get()

        # Return a list of tuples of (rule, entry) for each rule and entry.
        return [
            (identification_rule, entry) for entry in identification_rule.entries.all()
        ]

    @staticmethod
    def get_rec_rule_entries(
        organization: Organization,
        asset_type: AssetType,
    ):
        organization_rules = ReconciliationRule.objects.filter(
            asset_type=asset_type,
            organization=organization,
        )
        global_rules = ReconciliationRule.objects.filter(
            asset_type=asset_type,
            organization=None,
        ).exclude(
            # Global Reconciliation Rules overridden by Org Rules can be excluded.
            field__in=organization_rules.values_list("field", flat=True),
        )

        reconciliation_rules = list(chain(organization_rules, global_rules))
        return [(rule, list(rule.entries.all())) for rule in reconciliation_rules]

    @staticmethod
    def _missing_rules_from(rules):
        misconfigured_fields = set()
        extra_fields = set()
        all_technologies = set(get_external_technology_ids())

        for asset_type in AssetType:
            if asset_type != AssetType.HOST:
                # We only support hosts for now
                continue

            attribute_fields = AssetAttributes.get_attributes_type(
                asset_type
            ).get_members()
            for field in attribute_fields:
                field_entries = rules[asset_type].get(field, {})
                if set(field_entries) != all_technologies:
                    misconfigured_fields.add((asset_type, field))

            for field in rules[asset_type]:
                if field not in attribute_fields:
                    extra_fields.add((asset_type, field))

        return misconfigured_fields, extra_fields

    def missing_rules_from_code(self):
        rules_from_code = static_reconciliation_rules.rules
        return self._missing_rules_from(rules_from_code)

    def missing_rules_from_db(self):
        rules_from_db = static_reconciliation_rules.from_models(
            ReconciliationRule.objects.global_rules()
        ).rules
        return self._missing_rules_from(rules_from_db)

    def verify_rules(self):
        misconfigured_fields_code, _ = self.missing_rules_from_code()
        misconfigured_fields_db, _ = self.missing_rules_from_db()

        if misconfigured_fields_code or misconfigured_fields_db:
            raise RuntimeError("Merging rules are not properly configured.")

    def rebuild_metadata(self, org: Organization, asset_type: AssetType):
        assets = MergedAsset.documents.get_by_org_asset_type(org.id, asset_type)
        assets = list(assets)
        merged_asset_service.rebuild_sources_metadata(assets)
        updated = MergedAsset.documents.update_bulk(
            assets, MergedAssetFieldGroups.metadata_fields()
        )
        logger.info(f"Updated metadata for {len(updated)} merged assets")

    def rebuild_data(
        self,
        org: Organization,
        asset_type: AssetType,
        assets: list[MergedAsset] | None = None,
    ):
        if not assets:
            assets = MergedAsset.documents.get_ids(org.id, asset_type)
            assets = list(assets)

        merged_asset_ids = [asset.id for asset in assets]
        self.reconcile_merged_assets(org.id, merged_asset_ids)

    def realign_source_assets_by_id_rules(
        self,
        org: Organization,
        asset_type: AssetType,
        mutator: Callable[[SourceHost], SourceHost] = None,
    ):
        lock_key = self._asset_modify_lock_key(org, asset_type)
        with advisory_lock(lock_key):
            if mutator:
                integrations = Integration.objects.filter(organization=org)
                for integration in integrations:
                    gen = SourceHost.documents.get_by_integration(integration.id)
                    source_hosts = [mutator(source_host) for source_host in gen]
                    self._update_source_assets(org, integration, source_hosts)

            merged_asset_ids = self._update_merged_asset_ids(org, asset_type)
            if not merged_asset_ids:
                return
            self.reconcile_merged_assets(org.id, merged_asset_ids)


merge_service = MergeService()
