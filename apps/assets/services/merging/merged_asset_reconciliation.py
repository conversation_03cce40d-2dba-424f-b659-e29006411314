import logging
from collections import defaultdict
from copy import deepcopy

from apps.accounts.models import Organization
from apps.assets.models import (
    AssetType,
    HostAssetAttributes,
    MergedAsset,
    MergedSourceAsset,
    ReconcilerEntry,
    ReconciliationRule,
    SourceAssetDetailMetadata,
    SourceAssetIntegrationMetadata,
    SourceAssetMetadata,
)
from apps.assets.models.source_host import (
    SourceHost,
)
from apps.assets.services.criticality_settings import AssetCriticalitySettings
from apps.assets.services.merged_asset_service import merged_asset_service

logger = logging.getLogger(__name__)

# Define a constant for an unset value
_UNSET = object()


class MergedAssetReconciliation:
    def __init__(
        self,
        org: Organization,
        rec_rule_entries: list[(ReconciliationRule, ReconcilerEntry)],
        criticality_settings: AssetCriticalitySettings,
    ):
        self.org = org
        self.rec_rule_entries = rec_rule_entries
        self.criticality_settings = criticality_settings

        self.replaces = []
        self.processed = []
        self.bulk_result = None

    def reconcile_merged_assets(self, merged_asset_ids: list[str] | set[str]):
        merged_asset_ids = list(merged_asset_ids)
        merged_assets = {
            ma.id: ma for ma in MergedAsset.documents.get_by_ids(merged_asset_ids)
        }
        hosts_by_id = defaultdict(list)
        for host in SourceHost.documents.get_by_merged_assets(merged_asset_ids):
            hosts_by_id[host.merged_asset.id].append(host)

        for merged_asset_id in merged_asset_ids:
            hosts = hosts_by_id[merged_asset_id]
            manual_merged = hosts[0].merged_asset.manually_merged if hosts else False
            source_assets = [self.__convert_to_source_asset(host) for host in hosts]
            self.__reconcile_merged_asset(
                merged_assets.get(merged_asset_id),
                source_assets,
                merged_asset_id,
                manual_merged,
            )

        self.__flush()
        return self.bulk_result

    def manually_merge_preview(self, merged_asset_ids: list[str]):
        selected_id = merged_asset_ids[0]
        ma = MergedAsset.documents.get_by_id(selected_id)
        source_assets = [
            self.__convert_to_source_asset(host)
            for host in SourceHost.documents.get_by_merged_assets(merged_asset_ids)
        ]
        return self.__reconcile_merged_asset(ma, source_assets, selected_id, True)

    def manually_unmerge_preview(self, hosts: list[SourceHost]):
        source_assets_by_merged_asset = defaultdict(list)
        created_lookup = {}
        for host in hosts:
            source_asset = self.__convert_to_source_asset(host)
            source_assets_by_merged_asset[host.merged_asset.id].append(source_asset)
            created_lookup[host.merged_asset.id] = host.merged_asset.created

        merge_assets = []
        for merged_asset_id, source_assets in source_assets_by_merged_asset.items():
            ma = MergedAsset.create_shell(
                self.org,
                merged_asset_id,
                AssetType.HOST,
                created_lookup[merged_asset_id],
            )
            ma = self.__reconcile_merged_asset(
                ma, source_assets, merged_asset_id, False
            )
            merge_assets.append(ma)

        return merge_assets

    def __reconcile_merged_asset(
        self,
        merged_asset: MergedAsset,
        hosts: list[MergedSourceAsset],
        merged_asset_id: str,
        manually_merged: bool,
    ):
        if not hosts:
            logger.warning(f"No hosts found for merged asset {merged_asset_id}")
            return

        if not merged_asset:
            logger.warning(f"Merged asset {merged_asset_id} not found")
            return

        # Replace the source data with the new hosts
        # No need to check if the hosts are already in the merged asset
        merged_asset.source_data.clear()
        for host in hosts:
            merged_asset.source_data.add(host)
        self.replaces.append(merged_asset)

        merged_asset.metadata.manually_merged = manually_merged

        # Reconcile the merged asset attributes
        if merged_asset.source_data.count() == 1:
            s = next(iter(merged_asset.source_data.all()))
            merged_asset.merged_data = deepcopy(s.attributes)
        else:
            merged_asset.reconcile(self.rec_rule_entries)

        self.criticality_settings.assign_criticality(merged_asset)
        merged_asset_service.rebuild_sources_metadata([merged_asset])
        return merged_asset

    def __flush(self):
        # Write out all changes.
        result = MergedAsset.documents.replace_bulk(
            self.replaces,
            refresh="true",
            raise_on_exception=False,
        )

        self.bulk_result = result

        self.processed.extend(self.replaces)

        self.replaces.clear()

    @staticmethod
    def __convert_to_source_asset(host: SourceHost):
        return MergedSourceAsset(
            metadata=SourceAssetMetadata(
                integration=SourceAssetIntegrationMetadata(
                    id=host.integration.id,
                    technology_id=host.integration.technology_id,
                    category_id=host.integration.category_id,
                    vulnerability_coverage_mode=host.integration.vulnerability_coverage_mode,
                    endpoint_coverage_mode=host.integration.endpoint_coverage_mode,
                ),
                asset=SourceAssetDetailMetadata(
                    type=host.type,
                    last_seen=host.attributes.last_seen,
                    source_id=host.source_id,
                ),
            ),
            attributes=HostAssetAttributes(
                criticality=host.attributes.criticality,
                group_name=host.attributes.group_names,
                hostname=host.attributes.hostname,
                fqdn=host.attributes.fqdns,
                public_ip=host.attributes.public_ips,
                private_ip=host.attributes.private_ips,
                mac_address=[],
                universal_mac_address=host.attributes.universal_mac_addresses,
                local_mac_address=host.attributes.local_mac_addresses,
                internet_exposure=host.attributes.internet_exposure,
                os=host.attributes.os,
                owner=host.attributes.owners,
                aad_id=host.attributes.aad_id,
                primary_ip_address=host.attributes.primary_ip_address,
                primary_mac_address=host.attributes.primary_mac_address,
            ),
        )
