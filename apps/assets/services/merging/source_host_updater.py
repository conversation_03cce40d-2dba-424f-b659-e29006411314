from datetime import datetime, timedelta

from apps.accounts.models import Organization
from apps.assets.models.source_host import SourceHost
from apps.assets.services.purge_automation_settings import PurgeAutomationSettings


class SourceHostUpdater:
    def __init__(
        self,
        org: Organization,
        purge_automation_settings: PurgeAutomationSettings,
    ):
        self.org = org
        self.purge_automation_settings = purge_automation_settings

        self.delete = {}
        self.upsert = {}

        # Update only the fields that are fetched from the source.
        self.fields = SourceHost.documents.get_update_fetched_fields()

    def update_integration_source_assets(
        self,
        integration,
        fetched_assets: list[SourceHost],
    ):
        str_id = str(integration.id)
        assert all(a.integration.id == str_id for a in fetched_assets)
        self.__reset()

        existing_assets = {
            source.internal_id: source
            for source in SourceHost.documents.get_by_integration(integration.id)
        }

        for fetched in fetched_assets:
            existing = existing_assets.get(fetched.internal_id)

            # First check for older assets and purge
            if self.__try_purge_stale_source(existing, fetched):
                continue

            if existing:
                self.__update_asset(existing, fetched)
            else:
                self.__create_asset(fetched)
                existing_assets[fetched.internal_id] = fetched

        self.__try_purge_not_in_source(existing_assets, fetched_assets)

        self.write()

    def write(self):
        create = []
        update = []
        delete = []

        for source in self.upsert.values():
            if source.locator:
                update.append(source)
            else:
                create.append(source)

        for source in self.delete.values():
            if source.locator:
                delete.append(source)

        SourceHost.documents.create_update_delete_bulk(
            create=create,
            update=update,
            delete=delete,
            fields=self.fields,
        )

    def __reset(self):
        self.delete = {}
        self.upsert = {}

    def __update_asset(self, existing, update):
        if not self.__has_changed(existing, update):
            return

        existing.attributes = update.attributes
        existing.integration = update.integration
        existing.merged_asset = update.merged_asset
        self.upsert[existing.internal_id] = existing

    def __create_asset(self, source):
        self.upsert[source.internal_id] = source

    def __has_changed(self, existing, other: SourceHost):
        """Checks if asset has changed."""
        if other.integration != existing.integration:
            return True

        if other.attributes.last_seen == existing.attributes.last_seen:
            return other.attributes != existing.attributes

        last_seen_values = (other.attributes.last_seen, existing.attributes.last_seen)
        if not all(last_seen_values):
            return True

        return other.attributes.last_seen > existing.attributes.last_seen

    def __try_purge_stale_source(self, existing, fetched):
        """
        Purge this asset if it is older than the days set for the organization.
        """
        days = self.purge_automation_settings.days
        if days is None:
            return False

        if fetched.attributes.last_seen is None:
            return False

        current_time = datetime.now().date()
        delta = current_time - fetched.attributes.last_seen.date()
        if delta > timedelta(days=days):
            if existing:
                self.delete[existing.internal_id] = existing
            return True

        return False

    def __try_purge_not_in_source(self, existing_assets, fetched_assets):
        if not self.purge_automation_settings.source_deleted:
            return

        return self.__purge_not_in_source(existing_assets, fetched_assets)

    def __purge_not_in_source(self, existing_assets, fetched_assets):
        """
        Purge existing assets that were not returned by the source.
        """
        existing_ids = set(existing_assets.keys())
        fetched_ids = set(a.internal_id for a in fetched_assets)
        ids_not_fetched = existing_ids - fetched_ids
        for id_ in ids_not_fetched:
            self.delete[id_] = existing_assets[id_]
        return True
