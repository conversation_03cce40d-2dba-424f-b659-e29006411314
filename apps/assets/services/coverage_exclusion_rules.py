from apps.assets.models import (
    AssetType,
    HostType,
    MergedAsset,
    MergedSourceAsset,
)


class CoverageExclusionRules:
    SUPPORTED_HOST_TYPES = [HostType.WORKSTATION, HostType.SERVER]

    def is_endpoint_security_excluded(self, merged_asset: MergedAsset):
        return all(
            self.__is_endpoint_security_excluded(source_asset)
            for source_asset in merged_asset.source_data.all()
        )

    def __is_endpoint_security_excluded(self, source_asset: MergedSourceAsset):
        if source_asset.metadata.asset.type == AssetType.HOST:
            host_attrs = source_asset.attributes
            if not host_attrs.hostname:
                return True

            supported_host_types = self.SUPPORTED_HOST_TYPES
            if host_attrs.os.host_type not in supported_host_types:
                return True

            # we may want to eventually prevent these from even being returned by the
            # connector but for now we will use the operating system name to at
            # least filter out some obvious "non-host" assets
            if host_attrs.os.name:
                lower_os_name = host_attrs.os.name.lower()
                if "printer" in lower_os_name or "camera" in lower_os_name:
                    return True

        return False

    def is_vulnerability_management_excluded(self, merged_asset: MergedAsset):
        return False


coverage_exclusion_rules = CoverageExclusionRules()
