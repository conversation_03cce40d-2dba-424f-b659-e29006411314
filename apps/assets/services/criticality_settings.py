from apps.assets.models import OrganizationSetting, Setting
from apps.assets.models.merged_asset import MergedAsset
from apps.assets.models.merged_source_asset import AssetCriticality, HostType, OsFamily
from apps.assets.models.setting import SettingKey


class AssetCriticalitySettings:
    def __init__(self):
        self.linux_criticality = None
        self.linux_major_server_distros = []

        self.windows_server_criticality = None
        self.windows_workstation_criticality = None
        self.mac_os_criticality = None

    @staticmethod
    def for_organization(organization_id):
        settings = OrganizationSetting.objects.get_settings_list(
            organization_id=organization_id
        ).filter(category=Setting.Category.ASSET_CRITICALITY)

        ac_settings = AssetCriticalitySettings()
        for setting in settings:
            value = setting.overridden_value or setting.default_value
            if not value["enabled"]:
                continue

            if setting.key == SettingKey.OS_LINUX_DISTROS:
                ac_settings.linux_criticality = value["criticality"]
                ac_settings.linux_major_server_distros = value["distros"]
            elif setting.key == SettingKey.OS_WINDOWS_SERVER:
                ac_settings.windows_server_criticality = value["criticality"]
            elif setting.key == SettingKey.OS_WINDOWS_WORKSTATION:
                ac_settings.windows_workstation_criticality = value["criticality"]
            elif setting.key == SettingKey.OS_MAC:
                ac_settings.mac_os_criticality = value["criticality"]

        return ac_settings

    def assign_criticality(self, merged_asset: MergedAsset):
        if merged_asset.source_data.has_criticality():
            return

        os = merged_asset.merged_data.os

        def set_criticality(criticality):
            if criticality is not None:
                current_criticality = merged_asset.merged_data.criticality
                if current_criticality == criticality:
                    return False  # criticality is unchanged
                merged_asset.merged_data.criticality = criticality
                return True

        if os.family == OsFamily.LINUX:
            for distro in self.linux_major_server_distros:
                if os.name and distro.lower() in os.name.lower():
                    return set_criticality(self.linux_criticality)
        elif os.family == OsFamily.WINDOWS and os.host_type == HostType.SERVER:
            return set_criticality(self.windows_server_criticality)
        elif os.family == OsFamily.WINDOWS and os.host_type == HostType.WORKSTATION:
            return set_criticality(self.windows_workstation_criticality)
        elif os.family == OsFamily.MAC:
            return set_criticality(self.mac_os_criticality)
        else:
            # Revert to unknown criticality.
            return set_criticality(AssetCriticality.UNKNOWN)
