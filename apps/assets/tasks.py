import json
import logging
from io import BytesIO
from uuid import UUID

from ata_common.chunking import chunks
from celery import shared_task

from apps.accounts.models import Account, Organization
from apps.assets.models import (
    AssetField,
    AssetType,
    HostAssetAttributes,
    MergedAsset,
    SyncTask,
)
from apps.assets.services import (
    integration_response_staging,
    merge_service,
)
from apps.fs.filesystem import use_fs
from apps.integrations.api import IntegrationSchema, data_connectors_api
from apps.integrations.models import Integration, Technology
from apps.service_bus.v1.schemas import (
    IntegrationAction,
    IntegrationInvokeActionCommand,
)
from core.celery import BaseTask

logger = logging.getLogger(__name__)


@shared_task
def invoke_fetch_assets():
    """
    Invoke the api method on all supported integration actions.
    """

    merge_service.verify_rules()

    integrations = data_connectors_api.list_integrations()
    for integration in integrations:
        _sync_integration(integration)


def _sync_integration(integration: IntegrationSchema):
    org = ensure_organization_exists(integration)
    ensure_integration_exists(integration, org)
    if not integration.enabled:
        return

    for asset_type in get_asset_types_from_actions(integration):
        invoke_command = IntegrationInvokeActionCommand(
            integration_id=integration.id,
            action=IntegrationAction.HOST_SYNC,
        )

        from apps.service_bus.v1.handlers import COMMAND_INTEGRATION_INVOKE_ACTION
        from core.service_bus import service_bus

        correlation_id = service_bus.send_command(
            COMMAND_INTEGRATION_INVOKE_ACTION,
            invoke_command,
        )

        SyncTask.objects.start_task(correlation_id, integration.id, asset_type)


def ensure_organization_exists(integration: IntegrationSchema):
    """
    Ensure that an Organization exists in the database.
    TODO: Remove this once we have a better way to create Organizations.
    """

    org = Organization.objects.filter(id=integration.organization_id).first()
    if org:
        return org

    mock_account_alias = "mock_account"
    mock_account, _ = Account.objects.get_or_create(alias=mock_account_alias)
    return Organization.objects.create(
        id=integration.organization_id,
        account=mock_account,
    )


def ensure_integration_exists(
    integration: IntegrationSchema,
    org: Organization,
):
    """
    Ensure that a Integration exists in the database.
    TODO: Remove this once we have a better way to create Integrations.
    """

    # Ensure Technology exists first
    technology, _ = Technology.objects.get_or_create(
        technology_id=integration.technology_id,
        defaults={
            "name": integration.technology_name,
        }
    )

    Integration.objects.update_or_create(
        id=integration.id,
        defaults={
            "enabled": integration.enabled,
            "technology": technology,
            "category_id": integration.category_id,
            "name": integration.name,
            "organization": org,
            "vulnerability_coverage_mode": integration.vulnerability_coverage_mode,
            "endpoint_coverage_mode": integration.endpoint_coverage_mode,
        },
    )


def get_asset_types_from_actions(integration: IntegrationSchema):
    if "host_sync" in integration.enabled_actions:
        yield AssetType.HOST


class SyncBaseTask(BaseTask):
    SUCCESS_STATUS = None

    def on_success(self, retval, task_id, args, kwargs):
        super().on_success(retval, task_id, args, kwargs)
        if self.SUCCESS_STATUS is None:
            return
        correlation_id = kwargs["correlation_id"]
        SyncTask.objects.set_status(correlation_id, self.SUCCESS_STATUS)

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        super().on_failure(exc, task_id, args, kwargs, einfo)
        correlation_id = kwargs["correlation_id"]
        SyncTask.objects.set_status(correlation_id, SyncTask.Status.FAILED)


class StageIntegrationResponseTask(SyncBaseTask):
    SUCCESS_STATUS = SyncTask.Status.STAGED

    def on_success(self, retval, task_id, args, kwargs):
        correlation_id = kwargs["correlation_id"]
        key, return_type = retval

        if key is None:
            task = SyncTask.objects.set_status(correlation_id, SyncTask.Status.FAILED)
            logger.warning(
                f"No assets were fetched. response status:{return_type}",
                extra=task.log_extra,
            )
            return

        super().on_success(retval, task_id, args, kwargs)

        update_source_assets.delay(
            correlation_id=correlation_id,
            staging_key=key,
            return_type=return_type,
        )


class FetchFailedError(Exception):
    pass


def get_result_content(result_keys, file_system_url):
    fs = use_fs(file_system_url)
    result_content = bytearray()

    for key in result_keys:
        # Add line ending if not present
        if result_content and result_content[-1] != b"\n":
            result_content.extend(b"\n")

        buffer = BytesIO()
        fs.read_file(key, buffer)
        buffer.seek(0)
        object_data = buffer.read()
        result_content.extend(object_data)

    return result_content


@shared_task(base=StageIntegrationResponseTask)
def stage_integration_response(
    correlation_id: str = None,
    response: str = None,
):
    """
    Save the results of a fetch assets operation.
    """
    response = json.loads(response)
    response_status = response["status"]
    if response_status == "FAILURE":
        raise FetchFailedError()
    if response_status != "SUCCESS":
        return None, response_status

    artifact_keys = response.get("artifact_keys")
    artifact_filesystem_url = response.get("artifact_filesystem_url")

    result_content = get_result_content(artifact_keys, artifact_filesystem_url)

    task = SyncTask.objects.get(correlation_id=correlation_id)

    key = integration_response_staging.build_key(
        organization_id=task.integration.organization_id,
        technology_id=task.technology_id,
        integration_id=task.integration_id,
        correlation_id=correlation_id,
    )
    integration_response_staging.write_raw(key, result_content)

    return key, response.get("return_type")


@shared_task(base=StageIntegrationResponseTask)
def stage_integration_action_complete_response(
    correlation_id: str = None,
    integration_id: UUID = None,
    result_keys: list[str] = None,
    result_ids: list[str] = None,
    result_type: str = None,
    result_filesystem_url: str = None,
):
    integration = data_connectors_api.get_integration(integration_id)
    org = ensure_organization_exists(integration)
    ensure_integration_exists(integration, org)

    result_content = get_result_content(result_keys, result_filesystem_url)

    task = SyncTask.objects.start_task(correlation_id, integration_id, AssetType.HOST)

    key = integration_response_staging.build_key(
        organization_id=task.integration.organization_id,
        technology_id=task.technology_id,
        integration_id=task.integration_id,
        correlation_id=correlation_id,
    )
    integration_response_staging.write_raw(key, result_content)

    return key, result_type


class MergeAssetsTask(SyncBaseTask):
    SUCCESS_STATUS = SyncTask.Status.COMPLETED


@shared_task(
    base=SyncBaseTask,
    max_retries=None,
    default_retry_delay=60,
    autoretry_for=(merge_service.LockError,),
)
def update_source_assets(
    correlation_id: str = None,
    staging_key: str = None,
    return_type: str = None,
):
    """
    Read staged assets and merge them with existing assets.
    """

    logger.info(f"Merging assets from {staging_key}")

    task = SyncTask.objects.get(correlation_id=correlation_id)

    staged_assets = integration_response_staging.read_raw(staging_key)

    sync_type = return_type
    merge_service.update_source_assets(
        task.integration,
        staged_assets,
        sync_type,
    )

    update_merged_asset_ids.delay(
        correlation_id=correlation_id,
        organization_id=task.organization_id,
        sync_type=sync_type,
    )


@shared_task(
    base=MergeAssetsTask,
    max_retries=None,
    default_retry_delay=60,
    autoretry_for=(merge_service.LockError,),
)
def update_merged_asset_ids(
    correlation_id: str = None,
    organization_id: UUID = None,
    sync_type: str = None,
):
    org = Organization.objects.get(id=organization_id)
    merged_asset_ids = merge_service.update_merged_asset_ids(org, sync_type)
    if not merged_asset_ids:
        return

    for chunk in chunks(merged_asset_ids, 500):
        reconcile_merged_assets.delay(organization_id, chunk)


@shared_task
def reconcile_merged_assets(organization_id: UUID, merged_asset_ids: list[UUID]):
    result = merge_service.reconcile_merged_assets(organization_id, merged_asset_ids)
    if result and result.errors:
        merged_asset_ids = []
        for error in result.errors:
            err = next(iter(error.values()))
            if err.get("status") == 409:
                _id = err.get("_id")
                _index = err.get("_index")
                ma = MergedAsset.documents.get_by_document_id(_id, _index)
                if ma:
                    merged_asset_ids.append(ma.id)
            else:  # pragma: no cover
                raise Exception(f"Error reconciling merged assets: {error}")
        if merged_asset_ids:
            retry_reconcile_merged_assets.delay(organization_id, merged_asset_ids)


@shared_task
def retry_reconcile_merged_assets(organization_id: UUID, merged_asset_ids: list[UUID]):
    reconcile_merged_assets(organization_id, merged_asset_ids)


@shared_task
def delete_old_sync_tasks():
    SyncTask.objects.old_tasks().delete()


@shared_task
def recalculate_mac_address():
    excluded_values = AssetField.objects.get_excluded_values(AssetType.HOST)
    excluded_macs = excluded_values["universal_mac_addresses"]

    def update_mac_address(source_host):
        universal = source_host.attributes.universal_mac_addresses
        local = source_host.attributes.local_mac_addresses
        default_primary_mac = HostAssetAttributes.default_primary_mac_addr(
            universal, local, excluded_macs
        )
        source_host.attributes.primary_mac_address = default_primary_mac
        return source_host

    for org in Organization.objects.all():
        merge_service.realign_source_assets_by_id_rules(
            org, AssetType.HOST, update_mac_address
        )


@shared_task
def delete_assets_for_integration(integration_id: UUID):
    try:
        integration = Integration.objects.get(id=integration_id)
    except Integration.DoesNotExist:
        logger.info(f"Integration {integration_id} not found.  Nothing to delete.")
        return

    ids = merge_service.remove_sources_for_integration(integration, AssetType.HOST)
    if ids:
        reconcile_merged_assets.delay(integration.organization_id, ids)

    integration.delete()


@shared_task
def sync_integration(integration_id: UUID, force: bool = False):
    integration = data_connectors_api.get_integration(integration_id)
    if force or should_sync_integration(integration):
        _sync_integration(integration)


def should_sync_integration(integration: IntegrationSchema):
    try:
        local_copy = Integration.objects.get(id=integration.id)
        fields_to_compare = [
            "enabled",
            "vulnerability_coverage_mode",
            "endpoint_coverage_mode",
        ]
        # bypass the update if all fields are the same
        for field in fields_to_compare:
            if getattr(integration, field) != getattr(local_copy, field):
                return True
    except Integration.DoesNotExist:
        return True

    return False
