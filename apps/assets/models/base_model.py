from dataclasses import fields as dataclass_fields
from functools import cache
from typing import ClassVar

from pydantic import ConfigDict, TypeAdapter
from pydantic.dataclasses import dataclass
from pydantic.fields import FieldInfo


@dataclass(config=ConfigDict(validate_assignment=True))
class BaseModel:
    _type_adapter: ClassVar[TypeAdapter] = None

    @classmethod
    def get_fields(cls):
        return [
            f
            for f in dataclass_fields(cls)
            if not (isinstance(f.default, FieldInfo) and f.default.exclude)
        ]

    @classmethod
    @cache
    def get_members(cls):
        return [f.name for f in cls.get_fields()]


class ToDict:
    @staticmethod
    def dataclass(v):
        return v if v is None else v.to_dict()

    @staticmethod
    def str(v):
        return v if v is None else str(v)

    @staticmethod
    def bool(v):
        return v if v is None else v

    @staticmethod
    def datetime(v):
        return v if v is None else v

    @staticmethod
    def list_str(v):
        return v if v is None else list(v)

    @staticmethod
    def list_dataclass(v):
        return v if v is None else [x.to_dict() for x in v]
