import uuid
from uuid import UUID

from django.db import models
from django.db.models import OuterRef, QuerySet, Subquery

from apps.assets.audit_logs.organization_setting import (
    OrganizationSettingAuditLogManager,
)
from apps.assets.models import Setting


class OrganizationSettingManager(models.Manager):
    def get_settings_list(self, organization_id: UUID) -> QuerySet:
        settings = Setting.objects.annotate(
            overridden_value=Subquery(
                self.filter(
                    setting=OuterRef("pk"), organization_id=organization_id
                ).values("overridden_value")[:1]
            )
        )
        return settings


class OrganizationSetting(models.Model):
    id: UUID = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    organization = models.ForeignKey("accounts.Organization", on_delete=models.RESTRICT)
    setting = models.ForeignKey(Setting, on_delete=models.CASCADE)
    overridden_value = models.JSONField()

    audit_logs = OrganizationSettingAuditLogManager()

    objects = OrganizationSettingManager()

    class Meta:
        unique_together = ("organization", "setting")

    def clean(self) -> None:
        super().clean()
        self.overridden_value = Setting.validate_setting_value(
            self.setting.category, self.setting.key, self.overridden_value
        ).model_dump(mode="json")

    def save(self, *args, **kwargs):
        self.clean()
        return super().save(*args, **kwargs)

    def __str__(self):
        return f"Org:{self.organization} {self.setting}"
