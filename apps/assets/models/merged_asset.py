from __future__ import annotations

from collections.abc import Generator, Iterable
from copy import deepcopy
from datetime import datetime
from functools import cache
from itertools import chain
from typing import Any, Optional
from uuid import UUID, uuid4

from django.utils import timezone
from opensearchpy import NotFoundError, Search
from opensearchpy.helpers import scan as es_scan
from opensearchpy.helpers import streaming_bulk
from pydantic import Field, RootModel, field_validator, model_validator
from pydantic.dataclasses import dataclass
from pydantic_core import ArgsKwargs

from apps.accounts.models import Organization
from apps.api.v1.search import compile_simple_kql, simple_query
from apps.assets.audit_logs.merged_asset import MergedAssetAuditLogManager
from apps.assets.models import BaseModel, ReconcilerEntry, ReconciliationRule, ToDict
from apps.assets.models.merged_source_asset import (
    AssetAttributes,
    AssetCriticality,
    AssetFieldGroups,
    AssetType,
    CoverageCategory,
    MergedSourceAsset,
)
from apps.es import es_service
from apps.integrations.utils import get_all_technology_ids


@dataclass
class MergedAssetFieldGroups:
    """
    Merged asset groups of fields to read or write.
    """

    METADATA = "metadata"
    SOURCE_DATA = "source_data"
    MERGED_DATA = "merged_data"
    OVERRIDES = "overrides"

    includes: Optional[list[str]] = None
    excludes: Optional[list[str]] = None
    technologies: Optional[list[str]] = None
    asset_fields: Optional[AssetFieldGroups] = None

    @model_validator(mode="after")
    def validate_arguments(self):
        if self.SOURCE_DATA not in self.get_field_names():
            if self.asset_fields or self.technologies:
                raise ValueError(
                    f"Cannot include asset_fields or technologies "
                    f"without including {self.SOURCE_DATA}"
                )

        return self

    def get_field_names(self):
        """
        Get all non-excluded field names.
        """
        fields = self.includes or self.get_default_members()
        if self.excludes:
            fields = [f for f in fields if f not in self.excludes]

        return fields

    @staticmethod
    @cache
    def get_default_members():
        return MergedAsset.get_members()

    def to_es_query_source(self):
        """
        Convert to OpenSearch _source parameter.
        """
        field_names = self.get_field_names()
        source_fields = []
        for field in field_names:
            if field == self.SOURCE_DATA and (self.technologies or self.asset_fields):
                technologies = self.technologies or ["*"]
                asset_field_names = (
                    self.asset_fields.get_field_names() if self.asset_fields else None
                )
                for technology in technologies:
                    if asset_field_names:
                        for asset_field in asset_field_names:
                            source_fields.append(f"{field}.{technology}.{asset_field}")
                    else:
                        source_fields.append(f"{field}.{technology}")
            else:
                source_fields.append(field)

        return source_fields

    @staticmethod
    def technology_fields(technology_id: str = None) -> MergedAssetFieldGroups:
        return MergedAssetFieldGroups(
            excludes=[MergedAssetFieldGroups.OVERRIDES],
            technologies=[technology_id] if technology_id else None,
        )

    @staticmethod
    def metadata_fields(technology_id: str = None) -> MergedAssetFieldGroups:
        return MergedAssetFieldGroups(
            includes=[
                MergedAssetFieldGroups.METADATA,
                MergedAssetFieldGroups.SOURCE_DATA,
            ],
            technologies=[technology_id] if technology_id else None,
            # We must save all asset_fields now that we allow multiple sources
            # for each technology be ES replaces array data on update (no patching).
            # asset_fields=AssetFieldGroups(includes=[AssetFieldGroups.METADATA]),
        )

    @staticmethod
    def overrides_fields():
        return MergedAssetFieldGroups(includes=[MergedAssetFieldGroups.OVERRIDES])


class MergedAssetElasticSearchManager:
    client = es_service.es
    INDEX_WRITE = "merged_asset-current"
    INDEX_READ = "merged_asset-current"
    INDEX_BACKUP = "merged_asset-backup"

    REFRESH = "false"

    @classmethod
    def scan(
        cls, body: dict, index: str = None, include_shells=True, **kwargs
    ) -> Generator[MergedAsset]:
        if not include_shells:
            search = Search().update_from_dict(body)
            search = search.filter("exists", field="merged_data")
            body = search.to_dict()

        results = es_scan(
            cls.client,
            query=body,
            index=index or cls.INDEX_READ,
            **kwargs,
        )
        for doc in results:
            yield MergedAsset(locator=es_service.get_locator(doc), **doc["_source"])

    @classmethod
    def search(
        cls,
        body: dict,
        index: str = None,
        seq_no_primary_term=False,
        **kwargs,
    ) -> list[MergedAsset]:
        if seq_no_primary_term:
            kwargs["seq_no_primary_term"] = True

        response = cls.search_raw(
            index=index,
            body=body,
            **kwargs,
        )
        documents = response["hits"]["hits"]
        return [
            MergedAsset(
                locator=es_service.get_locator(doc),
                seq_no=es_service.get_seq_no(doc),
                primary_term=es_service.get_primary_term(doc),
                **doc["_source"],
            )
            for doc in documents
        ]

    @classmethod
    def search_raw(
        cls, body: dict, index: str = None, include_shells=True, **kwargs
    ) -> dict:
        if not include_shells:
            search = Search().update_from_dict(body)
            search = search.filter("exists", field="merged_data")
            body = search.to_dict()

        return cls.client.search(index=index or cls.INDEX_READ, body=body, **kwargs)

    @classmethod
    def text_query_to_body(cls, query: str) -> dict:
        body = cls.field_search_to_body(query)
        if body:
            return body

        # Fallback to simple query.
        # Append wildcard if not present to the beginning and end of the query.
        if not query.startswith("*"):
            query = f"*{query}"
        if not query.endswith("*"):
            query = f"{query}*"

        reserved_chars = {i: "\\" + chr(i) for i in b"+-=&&||><!(){}[]^~?:\\/ "}
        query = query.translate(reserved_chars)
        return simple_query(query, ["merged_data.*"])

    @classmethod
    def field_search_to_body(cls, query: str) -> dict:
        return compile_simple_kql(
            query,
            identifier_transformer=cls.identifier_transformer,
            in_clause_transformer=cls.in_clause_transformer,
        )

    @staticmethod
    def identifier_transformer(identifier: str) -> str:
        """
        Convert raw text query identifier to ES keyword
        * No type prefix = merged_data
          e.g. hostname -> merged_data.hostname.keyword
        * With type prefix = source_data
          e.g. sentinel_one.group_name -> merged_data.sentinel_one.attributes.keyword
        """
        parts = identifier.split(".")
        prefix = parts[0]
        if prefix in get_all_technology_ids() and len(parts) == 2:
            prefix, key = parts
            # keys are nested under attributes
            nested_path = ("attributes",)
            if key == "source_id":
                # source_id is nested under metadata.asset
                nested_path = ("metadata", "asset")
            return ".".join(("source_data", prefix, *nested_path, key, "keyword"))

        # 'id' exists at the root level of the document
        if identifier == "id":
            return "id.keyword"
        return ".".join(("merged_data", identifier, "keyword"))

    @staticmethod
    def in_clause_transformer(identifier: str, value: str):
        """
        Given an in clause identifier and value, return a transformed identifier and value.
        * No type prefix = identifier, value
            e.g. criticality in (tier1) -> criticality, tier1
        * With type prefix = prefix.identifier, value
            e.g. source_id in (sentinel_one:123) -> sentinel_one.source_id, 123
        * With special `identifier` clause = technology_id.source_id, 123 OR aad_id, 123
            e.g. identifier in (sentinel_one:123) -> sentinel_one.source_id, 123 OR identifier in (aad_id:123) -> aad_id, 123
        """
        parts = value.split(":")
        prefix = parts[0]

        # Treat 'identifier' as a special keyword that will convert to either
        # source_id or aad_id depending on the prefix
        if identifier == "identifier":
            if prefix == "aad_id":
                identifier = "aad_id"
            else:
                identifier = "source_id"
        if prefix in get_all_technology_ids():
            identifier = f"{prefix}.{identifier}"
        if len(parts) == 2:
            value = parts[1]

        return identifier, value

    @classmethod
    def get_by_locator(cls, locator: str) -> Optional[MergedAsset]:
        """
        Get an asset by its locator.
        """
        _index, _type, _id = es_service.decode_locator(locator)
        if _index and _type and _id:
            return cls.get_by_document_id(_id, _index, _type)
        return None

    @classmethod
    def get_by_document_id(
        cls,
        _id: str,
        _index: str = None,
        _type: str = None,
    ) -> Optional[MergedAsset]:
        try:
            document = cls.client.get(_index or cls.INDEX_READ, _id)
        except NotFoundError:
            return None
        else:
            locator = es_service.get_locator(document)
            return MergedAsset(locator=locator, **document["_source"])

    @classmethod
    def get_by_id(cls, _id: str) -> Optional[MergedAsset]:
        query = {"query": {"bool": {"filter": [{"term": {"id.keyword": _id}}]}}}
        result = cls.search(query, index=cls.INDEX_READ, seq_no_primary_term=True)

        if not result:
            return None

        assert len(result) == 1, "Multiple merged assets with the same ID"
        return result[0]

    @classmethod
    def get_by_ids(
        cls,
        ids: list[str],
        fields: MergedAssetFieldGroups = None,
    ) -> list[MergedAsset]:
        query = {
            "size": len(ids),
            "query": {"bool": {"filter": [{"terms": {"id": ids}}]}},
        }
        if fields:  # pragma: no cover
            query["_source"] = fields.to_es_query_source()

        return cls.search(query, index=cls.INDEX_READ, seq_no_primary_term=True)

    def get_ids(self, org_id: UUID, asset_type: str):
        fields = MergedAssetFieldGroups(includes=["id"])
        return self.get_by_org_asset_type(org_id, asset_type, fields=fields)

    def get_by_org_asset_type(
        self,
        organization_id: UUID,
        asset_type: str,
        fields: MergedAssetFieldGroups = None,
    ) -> Generator[MergedAsset]:
        query = {
            "query": {
                "bool": {
                    "filter": [
                        {
                            "bool": {
                                "must": [
                                    {
                                        "term": {
                                            "metadata.organization_id": str(
                                                organization_id
                                            )
                                        }
                                    },
                                    {"term": {"metadata.asset.type": asset_type}},
                                ],
                            }
                        }
                    ]
                }
            },
        }

        if fields:
            query["_source"] = fields.to_es_query_source()

        return self.scan(query)

    @classmethod
    def get_duplicate_field_counts(
        cls,
        organization_id: UUID,
        field: str,
    ) -> dict[str, int]:
        """
        Get counts of duplicate field values for merged assets by a specific field.
        """
        org_filter = cls._generate_org_filter_for_duplicate_search(
            organization_id, field
        )
        query = {
            "query": org_filter,
            "aggs": {
                "duplicate_field_values": {
                    "terms": {
                        "field": f"merged_data.{field}.keyword",
                        "min_doc_count": 2,
                        "size": 10000,  # Adjust size as needed
                    },
                }
            },
            "size": 0,  # set to 0 to only return the aggs
        }

        response = cls.client.search(index=cls.INDEX_READ, body=query)

        # extract duplicate values and their counts
        buckets = response["aggregations"]["duplicate_field_values"]["buckets"]
        return {bucket["key"]: int(bucket["doc_count"]) for bucket in buckets}

    @classmethod
    def get_duplicates_by_field(
        cls,
        organization_id: UUID,
        field: str,
    ) -> dict[str, list[MergedAsset]]:
        """
        Get duplicates of merged assets by a specific field.
        """
        duplicate_field_counts = cls.get_duplicate_field_counts(organization_id, field)
        duplicated_values = list(duplicate_field_counts.keys())

        if not duplicated_values:
            return {}

        org_filter = cls._generate_org_filter_for_duplicate_search(
            organization_id, field
        )
        fields = MergedAssetFieldGroups(excludes=[MergedAssetFieldGroups.SOURCE_DATA])
        terms_query = {
            "query": {
                "bool": {
                    "filter": [
                        org_filter,
                        {"terms": {f"merged_data.{field}.keyword": duplicated_values}},
                    ]
                }
            },
            "size": 1000,  # Adjust size as needed
            "_source": fields.to_es_query_source(),
        }
        merged_assets = cls.scan(terms_query)

        duplicate_assets = {}
        for merged_asset in merged_assets:
            value = getattr(merged_asset.merged_data, field)
            if value not in duplicate_assets:
                duplicate_assets[value] = []
            duplicate_assets[value].append(merged_asset)

        return duplicate_assets

    @classmethod
    def create(cls, asset: MergedAsset) -> str:
        """
        Create a new merged asset in OpenSearch.
        """
        asset.metadata.created = asset.metadata.updated = timezone.now()
        _index = cls.INDEX_WRITE
        result = cls.client.index(
            _index,
            body=asset.to_dict(),
            refresh=cls.REFRESH,
        )
        asset.locator = es_service.get_locator(result)
        return asset.locator

    @classmethod
    def update(cls, asset: MergedAsset, fields: MergedAssetFieldGroups = None):
        """
        Update an existing merged asset in OpenSearch.
        """
        asset.metadata.updated = timezone.now()
        _index, _type, _id = es_service.decode_locator(asset.locator)
        result = cls.client.update(
            _index,
            _id,
            {"doc_as_upsert": True, "doc": asset.to_dict(fields=fields)},
            refresh=True,
        )
        return result

    @classmethod
    def create_bulk(cls, assets: list[MergedAsset]) -> int:
        """
        Create multiple new merged assets in OpenSearch. Return number of created assets.
        """
        actions = cls._construct_create_actions(assets)
        results = cls._stream_actions(actions)
        created = 0
        for i, (ok, action) in enumerate(results):
            if ok:
                assets[i].locator = es_service.get_locator(action["create"])
                created += 1
        return created

    @classmethod
    def update_bulk(
        cls, assets: list[MergedAsset], fields: MergedAssetFieldGroups = None
    ) -> list[str]:
        """
        Update multiple existing merged assets in OpenSearch. Return number of updated assets.
        """
        actions = cls._construct_update_actions(assets, fields)
        results = cls._stream_actions(actions)
        return [
            action["update"]["_id"]
            for ok, action in results
            if action["update"]["result"] == "updated"
        ]

    @classmethod
    def delete_bulk(cls, assets: Iterable[MergedAsset]) -> list[str]:
        """
        Delete multiple existing merged assets in OpenSearch. Return IDs of deleted assets.
        """
        actions = cls._construct_delete_actions(assets)
        results = cls._stream_actions(actions)

        return [
            action["delete"]["_id"]
            for ok, action in results
            if ok and action["delete"]["result"] == "deleted"
        ]

    @classmethod
    def replace_bulk(
        cls,
        assets: list[MergedAsset],
        refresh: str = None,
        raise_on_exception: bool = True,
    ) -> BulkResult:
        """
        Replace multiple existing merged assets in OpenSearch. Replace excludes
        the overridden fields from the update.
        """
        actions = cls._construct_replace_actions(assets)

        results = cls._stream_actions(
            actions,
            refresh=refresh,
            raise_on_exception=raise_on_exception,
        )
        updated = 0
        errors = []
        for i, (ok, action) in enumerate(results):
            if ok:
                if doc := action.get("update"):
                    if doc["result"] == "updated":
                        updated += 1
            else:
                errors.append(action)

        return BulkResult(0, updated, 0, errors)

    @classmethod
    def create_delete_bulk(
        cls,
        assets_to_create: list[MergedAsset],
        assets_to_delete: list[MergedAsset],
        refresh: str = None,
        raise_on_exception: bool = True,
    ) -> BulkResult:
        """
        Create and delete multiple merged assets in one OpenSearch operation.

        :arg refresh: If 'true', OpenSearch refreshes the affected
            shards to make this operation visible to search, if `wait_for` then wait
            for a refresh to make this operation visible to search, if `false` do
            nothing with refreshes.
        """
        action_lists = []

        create_actions = cls._construct_create_actions(assets_to_create)
        action_lists.append(create_actions)

        delete_actions = cls._construct_delete_actions(assets_to_delete)
        action_lists.append(delete_actions)

        actions = chain.from_iterable(action_lists)
        results = cls._stream_actions(
            actions,
            refresh=refresh,
            raise_on_exception=raise_on_exception,
        )
        created, deleted = 0, 0
        errors = []
        for i, (ok, action) in enumerate(results):
            if ok:
                if doc := action.get("create"):
                    if doc["result"] == "created":
                        assets_to_create[i].locator = es_service.get_locator(doc)
                        created += 1
                elif doc := action.get("delete"):
                    if doc["result"] == "deleted":
                        deleted += 1
            else:  # pragma: no cover
                errors.append(action)

        return BulkResult(created, 0, deleted, errors)

    @classmethod
    def _construct_create_actions(
        cls,
        merged_assets: Iterable[MergedAsset],
    ) -> Generator[dict, None, None]:
        """
        Construct create actions for new merged assets.
        """
        now = timezone.now()
        for merged_asset in merged_assets:
            if not merged_asset.metadata.created:
                merged_asset.metadata.created = merged_asset.metadata.updated = now
            else:
                merged_asset.metadata.updated = now
            yield {
                "_op_type": "create",
                "_index": cls.INDEX_WRITE,
                "_source": merged_asset.to_dict(),
            }

    @classmethod
    def _construct_replace_actions(cls, merged_assets: Iterable[MergedAsset]):
        """
        Construct update actions to replace technology fields.
        """
        fields = MergedAssetFieldGroups.technology_fields()

        now = timezone.now()
        for merged_asset in merged_assets:
            merged_asset.metadata.updated = now
            _index, _type, _id = es_service.decode_locator(merged_asset.locator)
            body = {
                "_op_type": "update",
                "_index": _index,
                "_id": _id,
                "script": {
                    "source": "\n".join(
                        [
                            f"ctx._source['{f}'] = params.{f};"
                            for f in fields.get_field_names()
                        ]
                    ),
                    "params": merged_asset.to_dict(fields=fields),
                },
            }
            if merged_asset.seq_no:
                body["if_seq_no"] = merged_asset.seq_no
                body["if_primary_term"] = merged_asset.primary_term

            yield body

    @classmethod
    def _construct_update_actions(
        cls,
        merged_assets: Iterable[MergedAsset],
        fields: MergedAssetFieldGroups = None,
    ) -> Generator[dict, None, None]:
        """
        Construct update actions for existing merged assets.
        """
        now = timezone.now()
        for merged_asset in merged_assets:
            merged_asset.metadata.updated = now
            if merged_asset.locator is None:
                raise ValueError(
                    "Cannot update merged asset without locator"
                )  # pragma: no cover

            _index, _type, _id = es_service.decode_locator(merged_asset.locator)
            yield {
                "_op_type": "update",
                "_index": _index,
                "_id": _id,
                "doc_as_upsert": True,  # necessary to retain some metadata
                "doc": merged_asset.to_dict(fields=fields),
            }

    @classmethod
    def _construct_delete_actions(cls, merged_assets: Iterable[MergedAsset]):
        """
        Construct delete actions for existing merged assets.
        """
        for merged_asset in merged_assets:
            _index, _type, _id = es_service.decode_locator(merged_asset.locator)
            yield {
                "_op_type": "delete",
                "_index": _index,
                "_id": _id,
            }

    @classmethod
    def _stream_actions(
        cls,
        merged_asset_actions: Iterable[dict],
        batch_size: int = 500,
        max_retries: int = 2,
        refresh: str = None,
        raise_on_exception: bool = True,
    ):
        """
        Stream a series of actions to OpenSearch.
        """
        return streaming_bulk(
            cls.client,
            merged_asset_actions,
            max_retries=max_retries,
            chunk_size=batch_size,
            refresh=refresh or cls.REFRESH,
            raise_on_error=raise_on_exception,
            raise_on_exception=raise_on_exception,
        )

    @classmethod
    def _generate_org_filter_for_duplicate_search(
        cls, organization_id: UUID, field: str
    ):
        """
        Generate an organization filter for duplicate search.
        This filter ensures that only documents belonging to the specified organization
        and having non-empty, non-null values for the specified field are considered.
        """
        return {
            "bool": {
                "filter": [
                    {
                        "bool": {
                            "must": [
                                # Ensure the document belongs to the specified organization
                                {
                                    "term": {
                                        "metadata.organization_id": str(organization_id)
                                    }
                                },
                                # Ensure the specified field exists
                                {"exists": {"field": f"merged_data.{field}.keyword"}},
                                # Ensure the specified field is not an empty string
                                {
                                    "bool": {
                                        "must_not": {
                                            "term": {f"merged_data.{field}.keyword": ""}
                                        }
                                    }
                                },
                            ]
                        }
                    }
                ]
            }
        }


@dataclass
class BulkResult:
    created: int = 0
    updated: int = 0
    deleted: int = 0
    errors: list[dict] = Field(default_factory=list)


@dataclass
class MergedAssetTechnologiesMetadata:
    count: int
    all: list[str]
    backup_agent: list[str]
    backup_agent_combined: str
    full_coverage: bool
    endpoint_security: list[str] = Field(default_factory=list)
    endpoint_security_combined: str = Field(default_factory=str)
    vulnerability_management: list[str] = Field(default_factory=list)
    vulnerability_management_combined: str = Field(default_factory=str)
    endpoint_security_excluded: bool = False
    vulnerability_management_excluded: bool = False
    all_combined: str = Field(default_factory=str)

    def to_dict(self):
        return {
            "count": self.count,
            "all": ToDict.list_str(self.all),
            "backup_agent": ToDict.list_str(self.backup_agent),
            "backup_agent_combined": ToDict.str(self.backup_agent_combined),
            "full_coverage": self.full_coverage,
            "endpoint_security": ToDict.list_str(self.endpoint_security),
            "endpoint_security_combined": ToDict.str(self.endpoint_security_combined),
            "vulnerability_management": ToDict.list_str(self.vulnerability_management),
            "vulnerability_management_combined": ToDict.str(
                self.vulnerability_management_combined
            ),
            "endpoint_security_excluded": self.endpoint_security_excluded,
            "vulnerability_management_excluded": self.vulnerability_management_excluded,
            "all_combined": ToDict.str(self.all_combined),
        }


@dataclass
class MergedAssetSourcesMetadata:
    type: str
    technologies: Optional[MergedAssetTechnologiesMetadata]
    last_seen: Optional[datetime] = None
    last_seen_endpoint_security: Optional[datetime] = None
    last_seen_vulnerability_management: Optional[datetime] = None

    def to_dict(self):
        return {
            "type": self.type,
            "technologies": ToDict.dataclass(self.technologies),
            "last_seen": ToDict.datetime(self.last_seen),
            "last_seen_endpoint_security": ToDict.datetime(
                self.last_seen_endpoint_security
            ),
            "last_seen_vulnerability_management": ToDict.datetime(
                self.last_seen_vulnerability_management
            ),
        }

    @classmethod
    def full_coverage(cls, sources: MergedAssetSources):
        for category in CoverageCategory.supported():
            if not sources.by_coverage(category):
                return False

        return True


@dataclass
class MergedAssetMetadata:
    organization_id: Optional[str]
    organization_alias: Optional[str]
    asset: Optional[MergedAssetSourcesMetadata]
    created: Optional[datetime]
    updated: Optional[datetime]

    # TODO - Remove Optional and default value after we populate account_id for every merged asset
    account_id: Optional[str] = None
    account_alias: Optional[str] = None
    manually_merged: bool = False

    @field_validator("organization_id", mode="before")
    def organization_id_to_str(cls, value):
        if isinstance(value, UUID):
            return str(value)
        return value

    def to_dict(self):
        return {
            "organization_id": str(self.organization_id),
            "organization_alias": ToDict.str(self.organization_alias),
            "asset": ToDict.dataclass(self.asset),
            "created": ToDict.datetime(self.created),
            "updated": ToDict.datetime(self.updated),
            "account_id": ToDict.str(self.account_id),
            "account_alias": ToDict.str(self.account_alias),
            "manually_merged": self.manually_merged,
        }


@dataclass
class MergedAssetOverrides(BaseModel):
    """
    Represents data overridden by the user.
    We can't simply update the merged asset, because the values would get overridden
    by the next fetching task.
    """

    criticality: Optional[AssetCriticality] = None
    endpoint_security_excluded: Optional[bool] = None
    vulnerability_management_excluded: Optional[bool] = None

    def to_dict(self):
        return {
            "criticality": ToDict.str(self.criticality),
            "endpoint_security_excluded": ToDict.bool(self.endpoint_security_excluded),
            "vulnerability_management_excluded": ToDict.bool(
                self.vulnerability_management_excluded
            ),
        }

    def override_criticality(self, merged_asset: MergedAsset):
        if self.criticality is not None:
            merged_asset.merged_data.criticality = self.criticality

    def override_endpoint_security_excluded(self, merged_asset: MergedAsset):
        if self.endpoint_security_excluded is not None:
            merged_asset.metadata.asset.technologies.endpoint_security_excluded = (
                self.endpoint_security_excluded
            )

    def override_vulnerability_management_excluded(self, merged_asset: MergedAsset):
        if self.vulnerability_management_excluded is not None:
            merged_asset.metadata.asset.technologies.vulnerability_management_excluded = (
                self.vulnerability_management_excluded
            )

    def override_asset(self, merged_asset: MergedAsset, in_place: bool = False):
        if not in_place:
            merged_asset = deepcopy(merged_asset)

        for func in dir(self):
            if func.startswith("override_") and not func == "override_asset":
                getattr(self, func)(merged_asset)

        return merged_asset


class MergedAssetSources(RootModel):
    root: dict[str, list[MergedSourceAsset]]

    def to_dict(
        self,
        technologies: list[str] | None,
        asset_fields: AssetFieldGroups,
    ) -> dict[str, list[dict[str, Any]]]:
        return {
            t: [source.to_dict(asset_fields) for source in self[t]]
            for t in technologies or self.root.keys()
        }

    def add(self, source: MergedSourceAsset):
        if source.technology_id not in self.root:
            self.root[source.technology_id] = []

        self[source.technology_id].append(source)

    def clear(self):
        self.root.clear()

    def by_coverage(self, coverage: CoverageCategory) -> list[MergedSourceAsset]:
        if coverage == CoverageCategory.VULNERABILITY_MANAGEMENT:
            return [s for s in self.__all() if s.has_vulnerability_coverage]
        if coverage == CoverageCategory.ENDPOINT_SECURITY:
            return [s for s in self.__all() if s.has_endpoint_coverage]
        return [s for s in self.__all() if s.technology_category == coverage]

    def technology_ids(self, coverage: CoverageCategory = None) -> set[str]:
        if coverage:
            return set(s.technology_id for s in self.by_coverage(coverage))
        return set(t for t, s in self.root.items() if s)

    def get_sources_by(self, by: CoverageCategory | str | None = None):
        """
        Get sources matching the given coverage category or technology ID.

        :param by: If a CoverageCategory is provided, only sources of that category will be considered. If a string is
                   provided, only sources of that technology will be considered. If None, all sources will be considered.
        """

        if isinstance(by, CoverageCategory):
            return self.by_coverage(by)
        elif by:
            return self[by]
        return self.__all()

    def max_last_seen(self, by: CoverageCategory | str | None = None):
        """
        Get the maximum last seen date from all sources.
        :param by: If a CoverageCategory is provided, only sources of that category will be considered. If a string is
                   provided, only sources of that technology will be considered. If None, all sources will be considered.
        """
        sources = self.get_sources_by(by)
        return max((s.last_seen for s in sources if s.last_seen), default=None)

    def max_last_seen_source(self, by: CoverageCategory | str | None = None):
        """
        Get the source with the maximum last seen date from all sources. This method needs to read all fields
        to break ties when multiple sources have the same last seen date, which needs to be considered when
        calling this method.
        :param by: If a CoverageCategory is provided, only sources of that category will be considered. If a string is
                   provided, only sources of that technology will be considered. If None, all sources will be considered.
        """

        # This function is used as a tie-breaker when multiple sources have the same last seen date.
        def number_of_truthy_values(source):
            return sum(
                bool(getattr(source.attributes, field))
                for field in source.attributes.get_members()
            )

        sources = self.get_sources_by(by)

        return max(
            (s for s in sources if s.last_seen),
            default=None,
            key=lambda s: (s.last_seen, number_of_truthy_values(s)),
        )

    def has_criticality(self):
        return any(s.attributes.criticality for s in self.__all())

    def count(self):
        return sum(len(assets) for assets in self.root.values())

    def all(self) -> list[MergedSourceAsset]:
        return list(self.__all())

    def __all(self) -> Generator[MergedSourceAsset]:
        yield from chain.from_iterable(self.root.values())

    def __getitem__(self, item: str):
        return self.root.get(item, [])


@dataclass
class MergedAsset(BaseModel):
    metadata: Optional[MergedAssetMetadata] = None
    source_data: Optional[MergedAssetSources] = None
    merged_data: Optional[AssetAttributes] = None
    overrides: MergedAssetOverrides = Field(default_factory=MergedAssetOverrides)

    id: str = Field(default_factory=lambda _: str(uuid4()))
    locator: Optional[str] = Field(exclude=True)
    seq_no: Optional[int] = Field(exclude=True, default=None)
    primary_term: Optional[int] = Field(exclude=True, default=None)

    documents = MergedAssetElasticSearchManager()
    audit_logs = MergedAssetAuditLogManager()
    field_to_dict = {
        "metadata": ToDict.dataclass,
        # source_data is a special case handled in the to_dict method
        "merged_data": ToDict.dataclass,
        "overrides": ToDict.dataclass,
        "id": ToDict.str,
        # excluded fields not included in the dict
    }

    @field_validator("overrides", mode="before")
    def validate_overrides(cls, v) -> MergedAssetOverrides:
        return MergedAssetOverrides() if v is None else v

    @field_validator("source_data", mode="before")
    def validate_source_data(
        cls, v
    ) -> dict[str, list[MergedSourceAsset]] | MergedAssetSources:
        if isinstance(v, MergedAssetSources):
            return v
        for technology_id, assets in v.items():
            # ensuring a list ensures backwards compatibility with existing ES data
            if not isinstance(assets, list):
                v[technology_id] = [assets]
        return v

    @model_validator(mode="before")
    def validate_merged_data(cls, data):
        kwargs = data
        if isinstance(data, ArgsKwargs):
            kwargs = data.kwargs

        if isinstance(kwargs, dict):
            if kwargs and isinstance(kwargs.get("merged_data"), dict):
                attrs_type = AssetAttributes.get_attributes_type(
                    kwargs["metadata"]["asset"]["type"]
                )
                attrs = attrs_type.get_defaults()
                attrs.update(kwargs["merged_data"])
                kwargs["merged_data"] = attrs_type(**attrs)
        return data

    def reconcile(
        self, rule_entries: list[(ReconciliationRule, list[ReconcilerEntry])]
    ):
        """
        Reconcile the fields of this merged asset from its sources.
        """

        # Create a simple mapping of technology IDs to sources for reconciliation.
        # Use the latest last seen source for each technology to reconcile the merged asset.
        # If last seen is not available, use the first source.
        sources_for_reconcile = {
            technology_id: self.source_data.max_last_seen_source(technology_id)
            or next(iter(self.source_data[technology_id]))
            for technology_id in self.source_data.technology_ids()
        }

        attributes_type = self.attributes_type
        self.merged_data = attributes_type(**attributes_type.get_defaults())
        for rule, entries in rule_entries:
            for entry in entries:
                if entry.technology_id not in sources_for_reconcile:
                    continue

                source = sources_for_reconcile[entry.technology_id]

                # To avoid populating the merged asset with default values here,
                # HostAssetAttributes value classes must implement the __bool__ method.
                if value := getattr(source.attributes, rule.field):
                    # Field was reconciled. Assign it and move on to the next field.
                    setattr(self.merged_data, rule.field, value)
                    break

    @property
    def document_id(self) -> str:
        _index, _type, _id = es_service.decode_locator(self.locator)
        return _id

    @property
    def type(self) -> AssetType:
        return AssetType(self.metadata.asset.type)

    @property
    def attributes_type(self) -> type[AssetAttributes]:
        return AssetAttributes.get_attributes_type(self.type)

    @property
    def overridden_merged_asset(self):
        return self.overrides.override_asset(self, in_place=False)

    @property
    def full_coverage(self):
        return self.metadata.asset.technologies.full_coverage

    @property
    def coverage_gaps(self) -> list[CoverageCategory]:
        coverage_gaps = set()

        if (
            not self.metadata.asset.technologies.endpoint_security
            and not self.metadata.asset.technologies.endpoint_security_excluded
        ):
            coverage_gaps.add(CoverageCategory.ENDPOINT_SECURITY)

        if (
            not self.metadata.asset.technologies.vulnerability_management
            and not self.metadata.asset.technologies.vulnerability_management_excluded
        ):
            coverage_gaps.add(CoverageCategory.VULNERABILITY_MANAGEMENT)

        return list(coverage_gaps)

    def last_seen(self, technology_id: str = None) -> datetime | None:
        if technology_id:
            return self.source_data.max_last_seen(technology_id)
        return self.metadata.asset.last_seen

    @staticmethod
    def create_shell(
        org: Organization, _id: str, asset_type: AssetType, created
    ) -> MergedAsset:
        """
        Create a new MergedAsset shell. This is used to create a new asset to be
        populated later during the reconciliation process.
        """
        merged_asset = MergedAsset(
            id=_id,
            metadata=MergedAssetMetadata(
                account_id=str(org.account.id),
                account_alias=org.account.alias,
                organization_id=str(org.id),
                organization_alias=org.alias,
                asset=MergedAssetSourcesMetadata(
                    type=asset_type,
                    technologies=None,
                ),
                created=created,
                updated=None,
            ),
            source_data=MergedAssetSources({}),
            merged_data=None,
            locator=None,
        )
        return merged_asset

    def to_dict(self, fields: MergedAssetFieldGroups = None) -> dict[str, Any]:
        fields = fields or MergedAssetFieldGroups()
        field_names = fields.get_field_names()
        data = {}
        for field in field_names:
            if field == fields.SOURCE_DATA:
                data[field] = self.source_data.to_dict(
                    fields.technologies, fields.asset_fields
                )
            else:
                value = getattr(self, field)
                data[field] = self.field_to_dict[field](value)

        return data
