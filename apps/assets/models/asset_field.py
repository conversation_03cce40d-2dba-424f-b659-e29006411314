from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON><PERSON>
from django.db import models

from apps.assets.models import AssetType

asset_types = [(asset_type.value, asset_type.title()) for asset_type in AssetType]


class FieldManager(models.Manager):
    def get_excluded_values(self, asset_type: AssetType) -> dict[str, list[str]]:
        fields = self.filter(asset_type=asset_type)
        return {field.name: field.excluded_values for field in fields}


class AssetField(models.Model):
    asset_type = models.CharField(max_length=255, choices=asset_types)
    name = models.CharField(max_length=255)
    excluded_values = ArrayField(
        default=list, base_field=models.CharField(max_length=255)
    )

    objects = FieldManager()

    def __str__(self):
        return f"{self.asset_type}.{self.name}"

    class Meta:
        unique_together = ["asset_type", "name"]


excluded_macs = [
    # list of prefixes to exclude
    "00:50:56:",  # VMware virtual MAC addresses
    "00:05:9a:3c:7a:00",  # Cisco VPN client address
    "00:15:5d:",  # Microsoft Hyper-V addresses
    "ac:de:48:00:11:22",  # iBridge interface on new MacBook Pro (later 2016)
    "00:ff:bd:4d:50:a7",  # Unknown but found in the wild on different devices
    "00:09:0f:",  # Fortinet VPN client address
    "00:a0:c6:00:00:01",  # embedded Qualcomm firmware bug
    "00:a0:c6:00:00:00",  # embedded Qualcomm firmware bug
    "0c:37:96:4f:55:73",  # BIZLINK TECHNOLOGY, INC.
]

STATIC_EXCLUDED_VALUES = {
    AssetType.HOST: {
        "universal_mac_addresses": excluded_macs,  # New field naming convention
    }
}


def save_to_database():
    for asset_type, fields in STATIC_EXCLUDED_VALUES.items():
        for field, values in fields.items():
            field, _ = AssetField.objects.get_or_create(
                asset_type=asset_type, name=field
            )
            field.excluded_values = values
            field.save(update_fields=["excluded_values"])
