from django.contrib.postgres.fields import ArrayField
from django.db import models

from apps.accounts.models import Organization


class RulesManager(models.Manager):
    def global_rules(self):
        return self.filter(organization=None)


class IdentificationRule(models.Model):
    asset_type = models.CharField(max_length=255)
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, null=True)

    objects = RulesManager()

    class Meta:
        unique_together = ["asset_type", "organization"]

    def __repr__(self):
        return (
            f"IdentificationRule("
            f"asset_type={self.asset_type!r}, "
            f"organization={self.organization!r}"
            f")"
        )


class IdentifierEntry(models.Model):
    identification_rule = models.ForeignKey(
        IdentificationRule, on_delete=models.CASCADE, related_name="entries"
    )
    fields = ArrayField(models.CharField(max_length=255))

    """
    NAND (NOT-AND) exists fields are fields that must not exist on both sides of the comparison.
    |********* Truth Table **********|
    |        Source       |  Result  |
    | A exists | B exists | A NAND B |
    |----------|----------|----------|
    |  False   |  False   |   valid  |
    |  False   |  True    |   valid  |
    |  True    |  False   |   valid  |
    |  True    |  True    |  invalid |
    """
    nand_exists_fields = ArrayField(models.CharField(max_length=255), default=list)

    class Meta:
        unique_together = ["identification_rule", "fields", "nand_exists_fields"]

    def __repr__(self):
        return (
            f"IdentifierEntry("
            f"identification_rule={self.identification_rule!r}, "
            f"fields={self.fields!r}, "
            f")"
        )


class ReconciliationRule(models.Model):
    asset_type = models.CharField(max_length=255)
    field = models.CharField(max_length=255)
    organization = models.ForeignKey(Organization, on_delete=models.RESTRICT, null=True)

    objects = RulesManager()

    class Meta:
        unique_together = ["asset_type", "field", "organization"]

    def __repr__(self):
        return (
            f"ReconciliationRule("
            f"asset_type={self.asset_type!r}, "
            f"field={self.field!r}, "
            f"organization={self.organization!r}"
            f")"
        )


class ReconcilerEntry(models.Model):
    reconciliation_rule = models.ForeignKey(
        ReconciliationRule, on_delete=models.CASCADE, related_name="entries"
    )
    priority = models.IntegerField()
    technology_id = models.CharField(max_length=255)

    class Meta:
        ordering = ["-priority"]
        unique_together = ["reconciliation_rule", "technology_id"]

    def __repr__(self):
        return (
            f"ReconcilerEntry("
            f"reconciliation_rule={self.reconciliation_rule!r}, "
            f"priority={self.priority!r}, "
            f"technology_id={self.technology_id!r}"
            f")"
        )
