from apps.assets.models import (
    HostAttributes,
    IntegrationMeta,
    MergedAsset,
    MergedAssetMeta,
    MergedSourceAsset,
    SourceHost,
)


def convert_to_source_host(merged_asset: MergedAsset, source_asset: MergedSourceAsset):
    """
    Convert a MergedAsset and MergedSourceAsset to a SourceHost.

    Note: Typically, we create a SourceHost first which triggers the creation
    of a MergedAsset. This function is used to convert an existing MergedAsset/MergedSourceAsset
    back to a SourceHost which is useful for testing.
    """
    return SourceHost(
        source_id=source_asset.metadata.asset.source_id,
        attributes=HostAttributes(
            criticality=source_asset.attributes.criticality,
            last_seen=source_asset.metadata.asset.last_seen,
            hostname=source_asset.attributes.hostname,
            fqdns=source_asset.attributes.fqdn,
            os=source_asset.attributes.os,
            public_ips=source_asset.attributes.public_ip,
            private_ips=source_asset.attributes.private_ip,
            primary_ip_address=source_asset.attributes.primary_ip_address,
            universal_mac_addresses=source_asset.attributes.universal_mac_address,
            local_mac_addresses=source_asset.attributes.local_mac_address,
            primary_mac_address=source_asset.attributes.primary_mac_address,
            internet_exposure=source_asset.attributes.internet_exposure,
            owners=source_asset.attributes.owner,
            group_names=source_asset.attributes.group_name,
            aad_id=source_asset.attributes.aad_id,
        ),
        integration=IntegrationMeta(
            id=str(source_asset.metadata.integration.id),
            organization_id=str(merged_asset.metadata.organization_id),
            technology_id=source_asset.metadata.integration.technology_id,
            category_id=source_asset.metadata.integration.category_id,
            vulnerability_coverage_mode=source_asset.metadata.integration.vulnerability_coverage_mode,
            endpoint_coverage_mode=source_asset.metadata.integration.endpoint_coverage_mode,
        ),
        merged_asset=MergedAssetMeta(
            id=merged_asset.id,
            created=merged_asset.metadata.created,
            manually_merged=merged_asset.metadata.manually_merged,
        ),
    )
