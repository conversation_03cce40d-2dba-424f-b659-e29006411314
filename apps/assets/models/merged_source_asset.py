from datetime import datetime
from enum import StrEnum, auto
from functools import cache, cached_property
from typing import Annotated, Any, ClassVar, Optional, TypeVar, Union
from uuid import UUID

from pydantic import (
    BeforeValidator,
    StringConstraints,
    field_validator,
    model_validator,
)
from pydantic.dataclasses import dataclass
from pydantic_core import ArgsKwargs

from apps.assets.models import BaseModel, ToDict
from apps.integrations.models import Integration


def remove_falsy_items(v):
    return v if all(v) else [s for s in v if s]


def falsy_to_none(v):
    return v or None


T = TypeVar("T")
LowerStr = Annotated[str, StringConstraints(to_lower=True)]
TruthyItemList = Annotated[list[T], BeforeValidator(remove_falsy_items)]
TruthyOptional = Annotated[Optional[T], BeforeValidator(falsy_to_none)]


class AssetCriticality(StrEnum):
    TIER_0 = "tier0"
    TIER_1 = "tier1"
    TIER_2 = "tier2"
    TIER_3 = "tier3"
    TIER_4 = "tier4"
    UNKNOWN = "unknown"

    @classmethod
    def labels(cls):
        return {
            cls.TIER_0: "Tier 0",
            cls.TIER_1: "Tier 1",
            cls.TIER_2: "Tier 2",
            cls.TIER_3: "Tier 3",
            cls.TIER_4: "Tier 4",
            cls.UNKNOWN: "Unknown",
        }

    @classmethod
    def label(cls, criticality):
        return cls.labels().get(criticality)

    def __bool__(self):
        return self != AssetCriticality.UNKNOWN


class HostType(StrEnum):
    SERVER = auto()
    WORKSTATION = auto()
    MOBILE = auto()
    CONTAINER = auto()
    OTHER = auto()
    UNKNOWN = auto()


class OsFamily(StrEnum):
    WINDOWS = auto()
    LINUX = auto()
    MAC = auto()
    ANDROID = auto()
    IOS = auto()
    UNKNOWN = auto()

    @staticmethod
    def labels():
        """This should only include publicly facing OS families."""
        return {
            OsFamily.WINDOWS: "Windows",
            OsFamily.LINUX: "Linux",
            OsFamily.MAC: "macOS",
            OsFamily.UNKNOWN: "Unknown",
        }

    @staticmethod
    def external_families():
        return [
            OsFamily.WINDOWS,
            OsFamily.LINUX,
            OsFamily.MAC,
            OsFamily.UNKNOWN,
        ]


class CoverageCategory(StrEnum):
    ENDPOINT_SECURITY = auto()
    BACKUP_AGENT = auto()
    VULNERABILITY_MANAGEMENT = auto()
    TECHNICAL_SECURITY_CONTROL = auto()
    # An asset is imported from a source that does not provide coverage
    NONE = auto()

    @classmethod
    def supported(cls):
        return [
            cls.ENDPOINT_SECURITY,
            cls.VULNERABILITY_MANAGEMENT,
        ]

    @classmethod
    def technical_security_controls(cls) -> list["CoverageCategory"]:
        return [
            cls.ENDPOINT_SECURITY,
            cls.VULNERABILITY_MANAGEMENT,
        ]


class InternetExposure(StrEnum):
    INTERNET_FACING = auto()
    NOT_INTERNET_FACING = auto()
    UNKNOWN = auto()

    @classmethod
    def labels(cls):
        return {
            cls.INTERNET_FACING: "Internet Facing",
            cls.NOT_INTERNET_FACING: "Not Internet Facing",
            cls.UNKNOWN: "Unknown",
        }

    def __bool__(self):
        return self != InternetExposure.UNKNOWN


class AssetType(StrEnum):
    HOST = auto()
    USER = auto()


@dataclass
class AssetAttributes(BaseModel):
    _attribute_types: ClassVar[dict[AssetType, type["AssetAttributes"]]] = {}
    type: ClassVar[AssetType] = None

    # criticality is optional only because we have existing data
    # where it is set to null in ES.  If we fix up the data, we
    # can make it required and get rid of the validator below.
    criticality: Optional[AssetCriticality]

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        cls._attribute_types[cls.type] = cls

    def to_dict(self):
        return {"criticality": ToDict.str(self.criticality)}

    @classmethod
    @property
    def all_types(cls):
        return tuple(cls._attribute_types.values())

    @classmethod
    def get_attributes_type(cls, asset_type: AssetType):
        return cls._attribute_types[asset_type]

    @field_validator("criticality", mode="before")
    def validate_criticality(cls, v) -> AssetCriticality:  # noqa: N805
        return v or AssetCriticality.UNKNOWN

    @classmethod
    def get_defaults(cls) -> dict[str, Any]:
        # All attributes must be present
        return {"criticality": AssetCriticality.UNKNOWN}


@dataclass
class OsAttributes(BaseModel):
    host_type: HostType
    family: OsFamily
    name: TruthyOptional[str]

    def to_dict(self):
        return {
            "host_type": ToDict.str(self.host_type),
            "family": ToDict.str(self.family),
            "name": ToDict.str(self.name),
        }

    @classmethod
    def get_defaults(cls) -> dict[str, Any]:
        # All attributes must be present
        return {
            "host_type": HostType.UNKNOWN,
            "family": OsFamily.UNKNOWN,
            "name": None,
        }

    def __bool__(self):
        return self != OsAttributes(**self.get_defaults())


@dataclass
class OwnerAttributes(BaseModel):
    name: str
    # Use simple str instead of EmailStr for performance
    email: Optional[str]

    def to_dict(self):
        return {
            "name": ToDict.str(self.name),
            "email": ToDict.str(self.email),
        }

    @classmethod
    def get_defaults(cls) -> dict[str, Any]:
        # All attributes must be present
        return {
            "name": "",
            "email": None,
        }

    def __bool__(self):
        return self != OwnerAttributes(**self.get_defaults())


@dataclass
class HostAssetAttributes(AssetAttributes):
    type: ClassVar[AssetType] = AssetType.HOST

    group_name: list[str]
    hostname: LowerStr
    fqdn: TruthyItemList[LowerStr]
    public_ip: TruthyItemList[str]
    private_ip: TruthyItemList[str]
    # TODO remove mac_address after migration
    mac_address: TruthyItemList[str]
    universal_mac_address: TruthyItemList[str]
    local_mac_address: TruthyItemList[str]
    internet_exposure: InternetExposure
    os: OsAttributes
    owner: TruthyItemList[OwnerAttributes]
    aad_id: Optional[LowerStr]
    primary_ip_address: Optional[str]
    primary_mac_address: Optional[str]

    def to_dict(self):
        return {
            "group_name": ToDict.list_str(self.group_name),
            "hostname": ToDict.str(self.hostname),
            "fqdn": ToDict.list_str(self.fqdn),
            "public_ip": ToDict.list_str(self.public_ip),
            "private_ip": ToDict.list_str(self.private_ip),
            "mac_address": ToDict.list_str(self.mac_address),
            "universal_mac_address": ToDict.list_str(self.universal_mac_address),
            "local_mac_address": ToDict.list_str(self.local_mac_address),
            "internet_exposure": ToDict.str(self.internet_exposure),
            "os": ToDict.dataclass(self.os),
            "owner": ToDict.list_dataclass(self.owner),
            "aad_id": ToDict.str(self.aad_id),
            "primary_ip_address": ToDict.str(self.primary_ip_address),
            "primary_mac_address": ToDict.str(self.primary_mac_address),
            **super().to_dict(),
        }

    # FIXME: Temporary remove this validation in favor of a future solution.
    #   Validation was removed due to validation errors during reconcile. We set one
    #   property at a time and this validator is based on two properties.
    # @model_validator(mode="after")
    # def validate_fqdn(self):
    #     if self.hostname and self.fqdn:
    #         prefixes = [f.startswith(self.hostname) for f in self.fqdn]
    #         if not any(prefixes):
    #             raise ValueError(f"{self.fqdn=} does not start with {self.hostname=}")
    #     return self

    @staticmethod
    def default_primary_ip_addr(
        preferred_ips: list[str] = None, secondary_ips: list[str] = None
    ):
        if preferred_ips:
            return preferred_ips[0]
        if secondary_ips:
            return secondary_ips[0]
        return None

    @staticmethod
    def default_primary_mac_addr(
        universal_addresses: list[str] = None,
        local_addresses: list[str] = None,
        excluded_universal_values: list[str] = None,
    ):
        if excluded_universal_values is None:
            excluded_universal_values = []

        for mac in universal_addresses:
            for excluded in excluded_universal_values:
                if mac.startswith(excluded):
                    break
            else:
                return mac
        if local_addresses:
            return local_addresses[0]
        return None

    @classmethod
    def get_defaults(cls) -> dict[str, Any]:
        # All attributes must be present
        defaults = super().get_defaults()
        defaults.update(
            {
                "group_name": [],
                "hostname": "",
                "fqdn": [],
                "public_ip": [],
                "private_ip": [],
                "mac_address": [],
                "universal_mac_address": [],
                "local_mac_address": [],
                "internet_exposure": InternetExposure.UNKNOWN,
                "os": OsAttributes.get_defaults(),
                "aad_id": None,
                "primary_ip_address": None,
                "primary_mac_address": None,
                "owner": [],
            }
        )
        return defaults


@dataclass
class UserAttributes(AssetAttributes):
    type: ClassVar[AssetType] = AssetType.USER
    username: Optional[str]
    email: Optional[str]

    def to_dict(self):  # pragma: no cover
        return {
            "type": ToDict.str(self.type),
            "username": ToDict.str(self.username),
            "email": ToDict.str(self.email),
            **super().to_dict(),
        }


@dataclass
class SourceAssetIntegrationMetadata:
    id: str
    technology_id: str
    category_id: str
    vulnerability_coverage_mode: Integration.CoverageMode = (
        Integration.CoverageMode.NOT_APPLICABLE
    )
    endpoint_coverage_mode: Integration.CoverageMode = (
        Integration.CoverageMode.NOT_APPLICABLE
    )

    @field_validator("id", mode="before")
    def convert_uuid_to_str(cls, value):  # noqa: N805
        if isinstance(value, UUID):
            return str(value)
        return value

    def to_dict(self):
        return {
            "id": ToDict.str(self.id),
            "technology_id": ToDict.str(self.technology_id),
            "category_id": ToDict.str(self.category_id),
            "vulnerability_coverage_mode": ToDict.str(self.vulnerability_coverage_mode),
            "endpoint_coverage_mode": ToDict.str(self.endpoint_coverage_mode),
        }


@dataclass
class SourceAssetDetailMetadata:
    type: str
    last_seen: Optional[datetime]
    source_id: str

    def to_dict(self):
        return {
            "type": ToDict.str(self.type),
            "last_seen": ToDict.datetime(self.last_seen),
            "source_id": ToDict.str(self.source_id),
        }


@dataclass
class SourceAssetMetadata:
    integration: Optional[SourceAssetIntegrationMetadata]
    asset: Optional[SourceAssetDetailMetadata]

    def to_dict(self):
        return {
            "integration": ToDict.dataclass(self.integration),
            "asset": ToDict.dataclass(self.asset),
        }


@dataclass
class AssetFieldGroups:
    """
    Groups of asset fields to read or write.
    """

    ID = "asset_id"
    ATTRIBUTES = "attributes"
    METADATA = "metadata"

    includes: Optional[list[str]] = None

    def get_field_names(self):
        """
        Get all included field names.
        """
        return self.includes or self.get_default_members()

    @staticmethod
    @cache
    def get_default_members():
        return MergedSourceAsset.get_members()


AssetAttributeTypes = Union[AssetAttributes.all_types]


@dataclass
class MergedSourceAsset(BaseModel):
    metadata: Optional[SourceAssetMetadata] = None
    attributes: Optional[AssetAttributeTypes] = None

    field_to_dict = {
        "asset_id": ToDict.str,
        "metadata": ToDict.dataclass,
        "attributes": ToDict.dataclass,
    }

    @cached_property
    def asset_id(self) -> tuple:
        return self.metadata.integration.id, self.metadata.asset.source_id

    @model_validator(mode="before")
    def validate_attributes(cls, data):  # noqa: N805
        kwargs = data
        if isinstance(data, ArgsKwargs):
            kwargs = data.kwargs
        if isinstance(kwargs, dict):
            if isinstance(kwargs.get("attributes"), dict):
                attrs_type = AssetAttributes.get_attributes_type(
                    kwargs["metadata"]["asset"]["type"]
                )
                attrs = attrs_type.get_defaults()
                attrs.update(kwargs["attributes"])
                kwargs["attributes"] = attrs_type(**attrs)
        return data

    @property
    def technology_id(self) -> str:
        return self.metadata.integration.technology_id

    @property
    def technology_category(self) -> str:
        return self.metadata.integration.category_id

    @property
    def integration_id(self) -> str:
        return self.metadata.integration.id

    @property
    def last_seen(self) -> Optional[datetime]:
        return self.metadata.asset.last_seen

    @property
    def has_vulnerability_coverage(self) -> bool:
        return (
            self.metadata.integration.vulnerability_coverage_mode
            == Integration.CoverageMode.ENABLED
        )

    @property
    def has_endpoint_coverage(self) -> bool:
        return (
            self.metadata.integration.endpoint_coverage_mode
            == Integration.CoverageMode.ENABLED
        )

    def has_changed(self, other: "MergedSourceAsset"):
        """Checks if asset has changed."""
        if not other.last_seen:
            return False

        if not self.last_seen:
            return True

        if other.last_seen == self.last_seen:
            return other.attributes != self.attributes

        return other.last_seen > self.last_seen

    def to_dict(self, fields: AssetFieldGroups = None) -> dict[str, Any]:
        fields = fields or AssetFieldGroups()
        field_names = fields.get_field_names()
        data = {}
        for field in field_names:
            value = getattr(self, field)
            data[field] = self.field_to_dict[field](value)

        return data
