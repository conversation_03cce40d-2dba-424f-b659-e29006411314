from __future__ import annotations

from collections.abc import Generator, Iterable
from datetime import datetime
from functools import cached_property
from typing import Annotated, Literal, Optional, TypeVar
from uuid import UUID

from django.utils import timezone
from opensearchpy import Search
from opensearchpy.helpers import streaming_bulk
from pydantic import (
    BeforeValidator,
    Field,
    StringConstraints,
    field_validator,
    model_validator,
)
from pydantic.dataclasses import dataclass
from pydantic_core import ArgsKwargs

from apps.assets.models import (
    AssetCriticality,
    BaseModel,
    InternetExposure,
    OsAttributes,
    OwnerAttributes,
    ToDict,
)
from apps.es import es_service
from apps.integrations.models import Integration


def remove_falsy_items(v):
    # Avoid creating a new list if all items are truthy. We do
    # this for better performance reading data we know is valid.
    return v if all(v) else [s for s in v if s]


T = TypeVar("T")
LowerStr = Annotated[str, StringConstraints(to_lower=True)]
TruthyItemList = Annotated[list[T], BeforeValidator(remove_falsy_items)]


class SourceHostElasticSearchManager:
    client = es_service.es
    INDEX_WRITE = "source_host-current"
    INDEX_READ = "source_host-current"

    REFRESH = "wait_for"

    @classmethod
    def search(cls, body: dict, index: str = None, **kwargs) -> list[SourceHost]:
        response = cls.search_raw(
            index=index,
            body=body,
            **kwargs,
        )
        documents = response["hits"]["hits"]
        return [cls._convert_to_source_host(doc) for doc in documents]

    @classmethod
    def search_raw(cls, body: dict, index: str = None, **kwargs) -> dict:
        return cls.client.search(
            index=index or cls.INDEX_READ,
            body=body,
            **kwargs,
        )

    @classmethod
    def create_bulk(cls, hosts: list[SourceHost]):
        actions = cls._construct_create_actions(hosts)
        results = cls._stream_actions(actions)
        created = 0
        for i, (ok, action) in enumerate(results):
            if ok:
                hosts[i].locator = es_service.get_locator(action["create"])
                created += 1
        return created

    @classmethod
    def update_bulk(cls, hosts: Iterable[SourceHost], fields: list[str] = None):
        actions = cls._construct_update_actions(hosts, fields)
        results = cls._stream_actions(actions)
        updated = 0
        for i, (ok, action) in enumerate(results):
            if ok:
                updated += 1
        return updated

    @staticmethod
    def get_update_fetched_fields():
        return (
            "updated",
            "attributes",
            "integration",
        )

    @staticmethod
    def get_update_merged_asset_meta_fields():
        return (
            "updated",
            "merged_asset",
        )

    @classmethod
    def get_by_integration(cls, integration_id: UUID):
        search = Search(using=cls.client, index=cls.INDEX_READ)
        search = search.filter("term", integration__id=str(integration_id))
        search = search.filter("term", type="host")

        for doc in search.scan():
            yield cls._convert_to_source_host(doc)

    @classmethod
    def get_by_organization(cls, org_id: UUID):
        search = Search(using=cls.client, index=cls.INDEX_READ)
        search = search.filter("term", integration__organization_id=str(org_id))
        search = search.filter("term", type="host")

        for doc in search.scan():
            yield cls._convert_to_source_host(doc)

    @classmethod
    def get_by_merged_assets(cls, merged_asset_ids: list[str]):
        search = Search(using=cls.client, index=cls.INDEX_READ)
        search = search.filter("terms", merged_asset__id=merged_asset_ids)

        for doc in search.scan():
            yield cls._convert_to_source_host(doc)

    @classmethod
    def delete_merged_assets(cls, merged_asset_ids: list[str]):
        search = Search(using=cls.client, index=cls.INDEX_WRITE)
        search = search.filter("terms", merged_asset__id=merged_asset_ids)
        # The delete by query refresh param only allows 'true' or 'false' (the docs are wrong)
        search = search.params(refresh="true")

        search.delete()

    @classmethod
    def delete_by_integration(cls, integration_id: UUID):
        search = Search(using=cls.client, index=cls.INDEX_WRITE)
        search = search.filter("term", integration__id=str(integration_id))
        # The delete by query refresh param only allows 'true' or 'false' (the docs are wrong)
        search = search.params(refresh="true")

        search.delete()

    @classmethod
    def create_update_delete_bulk(
        cls,
        create: list[SourceHost],
        update: Iterable[SourceHost],
        delete: list[SourceHost],
        fields: list[str] = None,
    ):
        actions = list(cls._construct_create_actions(create))
        actions.extend(cls._construct_update_actions(update, fields))
        actions.extend(cls._construct_delete_actions(delete))
        results = cls._stream_actions(actions)
        created, updated, deleted = 0, 0, 0
        for i, (ok, action) in enumerate(results):
            if ok:
                if doc := action.get("create"):
                    if doc["result"] == "created":
                        create[i].locator = es_service.get_locator(doc)
                        created += 1
                elif doc := action.get("update"):
                    if doc["result"] == "updated":
                        updated += 1
                elif doc := action.get("delete"):
                    if doc["result"] == "deleted":
                        deleted += 1
        return created, updated, deleted

    @staticmethod
    def _convert_to_source_host(data):
        if isinstance(data, dict):
            locator = es_service.get_locator(data)
            return SourceHost(**data["_source"], locator=locator)
        else:
            locator = es_service.encode_locator(data.meta.index, "_doc", data.meta.id)
            return SourceHost(**data.to_dict(), locator=locator)

    @classmethod
    def _construct_create_actions(
        cls,
        hosts: Iterable[SourceHost],
    ) -> Generator[dict, None, None]:
        """
        Construct create actions for new source host.
        """
        now = timezone.now()
        for host in hosts:
            host.created = host.updated = now
            yield {
                "_op_type": "create",
                "_index": cls.INDEX_WRITE,
                "_source": host.to_dict(),
            }

    @classmethod
    def _construct_update_actions(
        cls,
        hosts: Iterable[SourceHost],
        fields: list[str] = None,
    ) -> Generator[dict, None, None]:
        """
        Construct update actions for existing source hosts.
        """
        now = timezone.now()
        for host in hosts:
            host.updated = now
            if host.locator is None:
                raise ValueError(
                    "Cannot update source host without locator"
                )  # pragma: no cover

            _index, _, _id = es_service.decode_locator(host.locator)
            yield {
                "_op_type": "update",
                "_index": _index,
                "_id": _id,
                "doc_as_upsert": True,  # necessary to retain some metadata
                "doc": host.to_dict(fields=fields),
            }

    @classmethod
    def _construct_delete_actions(cls, hosts: Iterable[SourceHost]):
        """
        Construct delete actions for existing source hosts.
        """
        for host in hosts:
            if host.locator is None:
                raise ValueError(
                    "Cannot delete source host without locator"
                )  # pragma: no cover

            _index, _, _id = es_service.decode_locator(host.locator)
            yield {
                "_op_type": "delete",
                "_index": _index,
                "_id": _id,
            }

    @classmethod
    def _stream_actions(
        cls,
        actions: Iterable[dict],
        batch_size: int = 500,
        max_retries: int = 2,
    ):
        """
        Stream a series of actions to OpenSearch.
        """
        return streaming_bulk(
            cls.client,
            actions,
            max_retries=max_retries,
            chunk_size=batch_size,
            refresh=cls.REFRESH,
        )


@dataclass
class HostAttributes:
    criticality: Optional[AssetCriticality]
    last_seen: Optional[datetime]

    hostname: LowerStr
    fqdns: TruthyItemList[LowerStr]
    os: OsAttributes

    public_ips: TruthyItemList[str]
    private_ips: TruthyItemList[str]
    primary_ip_address: Optional[str]
    universal_mac_addresses: TruthyItemList[str]
    local_mac_addresses: TruthyItemList[str]
    primary_mac_address: Optional[str]
    internet_exposure: InternetExposure

    owners: TruthyItemList[OwnerAttributes]
    group_names: TruthyItemList[str]

    aad_id: Optional[LowerStr]

    def to_dict(self):
        return {
            "criticality": ToDict.str(self.criticality),
            "last_seen": ToDict.datetime(self.last_seen),
            "hostname": ToDict.str(self.hostname),
            "fqdns": ToDict.list_str(self.fqdns),
            "os": self.os.to_dict(),
            "public_ips": ToDict.list_str(self.public_ips),
            "private_ips": ToDict.list_str(self.private_ips),
            "primary_ip_address": ToDict.str(self.primary_ip_address),
            "universal_mac_addresses": ToDict.list_str(self.universal_mac_addresses),
            "local_mac_addresses": ToDict.list_str(self.local_mac_addresses),
            "primary_mac_address": ToDict.str(self.primary_mac_address),
            "internet_exposure": ToDict.str(self.internet_exposure),
            "owners": ToDict.list_dataclass(self.owners),
            "group_names": ToDict.list_str(self.group_names),
            "aad_id": ToDict.str(self.aad_id),
        }

    @classmethod
    def get_defaults(cls):
        return {
            "criticality": AssetCriticality.UNKNOWN,
            "last_seen": None,
            "hostname": "",
            "fqdns": "",
            "os": OsAttributes.get_defaults(),
            "public_ips": "",
            "private_ips": "",
            "primary_ip_address": None,
            "universal_mac_addresses": "",
            "local_mac_addresses": "",
            "primary_mac_address": None,
            "internet_exposure": InternetExposure.UNKNOWN,
            "owners": [],
            "group_names": [],
            "aad_id": None,
        }


@dataclass
class IntegrationMeta:
    id: str
    organization_id: str
    technology_id: str
    category_id: str
    vulnerability_coverage_mode: Integration.CoverageMode = (
        Integration.CoverageMode.NOT_APPLICABLE
    )
    endpoint_coverage_mode: Integration.CoverageMode = (
        Integration.CoverageMode.NOT_APPLICABLE
    )

    @field_validator("id", mode="before")
    def convert_id_to_str(cls, value):  # noqa: N805
        if isinstance(value, UUID):
            return str(value)
        return value

    @field_validator("organization_id", mode="before")
    def convert_organization_id_to_str(cls, value):  # noqa: N805
        if isinstance(value, UUID):
            return str(value)
        return value

    def to_dict(self):
        return {
            "id": ToDict.str(self.id),
            "organization_id": ToDict.str(self.organization_id),
            "technology_id": ToDict.str(self.technology_id),
            "category_id": ToDict.str(self.category_id),
            "vulnerability_coverage_mode": ToDict.str(self.vulnerability_coverage_mode),
            "endpoint_coverage_mode": ToDict.str(self.endpoint_coverage_mode),
        }


@dataclass
class MergedAssetMeta:
    id: str
    created: Optional[datetime] = None
    manually_merged: bool = False

    @field_validator("id", mode="before")
    def convert_id_to_str(cls, value):  # noqa: N805
        if isinstance(value, UUID):
            return str(value)
        return value

    def to_dict(self):
        return {
            "id": ToDict.str(self.id),
            "created": ToDict.datetime(self.created),
            "manually_merged": ToDict.bool(self.manually_merged),
        }


@dataclass
class SourceHost(BaseModel):
    locator: Optional[str] = Field(exclude=True, default=None)

    source_id: Optional[str] = None  # ID given by the source system
    type: Literal["host"] = "host"

    created: Optional[datetime] = None
    updated: Optional[datetime] = None

    attributes: Optional[HostAttributes] = None
    integration: Optional[IntegrationMeta] = None
    merged_asset: Optional[MergedAssetMeta] = None

    documents = SourceHostElasticSearchManager()

    field_to_dict = {
        "source_id": ToDict.str,
        "type": ToDict.str,
        "created": ToDict.datetime,
        "updated": ToDict.datetime,
        "attributes": ToDict.dataclass,
        "integration": ToDict.dataclass,
        "merged_asset": ToDict.dataclass,
    }

    @cached_property
    def internal_id(self):
        return self.integration.id, self.source_id

    @model_validator(mode="before")
    @staticmethod
    def validate_attributes(data):
        kwargs = data
        if isinstance(data, ArgsKwargs):
            kwargs = data.kwargs
        if isinstance(kwargs, dict):
            if isinstance(kwargs.get("attributes"), dict):
                attrs = HostAttributes.get_defaults()
                attrs.update(kwargs["attributes"])
                kwargs["attributes"] = HostAttributes(**attrs)
        return data

    def to_dict(self, fields=None):
        if not fields:
            fields = [f.name for f in self.get_fields()]

        data = {}
        for field in fields:
            value = getattr(self, field)
            data[field] = self.field_to_dict[field](value)
        return data
