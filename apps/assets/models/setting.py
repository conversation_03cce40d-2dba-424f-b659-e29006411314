import uuid
from enum import StrEnum
from typing import Optional
from uuid import UUID

from criticalstart.fastapi_utils.parameters.period import QueryPeriodType
from django.core.exceptions import ValidationError as DjangoValidationError
from django.db import models
from pydantic import BaseModel, ValidationError

from apps.assets.models.merged_source_asset import AssetCriticality


class Setting(models.Model):
    class Category(models.TextChoices):
        DISPLAY = "display", "Display"
        CONFIGURATION = "configuration", "Configuration"
        ASSET_CRITICALITY = "asset_criticality", "Asset Criticality"
        PURGE_AUTOMATION = "purge_automation", "Purge Automation"

    id: UUID = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    category = models.CharField(max_length=255, choices=Category.choices)
    key = models.CharField(max_length=255)
    default_value = models.JSONField()

    class Meta:
        unique_together = ("category", "key")

    def clean(self) -> None:
        super().clean()
        self.default_value = self.validate_setting_value(
            self.category, self.key, self.default_value
        ).model_dump(mode="json")

    @staticmethod
    def validate_setting_value(category: str, key: str, value: dict):
        try:
            validation_schema = settings_values_map[category][key]
        except KeyError:
            raise DjangoValidationError(f"Invalid Category/Key: {category}/{key}")

        try:
            return validation_schema.model_validate(value)
        except ValidationError as e:
            errors = {".".join(error["loc"]): error["msg"] for error in e.errors()}
            raise DjangoValidationError(errors)

    def save(self, *args, **kwargs):
        self.clean()
        return super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.category}/{self.key}"


class SettingValue(BaseModel):
    enabled: bool


class CriticalitySettingValue(SettingValue):
    criticality: AssetCriticality


class DistrosCriticalitySettingValue(CriticalitySettingValue):
    distros: list[str]


class DaysSettingValue(SettingValue):
    days: int


class FilterSettingValue(BaseModel):
    coverage_gap: Optional[list[str]] = None
    last_seen: Optional[QueryPeriodType] = None
    last_seen_endpoint_security: Optional[QueryPeriodType] = None
    last_seen_vulnerability_management: Optional[QueryPeriodType] = None
    created: Optional[QueryPeriodType] = None
    os_family: Optional[list[str]] = None
    host_type: Optional[list[str]] = ["workstation", "server"]
    criticality: Optional[list[AssetCriticality]] = None
    technology: Optional[list[str]] = None
    integration: Optional[list[str]] = None
    endpoint_security_combined: Optional[list[str]] = None
    endpoint_security_excluded: Optional[bool] = None
    vulnerability_management_combined: Optional[list[str]] = None
    vulnerability_management_excluded: Optional[bool] = None
    manually_merged: Optional[bool] = None


class SettingKey(StrEnum):
    # Display
    FILTER_LAST_SEEN_GTR_X_DAYS = "filter_last_seen_gtr_x_days"
    DASHBOARD_FILTERS = "dashboard_filters"
    HOSTS_FILTERS = "hosts_filters"

    # Asset Criticality
    OS_LINUX_DISTROS = "os_linux_distros"
    OS_WINDOWS_SERVER = "os_windows_server"
    OS_WINDOWS_WORKSTATION = "os_windows_workstation"
    OS_MAC = "os_mac"

    # Purge Automation
    SOURCE_DELETED = "source_deleted"
    LAST_SEEN_GTR_X_DAYS = "last_seen_gtr_x_days"


settings_values_map = {
    Setting.Category.DISPLAY: {
        SettingKey.FILTER_LAST_SEEN_GTR_X_DAYS: DaysSettingValue(
            enabled=True,
            days=45,
        ),
        SettingKey.DASHBOARD_FILTERS: FilterSettingValue(),
        SettingKey.HOSTS_FILTERS: FilterSettingValue(),
    },
    Setting.Category.ASSET_CRITICALITY: {
        SettingKey.OS_LINUX_DISTROS: DistrosCriticalitySettingValue(
            enabled=True,
            criticality=AssetCriticality.TIER_3,
            distros=["Red Hat", "CentOS", "Debian", "Ubuntu", "Amazon Linux", "Oracle"],
        ),
        SettingKey.OS_WINDOWS_SERVER: CriticalitySettingValue(
            enabled=True,
            criticality=AssetCriticality.TIER_3,
        ),
        SettingKey.OS_WINDOWS_WORKSTATION: CriticalitySettingValue(
            enabled=True,
            criticality=AssetCriticality.TIER_4,
        ),
        SettingKey.OS_MAC: CriticalitySettingValue(
            enabled=True,
            criticality=AssetCriticality.TIER_4,
        ),
    },
    Setting.Category.PURGE_AUTOMATION: {
        SettingKey.SOURCE_DELETED: SettingValue(enabled=True),
        SettingKey.LAST_SEEN_GTR_X_DAYS: DaysSettingValue(
            enabled=True,
            days=180,
        ),
    },
}
