from __future__ import annotations

from abc import ABC, abstractmethod
from typing import TYPE_CHECKING, Any
from uuid import UUID

from criticalstart.audit_logs import Action, AuditLog, Entity

if TYPE_CHECKING:  # pragma: no cover
    from collections.abc import Iterable


class BaseAuditLogManager(ABC):
    @abstractmethod
    def get_entity(self, obj) -> Entity:
        pass

    @abstractmethod
    def get_create_details(self, obj) -> dict[str, str]:
        pass

    @abstractmethod
    def get_delete_details(self, obj) -> dict[str, str]:
        pass

    @abstractmethod
    def get_update_details(self, field: str, old_value: Any, new_value: Any) -> dict:
        pass

    def create(self, obj, action_by_id: UUID):
        from core.service_bus import service_bus

        audit_log = AuditLog(
            action=Action.CREATE,
            action_by_id=action_by_id,
            details=self.get_create_details(obj),
            entity=self.get_entity(obj),
        )

        audit_log.send(bus=service_bus)

    def delete(self, objs: Iterable, action_by_id: UUID):
        from core.service_bus import service_bus

        for obj in objs:
            audit_log = AuditLog(
                action=Action.DELETE,
                action_by_id=action_by_id,
                details=self.get_delete_details(obj),
                entity=self.get_entity(obj),
            )
            audit_log.send(bus=service_bus)

    def update(self, obj, user_id: UUID, field: str, old_value: Any, new_value: Any):
        from core.service_bus import service_bus

        audit_log = AuditLog(
            action=Action.UPDATE,
            action_by_id=user_id,
            details=self.get_update_details(field, old_value, new_value),
            entity=self.get_entity(obj),
        )

        audit_log.send(bus=service_bus)
