from __future__ import annotations

from typing import TYPE_CHECKING, Any

from criticalstart.audit_logs import Entity, EntityTypeId

from apps.assets.audit_logs.base import BaseAuditLogManager

if TYPE_CHECKING:  # pragma: no cover
    from apps.assets.models import MergedAsset


class MergedAssetAuditLogManager(BaseAuditLogManager):
    def get_create_details(self, merged_asset: MergedAsset) -> dict[str, str]:
        return {
            "hostname": merged_asset.merged_data.hostname,
            "ip": merged_asset.merged_data.primary_ip_address,
            "type_id": merged_asset.type,
        }

    def get_delete_details(self, merged_asset: MergedAsset) -> dict[str, str]:
        return {
            "hostname": merged_asset.merged_data.hostname,
            "ip": merged_asset.merged_data.primary_ip_address,
            "type_id": merged_asset.type,
        }

    def get_update_details(self, field: str, old_value: Any, new_value: Any) -> dict:
        return {
            "field_name": field,
            "old_value_raw": old_value,
            "value_raw": new_value,
        }

    def get_entity(self, merged_asset: MergedAsset) -> Entity:
        return Entity(
            id=merged_asset.id,
            type_id=EntityTypeId.MERGED_ASSET,
            account_id=merged_asset.metadata.account_id,
            organization_id=merged_asset.metadata.organization_id,
        )
