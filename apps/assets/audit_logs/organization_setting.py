from __future__ import annotations

from typing import TYPE_CHECKING, Any

from criticalstart.audit_logs import Entity, EntityTypeId

from apps.assets.audit_logs.base import BaseAuditLogManager

if TYPE_CHECKING:  # pragma: no cover
    from apps.assets.models import OrganizationSetting


class OrganizationSettingAuditLogManager(BaseAuditLogManager):
    def get_delete_details(
        self, organization_setting: OrganizationSetting
    ) -> dict[str, str]:
        return {
            "category": organization_setting.setting.category,
            "key": organization_setting.setting.key,
            "overridden_value": organization_setting.overridden_value,
        }

    def get_update_details(self, field: str, old_value: Any, new_value: Any) -> dict:
        return {
            "field_name": field,
            "old_value_raw": old_value,
            "value_raw": new_value,
        }

    def get_create_details(
        self, organization_setting: OrganizationSetting
    ) -> dict[str, str]:
        return {
            "category": organization_setting.setting.category,
            "key": organization_setting.setting.key,
            "overridden_value": organization_setting.overridden_value,
        }

    def get_entity(self, organization_setting: OrganizationSetting) -> Entity:
        return Entity(
            id=organization_setting.id,
            type_id=EntityTypeId.ASSET_INVENTORY_ORG_SETTING,
            account_id=organization_setting.organization.account.id,
            organization_id=organization_setting.organization.id,
        )
