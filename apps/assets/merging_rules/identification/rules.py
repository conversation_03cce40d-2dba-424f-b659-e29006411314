from dataclasses import dataclass

from apps.assets.models import IdentificationRule, IdentifierEntry
from apps.assets.models.merged_source_asset import AssetType

# Dictionary containing global static rules that are not configurable by the user.
IDENTIFICATION_RULES = {
    AssetType.HOST: [
        {"fields": ["aad_id"], "nand_exists_fields": []},
        {"fields": ["universal_mac_addresses"], "nand_exists_fields": []},
        {"fields": ["hostname", "fqdns"], "nand_exists_fields": []},
        {
            "fields": ["hostname"],
            "nand_exists_fields": [
                "universal_mac_addresses",
                "local_mac_addresses",
                "aad_id",
            ],
        },
    ],
}


@dataclass
class IdentificationStaticRules:
    rules: dict[AssetType, list[dict]]

    def to_models(self) -> list[tuple[IdentificationRule, list[IdentifierEntry]]]:
        rules = []
        for asset_type, id_entries in self.rules.items():
            rule = IdentificationRule(
                asset_type=asset_type,
                organization_id=None,
            )
            entries = [
                IdentifierEntry(
                    fields=id_entry["fields"],
                    nand_exists_fields=id_entry["nand_exists_fields"],
                )
                for i, id_entry in enumerate(id_entries)
            ]
            rules.append((rule, entries))
        return rules


identification_rules = IdentificationStaticRules(rules=IDENTIFICATION_RULES)
