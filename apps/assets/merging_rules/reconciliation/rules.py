from collections import defaultdict
from dataclasses import dataclass

from django.contrib.postgres.aggregates import ArrayAgg

from apps.assets.models import ReconcilerEntry, ReconciliationRule
from apps.assets.models.merged_source_asset import AssetType
from apps.integrations.utils import get_external_technology_ids

DEFAULT_ORDER = [
    "ms_intune",
    "crowdstrike_falcon",
    "falcon_em",
    "defender_atp",
    "tenable_io",
    "s1_ranger",
    "sentinel_one",
    "carbon_black",
    "cb_threat_hunter",
    "cb_cloud",
    "cortex_xdr",
    "qualys_vmpc",
    "azure_ad",
    "tm_vision_one",
    "jamf_pro",
    "cylance",
    "absolute",
    "qualys_gav",
    "infoblox_ddi",
    "cisco_ise",
]
# Dictionary containing global static rules that are not configurable by the user.
# Technology priorities are defined in the order of the list (the first technology
# has the highest priority).
RECONCILIATION_RULES = {
    AssetType.HOST: {
        "group_name": DEFAULT_ORDER,
        "hostname": DEFAULT_ORDER,
        "fqdn": DEFAULT_ORDER,
        "public_ip": DEFAULT_ORDER,
        "private_ip": DEFAULT_ORDER,
        "mac_address": DEFAULT_ORDER,
        "universal_mac_address": DEFAULT_ORDER,
        "local_mac_address": DEFAULT_ORDER,
        # We want to prioritize falcon_em, since it offers the most robust data
        "internet_exposure": [
            "falcon_em",
            "qualys_vmpc",
            "defender_atp",
            "ms_intune",
            "crowdstrike_falcon",
            "tenable_io",
            "s1_ranger",
            "sentinel_one",
            "carbon_black",
            "cb_threat_hunter",
            "cb_cloud",
            "cortex_xdr",
            "azure_ad",
            "tm_vision_one",
            "jamf_pro",
            "cylance",
            "absolute",
            "qualys_gav",
            "infoblox_ddi",
            "cisco_ise",
        ],
        # We want to prioritize tenable_io over defender_atp for os
        "os": [
            "ms_intune",
            "crowdstrike_falcon",
            "falcon_em",
            "s1_ranger",
            "sentinel_one",
            "tenable_io",
            "defender_atp",
            "carbon_black",
            "cb_threat_hunter",
            "cb_cloud",
            "cortex_xdr",
            "qualys_vmpc",
            "azure_ad",
            "tm_vision_one",
            "jamf_pro",
            "cylance",
            "absolute",
            "qualys_gav",
            "infoblox_ddi",
            "cisco_ise",
        ],
        "aad_id": [
            "azure_ad",
            "ms_intune",
            "defender_atp",
            "tenable_io",
            "sentinel_one",
            "carbon_black",
            "cb_threat_hunter",
            "cb_cloud",
            "cortex_xdr",
            "crowdstrike_falcon",
            "falcon_em",
            "s1_ranger",
            "qualys_vmpc",
            "tm_vision_one",
            "jamf_pro",
            "cylance",
            "absolute",
            "qualys_gav",
            "infoblox_ddi",
            "cisco_ise",
        ],
        "criticality": DEFAULT_ORDER,
        "primary_ip_address": DEFAULT_ORDER,
        "primary_mac_address": DEFAULT_ORDER,
        "owner": [
            "azure_ad",
            "ms_intune",
            "defender_atp",
            "tenable_io",
            "sentinel_one",
            "carbon_black",
            "cb_threat_hunter",
            "cb_cloud",
            "cortex_xdr",
            "crowdstrike_falcon",
            "falcon_em",
            "s1_ranger",
            "qualys_vmpc",
            "tm_vision_one",
            "jamf_pro",
            "cylance",
            "absolute",
            "qualys_gav",
            "infoblox_ddi",
            "cisco_ise",
        ],
    }
}


@dataclass
class ReconciliationStaticRules:
    rules: dict[AssetType, dict[str, list[str]]]

    def to_models(self) -> list[tuple[ReconciliationRule, list[ReconcilerEntry]]]:
        rules = []
        for asset_type, fields in self.rules.items():
            for field, technologies in fields.items():
                rule = ReconciliationRule(asset_type=asset_type, field=field)
                max_priority = len(technologies)
                entries = [
                    ReconcilerEntry(
                        priority=max_priority - order, technology_id=technology_id
                    )
                    for order, technology_id in enumerate(technologies)
                ]
                rules.append((rule, entries))
        return rules

    @staticmethod
    def from_models(rules_qs):
        rules = defaultdict(dict)
        for rule in rules_qs.annotate(technologies=ArrayAgg("entries__technology_id")):
            # ArrayAgg returns [None] when there are no entries, so filter out None values
            technologies = [
                tech for tech in (rule.technologies or []) if tech is not None
            ]
            rules[rule.asset_type][rule.field] = technologies
        return ReconciliationStaticRules(rules=rules)


# Function to update technologies with any new technologies that have been added
def _update_reconciliation_rules():
    """Update RECONCILIATION_RULES with any new technologies from the database."""
    external_ids = set(get_external_technology_ids())
    for asset_type, fields in RECONCILIATION_RULES.items():
        for field, technologies in fields.items():
            # Convert to set for easier manipulation
            set(technologies)

            # Remove technologies that are no longer in external_ids
            # Keep the order of remaining technologies
            technologies[:] = [tech for tech in technologies if tech in external_ids]


# Lazy initialization of reconciliation rules
_reconciliation_rules = None


def get_reconciliation_rules():
    """Get reconciliation rules, initializing them if needed."""
    global _reconciliation_rules
    if _reconciliation_rules is None:
        _update_reconciliation_rules()
        _reconciliation_rules = ReconciliationStaticRules(rules=RECONCILIATION_RULES)
    return _reconciliation_rules


# Create a simple object that will be replaced when first accessed
class LazyReconciliationRules:
    def __getattr__(self, name):
        # Replace this object with the real one on first access
        global reconciliation_rules
        reconciliation_rules = get_reconciliation_rules()
        return getattr(reconciliation_rules, name)


reconciliation_rules = LazyReconciliationRules()
