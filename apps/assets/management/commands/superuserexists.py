from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Check if any superuser exists (for local setup)."

    def handle(self, *args, **options):
        model = get_user_model()
        user_exists = model.objects.filter(is_superuser=True).count() > 0
        if not user_exists:
            print("No superuser exists.")
            raise SystemExit(1)
