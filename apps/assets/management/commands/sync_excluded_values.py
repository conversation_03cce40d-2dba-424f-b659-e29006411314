import logging

from django.core.management.base import BaseCommand

from apps.assets.models.asset_field import save_to_database

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Look for excluded field values defined in the code and save them to the database.
    """

    help = "Save excluded field values to the database."

    def handle(self, *args, **options):
        save_to_database()
