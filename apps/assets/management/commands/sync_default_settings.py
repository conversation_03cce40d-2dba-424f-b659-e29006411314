import logging

from django.core.management.base import BaseCommand

from apps.assets.models import Setting
from apps.assets.models.setting import settings_values_map

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Look for rules defined in the code and save them to the database.
    """

    help = "Save default asset settings to the database."

    def add_arguments(self, parser):
        parser.add_argument(
            "--force",
            dest="force",
            action="store_true",
            help="Override existing default settings if they don't match.",
        )

    def handle(self, *args, **options):
        force = options.get("force", False)
        for category, keys in settings_values_map.items():
            for key, default_value in keys.items():
                self.sync_default(category, key, default_value, force)

    @staticmethod
    def sync_default(category, key, default_value, force):
        setting, created = Setting.objects.get_or_create(
            category=category,
            key=key,
            defaults={"default_value": default_value.model_dump(mode="json")},
        )
        if not created:
            if setting.default_value != default_value.model_dump(mode="json"):
                if force:
                    setting.default_value = default_value.model_dump(mode="json")
                    setting.save()
                    logger.info(f"Default value for {category}/{key} has been updated.")
                else:
                    logger.warning(
                        f"Default value for {category}/{key} has changed. Use --force to override."
                    )
        else:
            logger.info(f"Created default setting {category}/{key}.")
