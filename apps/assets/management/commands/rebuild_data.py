import logging

from django.core.management.base import BaseCommand

from apps.accounts.models import Organization
from apps.assets.services import merge_service

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Management command to recalculate data for assets.
    """

    help = "Rebuild data for assets for an organization"

    def add_arguments(self, parser):
        parser.add_argument(
            "--organization_id",
            dest="organization_id",
            required=True,
            type=str,
            help="Organization ID (UUID)",
        )
        parser.add_argument(
            "--type",
            dest="asset_type",
            required=True,
            type=str,
            help="Asset type key",
        )

    def handle(self, *args, **options):
        organization_id = options["organization_id"]
        asset_type = options["asset_type"]

        # Update data for assets of this type for an organization
        if organization_id == "all":
            organizations = Organization.objects.all()
        else:
            organizations = [Organization.objects.get(id=organization_id)]

        if not self.confirm(organizations):
            return

        for organization in organizations:
            logger.info(f"Updating data for {organization.alias} ({organization.id})")
            merge_service.rebuild_data(organization, asset_type)

    def confirm(self, organizations):
        count = len(organizations)
        message = f"Are you sure you want to rebuild data for {count} organization(s)? (yes): "
        if input(message).lower() == "yes":
            return True

        abort_message = 'You must type "yes" to continue'
        logger.warning(abort_message)
        return False
