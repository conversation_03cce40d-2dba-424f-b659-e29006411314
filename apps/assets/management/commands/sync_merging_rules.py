import logging

from django.core.management.base import BaseCommand

from apps.assets.merging_rules.identification.rules import identification_rules
from apps.assets.merging_rules.reconciliation.rules import reconciliation_rules
from apps.assets.models import IdentificationRule, ReconciliationRule

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Look for rules defined in the code and save them to the database.
    """

    help = "Save rules to the database."

    def handle(self, *args, **options):
        _sync_merging_rules()


def _sync_merging_rules():
    for rule, entries in identification_rules.to_models():
        rule_obj, created = IdentificationRule.objects.get_or_create(
            asset_type=rule.asset_type,
            organization_id=rule.organization_id,
        )

        # We can definitely do something smarter here. But as long as we don't
        # support creating customer rules on the FE, I think this will never happen.
        if not created and rule_obj.organization:
            raise RuntimeError(
                "This would override an existing rule that's not in the code, aborting."
            )

        # It's safe to delete rules that are defined in the code
        rule_obj.entries.all().delete()
        for entry in entries:
            rule_obj.entries.add(entry, bulk=False)

    for rule, entries in reconciliation_rules.to_models():
        rule_obj, created = ReconciliationRule.objects.get_or_create(
            asset_type=rule.asset_type,
            field=rule.field,
            organization_id=rule.organization_id,
        )

        if not created and rule_obj.organization:
            raise RuntimeError(
                "This would override an existing rule that's not in the code, aborting."
            )

        rule_obj.entries.all().delete()
        for entry in entries:
            rule_obj.entries.add(entry, bulk=False)
