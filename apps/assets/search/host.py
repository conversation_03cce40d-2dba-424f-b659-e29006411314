from collections.abc import Sequence
from dataclasses import dataclass
from typing import Optional, Union
from uuid import UUID

from criticalstart.fastapi_utils.paginate.elasticsearch import ElasticSearchDocuments
from criticalstart.fastapi_utils.parameters.period import Period

from apps.api.v1.search import add_must_clause, add_must_not_clause
from apps.assets.models import AssetType
from apps.assets.models.merged_asset import MergedAsset, MergedAssetFieldGroups
from apps.assets.models.merged_source_asset import (
    AssetCriticality,
    AssetFieldGroups,
    CoverageCategory,
    HostType,
)
from apps.es import es_service


@dataclass
class InventoryFilter:
    missing_coverage: Optional[Sequence[CoverageCategory]] = None
    last_seen: Optional[Period] = None
    last_seen_endpoint_security: Optional[Period] = None
    last_seen_vulnerability_management: Optional[Period] = None
    created: Optional[Period] = None
    os_family: Optional[list[str]] = None
    host_type: Optional[list[HostType]] = None
    criticality: Optional[list[AssetCriticality]] = None
    technology_ids: Optional[list[str]] = None
    integration_ids: Optional[list[str]] = None
    missing_technology_ids: Optional[list[str]] = None
    endpoint_security_combined: Optional[list[str]] = None
    endpoint_security_excluded: Optional[bool] = None
    vulnerability_management_combined: Optional[list[str]] = None
    vulnerability_management_excluded: Optional[bool] = None
    manually_merged: Optional[bool] = None

    @classmethod
    def gap_excluded_clause(cls, coverage: CoverageCategory) -> dict:
        metadata_field = f"metadata.asset.technologies.{coverage}_excluded"
        overrides_field = f"overrides.{coverage}_excluded"
        excluded_clause = {
            "bool": {
                "should": [
                    {"bool": {"filter": [{"term": {overrides_field: True}}]}},
                    {
                        "bool": {
                            "must_not": [{"exists": {"field": overrides_field}}],
                            "filter": [{"term": {metadata_field: True}}],
                        }
                    },
                ],
                "minimum_should_match": 1,
            }
        }
        return excluded_clause

    @classmethod
    def coverage_combined_field(cls, coverage: CoverageCategory) -> str:
        return f"metadata.asset.technologies.{coverage}_combined"

    def last_seen_term(self):
        if len(self.technology_ids or []) == 1:
            tid = self.technology_ids[0]
            return f"source_data.{tid}.metadata.asset.last_seen.keyword"
        return "metadata.asset.last_seen"

    def filter_body(self, body: dict):
        if self.missing_coverage:
            self.search_by_missing_coverage(body)

        time_based_filters = [
            (
                self.last_seen,
                self.last_seen_term(),
            ),
            (
                self.last_seen_endpoint_security,
                "metadata.asset.last_seen_endpoint_security",
            ),
            (
                self.last_seen_vulnerability_management,
                "metadata.asset.last_seen_vulnerability_management",
            ),
            (
                self.created,
                "metadata.created",
            ),
        ]
        for period, term in time_based_filters:
            if period:
                clause = {
                    "range": {
                        term: {
                            "gte": period.start,
                            "lte": period.end,
                        }
                    }
                }
                if period.exclude:
                    add_must_not_clause(body, clause)
                else:
                    add_must_clause(body, clause)

                add_must_clause(body, {"exists": {"field": term}})

        if self.os_family:
            add_must_clause(
                body, {"terms": {"merged_data.os.family.keyword": self.os_family}}
            )

        if self.host_type:
            add_must_clause(
                body, {"terms": {"merged_data.os.host_type.keyword": self.host_type}}
            )

        if self.criticality:
            self.search_by_criticality(body)

        if self.technology_ids:
            add_must_clause(
                body,
                {"terms": {"metadata.asset.technologies.all": self.technology_ids}},
            )
        if self.missing_technology_ids:
            add_must_not_clause(
                body,
                {
                    "terms": {
                        "metadata.asset.technologies.all": self.missing_technology_ids
                    }
                },
            )

        if self.integration_ids:
            for integration_id in self.integration_ids:
                add_must_clause(
                    body,
                    {
                        "multi_match": {
                            "query": integration_id,
                            "fields": ["source_data.*.metadata.integration.id"],
                        }
                    },
                )

        if self.endpoint_security_combined:
            add_must_clause(
                body,
                {
                    "terms": {
                        "metadata.asset.technologies.endpoint_security_combined": self.endpoint_security_combined
                    }
                },
            )

        if self.vulnerability_management_combined:
            add_must_clause(
                body,
                {
                    "terms": {
                        "metadata.asset.technologies.vulnerability_management_combined": self.vulnerability_management_combined
                    }
                },
            )

        if self.endpoint_security_excluded is not None:
            excluded_clause = self.gap_excluded_clause(
                CoverageCategory.ENDPOINT_SECURITY
            )
            if self.endpoint_security_excluded:
                add_must_clause(body, excluded_clause)
            else:
                add_must_not_clause(body, excluded_clause)

        if self.vulnerability_management_excluded is not None:
            excluded_clause = self.gap_excluded_clause(
                CoverageCategory.VULNERABILITY_MANAGEMENT
            )
            if self.vulnerability_management_excluded:
                add_must_clause(body, excluded_clause)
            else:
                add_must_not_clause(body, excluded_clause)

        if self.manually_merged is not None:
            add_must_clause(
                body, {"term": {"metadata.manually_merged": self.manually_merged}}
            )

    def search_by_missing_coverage(self, body: dict) -> dict:
        # Note that all of these clauses are inverted because there is no equivalent to
        # "not_exists"
        clauses = []

        for missing in self.missing_coverage:
            if missing == CoverageCategory.NONE:
                clauses.append(
                    [{"match": {"metadata.asset.technologies.full_coverage": False}}]
                )
            elif missing == CoverageCategory.TECHNICAL_SECURITY_CONTROL:
                tsc_categories = CoverageCategory.technical_security_controls()
                combined_clause = []
                for tsc in tsc_categories:
                    coverage_list_field = f"metadata.asset.technologies.{tsc}"
                    combined_clause.append({"exists": {"field": coverage_list_field}})
                    combined_clause.append(self.gap_excluded_clause(tsc))
                clauses.append(combined_clause)
            else:
                coverage_list_field = f"metadata.asset.technologies.{missing}"
                clauses.append(
                    [
                        {"exists": {"field": coverage_list_field}},
                        self.gap_excluded_clause(missing),
                    ]
                )

        if clauses:
            # Most filters are simple and use a single `terms` clause. A `terms` clause is
            # an or operator, so we need to use a `bool` query with `should` to get the
            # equivalent of an or operator for missing coverage.
            add_must_clause(
                body,
                {
                    "bool": {
                        "should": [{"bool": {"must_not": c}} for c in clauses],
                        "minimum_should_match": 1,
                    }
                },
            )

        return body

    def search_by_criticality(self, body: dict) -> dict:
        """
        Search for criticality, converting unknown to proper exists query.
        """
        criticality = self.criticality
        override_field_exists = {"exists": {"field": "overrides.criticality"}}

        values = [c.value for c in criticality]
        field_matches = {"terms": {"merged_data.criticality.keyword": values}}
        override_field_matches = {"terms": {"overrides.criticality.keyword": values}}

        add_must_clause(
            body,
            {
                "bool": {
                    "should": [
                        {"bool": {"filter": override_field_matches}},
                        {
                            "bool": {
                                "filter": field_matches,
                                "must_not": override_field_exists,
                            }
                        },
                    ],
                    "minimum_should_match": 1,
                }
            },
        )
        return body


class HostClient(ElasticSearchDocuments):
    def __init__(
        self,
        organization_ids: set[Union[str, UUID]],
        filters: InventoryFilter | None,
        include_all_fields: bool = False,
    ):
        self.organization_ids = [str(org_id) for org_id in organization_ids]
        self.filters = filters
        self.include_all_fields = include_all_fields

    def search(self, body: dict):
        body = self._update_body(body)
        return MergedAsset.documents.search_raw(body, include_shells=False)

    def scan(self, body: dict):
        body = self._update_body(body)
        yield from MergedAsset.documents.scan(body, include_shells=False)

    def _update_body(self, body: dict):
        only_hosts = {"term": {"metadata.asset.type": AssetType.HOST}}
        add_must_clause(body, only_hosts)

        if self.organization_ids:
            add_must_clause(
                body,
                {"terms": {"metadata.organization_id": self.organization_ids}},
            )

        if self.filters:
            self.filters.filter_body(body)

            # Don't retrieve more asset data than is needed for this hosts request.
            # If we're filtering by technology, load only the metadata for the requested
            # technologies' assets.  Otherwise, no technology asset data is needed.
            if self.filters.technology_ids:
                fields = MergedAssetFieldGroups(
                    technologies=self.filters.technology_ids,
                    asset_fields=AssetFieldGroups(includes=[AssetFieldGroups.METADATA]),
                )
            else:
                fields = MergedAssetFieldGroups(
                    excludes=[MergedAssetFieldGroups.SOURCE_DATA]
                )
            if not self.include_all_fields:
                body["_source"] = fields.to_es_query_source()
        return body

    def from_doc(self, doc):
        asset = MergedAsset(locator=es_service.get_locator(doc), **doc["_source"])
        return asset
