from django.contrib import admin

from apps.assets.models import SyncTask


class RecentStartedFilter(admin.SimpleListFilter):
    title = "Recent Started"
    parameter_name = "recent_started"

    def lookups(self, request, model_admin):  # pragma: no cover
        return (
            ("yes", "Yes"),
            ("no", "No"),
        )

    def queryset(self, request, queryset):  # pragma: no cover
        last = SyncTask.objects.last()
        if not last:
            return queryset

        # Round down to the nearest minute
        last_start = last.started_at.replace(second=0, microsecond=0)
        if self.value() == "yes":
            return queryset.filter(started_at__gte=last_start)
        elif self.value() == "no":
            return queryset.exclude(started_at__gte=last_start)


@admin.register(SyncTask)
class SyncTaskAdmin(admin.ModelAdmin):
    list_display = (
        "correlation_id",
        "organization_alias",
        "technology_id",
        "asset_type",
        "status",
        "started_at",
        "staged_at",
        "finished_at",
    )
    list_filter = (
        RecentStartedFilter,
        "asset_type",
        "status",
        "integration__technology_id",
        "integration__organization__alias",
    )
    search_fields = (
        "correlation_id",
        "integration__organization__id",
        "integration__organization__alias",
        "integration__technology_id",
    )

    def organization_alias(self, obj):  # pragma: no cover
        return obj.integration.organization.alias

    organization_alias.short_description = "Organization Alias"
    organization_alias.admin_order_field = "integration__organization__alias"

    def technology_id(self, obj: SyncTask):  # pragma: no cover
        return obj.integration.technology_id

    technology_id.short_description = "Technology ID"
    technology_id.admin_order_field = "integration__technology_id"
