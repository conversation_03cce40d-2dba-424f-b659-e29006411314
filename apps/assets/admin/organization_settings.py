from django.contrib import admin

from apps.assets.models.organization_setting import OrganizationSetting


@admin.register(OrganizationSetting)
class OrganizationSettingsAdmin(admin.ModelAdmin):
    list_display = (
        "organization_alias",
        "category",
        "key",
        "overridden_value",
        "default_value",
    )
    list_filter = ("organization__alias",)
    search_fields = ("organization__alias",)

    def organization_alias(self, obj):  # pragma: no cover
        return obj.organization.alias

    organization_alias.short_description = "Organization Alias"
    organization_alias.admin_order_field = "organization__alias"

    def category(self, obj):  # pragma: no cover
        return obj.setting.category

    category.short_description = "Category"
    category.admin_order_field = "category__name"

    def key(self, obj):  # pragma: no cover
        return obj.setting.key

    key.short_description = "Key"

    def default_value(self, obj):  # pragma: no cover
        return obj.setting.default_value

    default_value.short_description = "Default Value"
