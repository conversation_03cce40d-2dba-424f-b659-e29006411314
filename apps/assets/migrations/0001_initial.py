# Generated by Django 3.2.19 on 2023-07-07 10:46

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("connectors", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Device",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("uid", models.UUIDField(unique=True)),
                ("account_uid", models.CharField(max_length=255)),
                ("group_name", models.Char<PERSON>ield(blank=True, max_length=255)),
                ("hostname", models.<PERSON>r<PERSON>ield(max_length=255)),
                ("fqdn", models.Char<PERSON>ield(blank=True, max_length=255)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("public_ip", models.GenericIPAddress<PERSON>ield(blank=True, null=True)),
                ("private_ip", models.GenericIPAddressField(blank=True, null=True)),
                ("mac_address", models.CharField(blank=True, max_length=255)),
                (
                    "device_type",
                    models.CharField(
                        choices=[
                            ("server", "Server"),
                            ("workstation", "Workstation"),
                            ("other", "Other"),
                        ],
                        default="other",
                        max_length=255,
                    ),
                ),
                ("os", models.CharField(blank=True, max_length=255)),
                (
                    "connector_configs",
                    models.ManyToManyField(to="connectors.ConnectorInstanceExtension"),
                ),
            ],
        ),
    ]
