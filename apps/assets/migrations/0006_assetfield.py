# Generated by Django 4.2.8 on 2024-03-19 10:32

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("assets", "0005_rename_asset_type_key_identificationrule_asset_type_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Asset<PERSON><PERSON>",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "asset_type",
                    models.CharField(
                        choices=[("host", "Host"), ("user", "User")], max_length=255
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                (
                    "excluded_values",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=255),
                        default=list,
                        size=None,
                    ),
                ),
            ],
            options={
                "unique_together": {("asset_type", "name")},
            },
        ),
    ]
