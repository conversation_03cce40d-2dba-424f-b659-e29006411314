# Generated by Django 4.2.5 on 2023-09-27 11:02

import django.contrib.postgres.fields
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("accounts", "0001_initial"),
        ("assets", "0002_synctask_delete_device"),
    ]

    operations = [
        migrations.CreateModel(
            name="IdentificationRule",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("asset_type_key", models.CharField(max_length=255)),
                (
                    "organization",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="accounts.organization",
                    ),
                ),
            ],
            options={
                "unique_together": {("asset_type_key", "organization")},
            },
        ),
        migrations.CreateModel(
            name="ReconciliationRule",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("asset_type_key", models.Char<PERSON>ield(max_length=255)),
                ("field", models.CharField(max_length=255)),
                (
                    "organization",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="accounts.organization",
                    ),
                ),
            ],
            options={
                "unique_together": {("asset_type_key", "field", "organization")},
            },
        ),
        migrations.CreateModel(
            name="ReconcilerEntry",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("priority", models.IntegerField()),
                ("technology_id", models.CharField(max_length=255)),
                (
                    "reconciliation_rule",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="entries",
                        to="assets.reconciliationrule",
                    ),
                ),
            ],
            options={
                "ordering": ["-priority"],
                "unique_together": {("reconciliation_rule", "technology_id")},
            },
        ),
        migrations.CreateModel(
            name="IdentifierEntry",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "fields",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=255), size=None
                    ),
                ),
                ("priority", models.IntegerField()),
                (
                    "identification_rule",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="entries",
                        to="assets.identificationrule",
                    ),
                ),
            ],
            options={
                "ordering": ["-priority"],
                "unique_together": {("identification_rule", "fields")},
            },
        ),
    ]
