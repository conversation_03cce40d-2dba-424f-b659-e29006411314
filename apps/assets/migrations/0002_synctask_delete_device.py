# Generated by Django 4.2.5 on 2023-09-20 07:42

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("integrations", "__first__"),
        ("assets", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="SyncTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("correlation_id", models.CharField(max_length=255, unique=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("started", "Started"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("fetched", "Fetched"),
                        ],
                        max_length=255,
                    ),
                ),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("finished_at", models.DateTimeField(blank=True, null=True)),
                ("asset_type_key", models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                (
                    "integration",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="integrations.integration",
                    ),
                ),
            ],
        ),
        migrations.DeleteModel(
            name="<PERSON>ce",
        ),
    ]
