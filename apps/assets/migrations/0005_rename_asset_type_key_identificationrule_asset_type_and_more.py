# Generated by Django 4.2.8 on 2024-02-23 14:04

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("accounts", "0003_alter_account_alias"),
        ("assets", "0004_synctask_staged_at_alter_synctask_status"),
    ]

    operations = [
        migrations.RenameField(
            model_name="identificationrule",
            old_name="asset_type_key",
            new_name="asset_type",
        ),
        migrations.RenameField(
            model_name="reconciliationrule",
            old_name="asset_type_key",
            new_name="asset_type",
        ),
        migrations.RenameField(
            model_name="synctask",
            old_name="asset_type_key",
            new_name="asset_type",
        ),
        migrations.AlterUniqueTogether(
            name="identificationrule",
            unique_together={("asset_type", "organization")},
        ),
        migrations.AlterUniqueTogether(
            name="reconciliationrule",
            unique_together={("asset_type", "field", "organization")},
        ),
    ]
