# Generated by Django 4.2.8 on 2024-03-27 17:55

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("assets", "0006_assetfield"),
    ]

    operations = [
        migrations.AddField(
            model_name="identifierentry",
            name="nand_exists_fields",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=255), default=list, size=None
            ),
        ),
    ]
