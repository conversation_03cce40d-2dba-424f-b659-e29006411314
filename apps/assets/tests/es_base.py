import json
import os
from unittest.mock import patch

from django.utils import timezone
from opensearchpy.helpers import bulk

from apps.assets.models.merged_asset import MergedAsset
from apps.es import es_manager, es_service
from core import settings

UNITTEST_MERGED_ASSET_INDEX = "merged_asset-test"
UNITTEST_MERGED_ASSET_BACKUP_INDEX = "merged_asset-test-backup"
UNITTEST_PERSISTENT_STATS_INDEX = "persistent_stats-test"
UNITTEST_DAILY_COVERAGE_GAPS_INDEX = "daily_coverage_gaps-test"
UNITTEST_SOURCE_HOST_INDEX = "source_host-test"

ALIAS_LOOKUP = {
    UNITTEST_MERGED_ASSET_INDEX: "merged_asset-test-current",
    UNITTEST_MERGED_ASSET_BACKUP_INDEX: "merged_asset-test-backup-current",
    UNITTEST_PERSISTENT_STATS_INDEX: "persistent_stats-test-current",
    UNITTEST_DAILY_COVERAGE_GAPS_INDEX: "daily_coverage_gaps-test-current",
    UNITTEST_SOURCE_HOST_INDEX: "source_host-test-current",
}

TEST_INDICES = [
    UNITTEST_MERGED_ASSET_INDEX,
    UNITTEST_MERGED_ASSET_BACKUP_INDEX,
    UNITTEST_PERSISTENT_STATS_INDEX,
    UNITTEST_DAILY_COVERAGE_GAPS_INDEX,
    UNITTEST_SOURCE_HOST_INDEX,
]

OPENSEARCH_FIXTURE_TYPES = {
    "assets.MergedAsset": MergedAsset,
}

TIME_KEYS = [
    "created",
    "updated",
    "asset.last_seen",
]


def fix_inner_metadata(source, model_name, now):
    if model_name == "assets.MergedAsset":
        for asset in source["source_data"].values():
            metadata = asset["metadata"]
            fix_metadata_times(metadata, now)


def fix_metadata_times(metadata, now):
    for key in TIME_KEYS:
        parts = key.split(".")
        lookup = metadata
        for sub_key in parts[:-1]:
            lookup = lookup.get(sub_key)

        final_key = parts[-1]
        if lookup.get(final_key) == "now":
            lookup[final_key] = now


class ESCaseMixin:
    # Default indexes names to be patched. Can be redefined in children.
    # mock.patch(...) is sensible to an order of imported modules,
    # so sometimes it requires different object paths
    # (e.g. local imports instead of global ones).
    indices_to_patch = (
        (
            "apps.assets.models.merged_asset.MergedAssetElasticSearchManager.INDEX_READ",
            UNITTEST_MERGED_ASSET_INDEX,
        ),
        (
            "apps.assets.models.merged_asset.MergedAssetElasticSearchManager.INDEX_WRITE",
            UNITTEST_MERGED_ASSET_INDEX,
        ),
        (
            "apps.assets.models.merged_asset.MergedAssetElasticSearchManager.INDEX_BACKUP",
            UNITTEST_MERGED_ASSET_BACKUP_INDEX,
        ),
        (
            "apps.stats.models.persistent_stats.PersistentStatManager.INDEX_READ",
            UNITTEST_PERSISTENT_STATS_INDEX,
        ),
        (
            "apps.stats.models.persistent_stats.PersistentStatManager.INDEX_WRITE",
            UNITTEST_PERSISTENT_STATS_INDEX,
        ),
        (
            "apps.stats.models.daily_coverage_gaps.DailyCoverageGapsManager.INDEX_READ",
            UNITTEST_DAILY_COVERAGE_GAPS_INDEX,
        ),
        (
            "apps.stats.models.daily_coverage_gaps.DailyCoverageGapsManager.INDEX_WRITE",
            UNITTEST_DAILY_COVERAGE_GAPS_INDEX,
        ),
        (
            "apps.assets.models.source_host.SourceHostElasticSearchManager.INDEX_READ",
            UNITTEST_SOURCE_HOST_INDEX,
        ),
        (
            "apps.assets.models.source_host.SourceHostElasticSearchManager.INDEX_WRITE",
            UNITTEST_SOURCE_HOST_INDEX,
        ),
    )

    es_fixtures = []

    @staticmethod
    def index_fixture(fixture):
        with open(os.path.join(settings.FIXTURE_DIRS[0], "es", fixture), "r") as f:
            now = timezone.now()

            docs = json.load(f)
            actions = []
            for doc in docs:
                model_name = doc["model"]
                model = OPENSEARCH_FIXTURE_TYPES[model_name]
                source = doc["source"]
                metadata = source.get("metadata")
                fix_metadata_times(metadata, now)
                fix_inner_metadata(source, model_name, now)

                actions.append(
                    {
                        "_op_type": "create",  # fails if _id already exists
                        "_index": model.documents.INDEX_WRITE,
                        "_source": doc["source"],
                    }
                )

            bulk(model.documents.client, actions, refresh=True)

    @staticmethod
    def delete_indices():
        for index in TEST_INDICES:
            es_service.es.indices.delete(index=index, ignore=404)

    @staticmethod
    def clear_indices():
        delete = {"query": {"match_all": {}}}
        for index in TEST_INDICES:
            es_service.es.delete_by_query(index=index, body=delete, refresh=True)

    @staticmethod
    def create_indices_and_aliases():
        for index in TEST_INDICES:
            es_service.es.indices.create(index=index)
            alias = ALIAS_LOOKUP[index]
            es_service.es.indices.put_alias(index=index, name=alias)

    @classmethod
    def patch_refresh(cls):
        patcher = patch(
            "apps.assets.models.merged_asset.MergedAssetElasticSearchManager.REFRESH",
            "true",
        )
        patcher.start()
        cls.addClassCleanup(patcher.stop)

        patcher = patch(
            "apps.stats.models.persistent_stats.PersistentStatManager.REFRESH", "true"
        )
        patcher.start()
        cls.addClassCleanup(patcher.stop)

        patcher = patch(
            "apps.stats.models.daily_coverage_gaps.DailyCoverageGapsManager.REFRESH",
            "true",
        )
        patcher.start()
        cls.addClassCleanup(patcher.stop)

        patcher = patch(
            "apps.assets.models.source_host.SourceHostElasticSearchManager.REFRESH",
            "true",
        )
        patcher.start()
        cls.addClassCleanup(patcher.stop)

    @classmethod
    def create_index_patchers(cls, empty=False, target_only=None):
        cls.index_patcher = []
        for target, replacement in cls.indices_to_patch:
            if target_only and target != target_only:
                continue

            if empty and target.endswith("READ"):
                replacement = "non-existent-index-*"

            patcher = patch(target, replacement)
            patcher.start()
            cls.addClassCleanup(patcher.stop)

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.create_index_patchers()
        cls.delete_indices()
        es_manager.configure_mappings(develop=True)
        cls.create_indices_and_aliases()
        cls.patch_refresh()

    def setUp(self):
        super().setUp()
        self.clear_indices()
        for fixture in self.es_fixtures:
            self.index_fixture(fixture)
