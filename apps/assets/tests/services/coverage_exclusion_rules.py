from django.test import TestCase

from apps.assets.models import HostType
from apps.assets.services import coverage_exclusion_rules
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import MergedSourceHostFactory


class CoverageExclusionRulesTest(TestCase):
    def test_exclude_endpoint_security_blank_hostname(self):
        merged_asset = MergedHostFactory(
            source_assets=[MergedSourceHostFactory(attributes__hostname="")]
        )
        self.assertTrue(
            coverage_exclusion_rules.is_endpoint_security_excluded(merged_asset)
        )

    def test_exclude_endpoint_security_printer_operating_systems(self):
        merged_asset = MergedHostFactory(
            source_assets=[
                MergedSourceHostFactory(attributes__os__name="Lexmark Printer")
            ]
        )
        self.assertTrue(
            coverage_exclusion_rules.is_endpoint_security_excluded(merged_asset)
        )

    def test_exclude_endpoint_security_camera_operating_systems(self):
        merged_asset = MergedHostFactory(
            source_assets=[
                MergedSourceHostFactory(attributes__os__name="Axis Network Camera")
            ]
        )
        self.assertTrue(
            coverage_exclusion_rules.is_endpoint_security_excluded(merged_asset)
        )

    def test_exclude_endpoint_security_mobile_operating_systems(self):
        merged_asset = MergedHostFactory(
            source_assets=[
                MergedSourceHostFactory(attributes__os__host_type=HostType.MOBILE)
            ]
        )
        self.assertTrue(
            coverage_exclusion_rules.is_endpoint_security_excluded(merged_asset)
        )

    def test_include_endpoint_security(self):
        merged_asset = MergedHostFactory(
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="defender_atp"
                )
            ]
        )
        self.assertFalse(
            coverage_exclusion_rules.is_endpoint_security_excluded(merged_asset)
        )

    def test_include_endpoint_security_if_any_not_excluded(self):
        merged_asset = MergedHostFactory(
            source_assets=[
                MergedSourceHostFactory(attributes__hostname=""),
                MergedSourceHostFactory(
                    metadata__integration__technology_id="defender_atp"
                ),
            ]
        )
        self.assertFalse(
            coverage_exclusion_rules.is_endpoint_security_excluded(merged_asset)
        )
