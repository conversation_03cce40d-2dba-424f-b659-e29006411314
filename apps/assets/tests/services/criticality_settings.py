from unittest import TestCase

from apps.assets.models.merged_source_asset import (
    AssetCriticality,
    HostType,
    OsAttributes,
    OsFamily,
)
from apps.assets.models.setting import SettingKey
from apps.assets.services.criticality_settings import AssetCriticalitySettings
from apps.tests.base import ApiDatabaseTestCase
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import MergedSourceHostFactory
from factories.setting import SettingFactory


class MergedAssetCriticalitySettingsTestCase(TestCase):
    def test_default_criticality_linux(self):
        asset = MergedSourceHostFactory(
            attributes__criticality=AssetCriticality.UNKNOWN,
            attributes__os=OsAttributes(HostType.SERVER, OsFamily.LINUX, "Red Hat"),
        )
        merged_host = MergedHostFactory(source_assets=[asset])
        criticality_settings = AssetCriticalitySettings()
        criticality_settings.linux_criticality = AssetCriticality.TIER_2
        criticality_settings.linux_major_server_distros = ["Red Hat"]
        criticality_settings.assign_criticality(merged_host)
        self.assertEqual(AssetCriticality.TIER_2, merged_host.merged_data.criticality)

    def test_default_criticality_windows_server(self):
        asset = MergedSourceHostFactory(
            attributes__criticality=AssetCriticality.UNKNOWN,
            attributes__os=OsAttributes(
                HostType.SERVER, OsFamily.WINDOWS, "Windows Server 2019"
            ),
        )
        merged_host = MergedHostFactory(source_assets=[asset])
        criticality_settings = AssetCriticalitySettings()
        criticality_settings.windows_server_criticality = AssetCriticality.TIER_3
        criticality_settings.assign_criticality(merged_host)
        self.assertEqual(AssetCriticality.TIER_3, merged_host.merged_data.criticality)

    def test_default_criticality_windows_workstation(self):
        asset = MergedSourceHostFactory(
            attributes__criticality=AssetCriticality.UNKNOWN,
            attributes__os=OsAttributes(
                HostType.WORKSTATION, OsFamily.WINDOWS, "Windows 10"
            ),
        )
        merged_host = MergedHostFactory(source_assets=[asset])
        criticality_settings = AssetCriticalitySettings()
        criticality_settings.windows_workstation_criticality = AssetCriticality.TIER_4
        criticality_settings.assign_criticality(merged_host)
        self.assertEqual(AssetCriticality.TIER_4, merged_host.merged_data.criticality)

    def test_default_criticality_mac_os(self):
        asset = MergedSourceHostFactory(
            attributes__criticality=AssetCriticality.UNKNOWN,
            attributes__os=OsAttributes(HostType.WORKSTATION, OsFamily.MAC, "Mac OS X"),
        )
        merged_host = MergedHostFactory(source_assets=[asset])
        criticality_settings = AssetCriticalitySettings()
        criticality_settings.mac_os_criticality = AssetCriticality.TIER_0
        criticality_settings.assign_criticality(merged_host)
        self.assertEqual(AssetCriticality.TIER_0, merged_host.merged_data.criticality)

    def test_default_criticality_exsisting(self):
        asset = MergedSourceHostFactory(
            attributes__criticality=AssetCriticality.TIER_1,
            attributes__os=OsAttributes(HostType.SERVER, OsFamily.LINUX, "Red Hat"),
        )
        merged_host = MergedHostFactory(source_assets=[asset])
        criticality_settings = AssetCriticalitySettings()
        criticality_settings.linux_criticality = AssetCriticality.TIER_2
        criticality_settings.linux_major_server_distros = ["Red Hat"]
        criticality_settings.assign_criticality(merged_host)
        self.assertEqual(AssetCriticality.TIER_1, merged_host.merged_data.criticality)

    def test_default_criticality_no_os(self):
        asset = MergedSourceHostFactory(
            attributes__criticality=AssetCriticality.UNKNOWN,
            attributes__os=OsAttributes(HostType.UNKNOWN, OsFamily.UNKNOWN, None),
        )
        merged_host = MergedHostFactory(source_assets=[asset])
        criticality_settings = AssetCriticalitySettings()
        criticality_settings.linux_criticality = AssetCriticality.TIER_2
        criticality_settings.linux_major_server_distros = ["Red Hat"]
        criticality_settings.assign_criticality(merged_host)
        self.assertEqual(AssetCriticality.UNKNOWN, merged_host.merged_data.criticality)

    def test_disabled_criticality(self):
        asset = MergedSourceHostFactory(
            attributes__criticality=AssetCriticality.UNKNOWN,
            attributes__os=OsAttributes(HostType.SERVER, OsFamily.LINUX, "Red Hat"),
        )
        merged_host = MergedHostFactory(source_assets=[asset])
        criticality_settings = AssetCriticalitySettings()
        criticality_settings.linux_criticality = None
        criticality_settings.linux_major_server_distros = ["Red Hat"]
        criticality_settings.assign_criticality(merged_host)
        self.assertEqual(AssetCriticality.UNKNOWN, merged_host.merged_data.criticality)

    def test_assign_criticality_with_none_os_name(self):
        asset = MergedSourceHostFactory(
            attributes__criticality=AssetCriticality.UNKNOWN,
            attributes__os=OsAttributes(HostType.SERVER, OsFamily.LINUX, None),
        )
        merged_host = MergedHostFactory(source_assets=[asset])
        criticality_settings = AssetCriticalitySettings()
        criticality_settings.linux_criticality = AssetCriticality.TIER_2
        criticality_settings.linux_major_server_distros = ["Red Hat"]
        # Should not raise and should not set criticality to TIER_2 since os.name is None
        criticality_settings.assign_criticality(merged_host)
        self.assertEqual(AssetCriticality.UNKNOWN, merged_host.merged_data.criticality)


class AssetCriticalityForOrganizationTestCase(ApiDatabaseTestCase):
    def test_asset_criticality_settings_enabled(self):
        SettingFactory.create(
            category="asset_criticality",
            key=SettingKey.OS_LINUX_DISTROS,
            default_value={
                "enabled": True,
                "criticality": AssetCriticality.TIER_2,
                "distros": ["Red Hat"],
            },
        )
        ac_settings = AssetCriticalitySettings.for_organization(self.organization.id)
        self.assertEqual(ac_settings.linux_criticality, AssetCriticality.TIER_2)
        self.assertEqual(ac_settings.linux_major_server_distros, ["Red Hat"])

        SettingFactory.create(
            category="asset_criticality",
            key=SettingKey.OS_WINDOWS_SERVER,
            default_value={"enabled": True, "criticality": AssetCriticality.TIER_1},
        )
        ac_settings = AssetCriticalitySettings.for_organization(self.organization.id)
        self.assertEqual(
            ac_settings.windows_server_criticality, AssetCriticality.TIER_1
        )

        SettingFactory.create(
            category="asset_criticality",
            key=SettingKey.OS_WINDOWS_WORKSTATION,
            default_value={"enabled": True, "criticality": AssetCriticality.TIER_3},
        )
        ac_settings = AssetCriticalitySettings.for_organization(self.organization.id)
        self.assertEqual(
            ac_settings.windows_workstation_criticality, AssetCriticality.TIER_3
        )

        SettingFactory.create(
            category="asset_criticality",
            key=SettingKey.OS_MAC,
            default_value={"enabled": True, "criticality": AssetCriticality.TIER_0},
        )
        ac_settings = AssetCriticalitySettings.for_organization(self.organization.id)
        self.assertEqual(ac_settings.mac_os_criticality, AssetCriticality.TIER_0)

    def test_asset_criticality_settings_disabled(self):
        SettingFactory.create(
            category="asset_criticality",
            key=SettingKey.OS_MAC,
            default_value={"enabled": False, "criticality": AssetCriticality.TIER_2},
        )
        ac_settings = AssetCriticalitySettings.for_organization(self.organization.id)
        self.assertEqual(ac_settings.mac_os_criticality, None)
