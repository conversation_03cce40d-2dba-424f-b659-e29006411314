import logging

from django.core.management import call_command

from apps.assets.models import (
    AssetCriticality,
    CoverageCategory,
    MergedAsset,
    <PERSON>sF<PERSON>ly,
    Setting,
    SettingKey,
)
from apps.assets.services import merge_service
from apps.assets.tests.es_base import ESCaseMixin
from apps.tests.base import BaseTestCase
from factories.integration import IntegrationFactory
from factories.setting import SettingFactory
from factories.staged_host import StagedHostFactory


class MergeServiceTestCase(ESCaseMixin, BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.set_logging(True)
        call_command("sync_merging_rules")

    def test_merged_asset_created_and_deleted(self):
        """
        The MergeHelper tracks the operation to be performed on the merged asset
        during the merge process. If a merged asset is created and then deleted
        in the same merge operation, the MergeHelper should ignore both the creation
        and deletion operations.

        In the setup below, the merge process will create a merged asset for #1 and
        #2, and then delete on of them when processing #3 matches both #1 and #2.

        ******************************************************************************
        NOTE: The description above is no longer accurate, but the test is still valid.
        ******************************************************************************
        """
        integration = IntegrationFactory(organization=self.organization)
        staged_host1 = StagedHostFactory(
            hostname="host1", mac_addresses=["f0:20:ff:5a:75:f8"]
        )
        staged_host2 = StagedHostFactory(
            hostname="host2", mac_addresses=["f0:20:ff:5a:75:f7"]
        )
        staged_host3 = StagedHostFactory(
            hostname="host2", mac_addresses=["f0:20:ff:5a:75:f8", "f0:20:ff:5a:75:f7"]
        )

        merged_asset = self.merge_staged_assets(
            integration, [staged_host1, staged_host2, staged_host3], 1
        )
        self.assertEqual(3, merged_asset.source_data.count())

    def test_auto_assign_criticality(self):
        integration = IntegrationFactory(organization=self.organization)
        SettingFactory.create(
            category=Setting.Category.ASSET_CRITICALITY,
            key=SettingKey.OS_MAC,
            default_value={"enabled": True, "criticality": AssetCriticality.TIER_1},
        )
        staged_host = StagedHostFactory(
            hostname="host1",
            criticality=AssetCriticality.UNKNOWN,
            os__family=OsFamily.MAC,
        )
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assert_auto_criticality(merged_asset, AssetCriticality.TIER_1)

    def test_auto_assign_criticality_unchanged_asset(self):
        # First merge without any criticality settings
        integration = IntegrationFactory(organization=self.organization)
        staged_host = StagedHostFactory(
            hostname="host1",
            criticality=AssetCriticality.UNKNOWN,
            os__family=OsFamily.MAC,
        )
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assert_auto_criticality(merged_asset, AssetCriticality.UNKNOWN)
        #######################################################################

        # Second merge the same asset again (unchanged) with criticality settings
        setting = SettingFactory.create(
            category=Setting.Category.ASSET_CRITICALITY,
            key=SettingKey.OS_MAC,
            default_value={"enabled": True, "criticality": AssetCriticality.TIER_1},
        )
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assert_auto_criticality(merged_asset, AssetCriticality.TIER_1)
        #######################################################################

        # Third merge the same asset again (unchanged) with different criticality settings
        setting.default_value = {
            "enabled": True,
            "criticality": AssetCriticality.TIER_2,
        }
        setting.save()
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assert_auto_criticality(merged_asset, AssetCriticality.TIER_2)

    def test_auto_assign_criticality_revert_to_source_criticality(self):
        crowdstrike_integration = IntegrationFactory(
            organization=self.organization, technology_id="crowdstrike_falcon"
        )
        SettingFactory.create(
            category=Setting.Category.ASSET_CRITICALITY,
            key=SettingKey.OS_MAC,
            default_value={"enabled": True, "criticality": AssetCriticality.TIER_4},
        )
        staged_host = StagedHostFactory(
            hostname="host1",
            criticality=AssetCriticality.UNKNOWN,
            mac_addresses=[
                "f0:20:ff:5a:75:f8"
            ],  # set to trigger merge with second asset
            os__family=OsFamily.MAC,
        )
        merged_asset = self.merge_staged_assets(
            crowdstrike_integration, [staged_host], 1
        )

        self.assert_auto_criticality(merged_asset, AssetCriticality.TIER_4)
        #######################################################################

        # Second merge an asset from a different source with a criticality
        qualys_integration = IntegrationFactory(
            organization=self.organization, technology_id="qualys_vmpc"
        )
        staged_host = StagedHostFactory(
            hostname="host1",
            criticality=AssetCriticality.TIER_2,
            mac_addresses=["f0:20:ff:5a:75:f8"],
            os__family=OsFamily.MAC,
        )
        merged_asset = self.merge_staged_assets(qualys_integration, [staged_host], 1)

        self.assertEqual(merged_asset.merged_data.criticality, AssetCriticality.TIER_2)

    def test_auto_assign_criticality_one_of_two_assets(self):
        integration = IntegrationFactory(organization=self.organization)
        SettingFactory.create(
            category=Setting.Category.ASSET_CRITICALITY,
            key=SettingKey.OS_MAC,
            default_value={"enabled": True, "criticality": AssetCriticality.TIER_1},
        )
        staged_host1 = StagedHostFactory(
            hostname="host1",
            criticality=AssetCriticality.UNKNOWN,
            os__family=OsFamily.MAC,
        )
        staged_host2 = StagedHostFactory(
            hostname="host2",
            criticality=AssetCriticality.TIER_2,
            os__family=OsFamily.MAC,
        )

        merged_assets = self.merge_staged_assets(
            integration, [staged_host1, staged_host2], 2
        )

        host1 = next(h for h in merged_assets if h.merged_data.hostname == "host1")
        host2 = next(h for h in merged_assets if h.merged_data.hostname == "host2")
        self.assert_auto_criticality(host1, AssetCriticality.TIER_1)
        self.assertEqual(host2.merged_data.criticality, AssetCriticality.TIER_2)

    def test_vulnerability_coverage_mode_changed(self):
        integration = IntegrationFactory(
            organization=self.organization,
            technology_id="qualys_vmpc",
            vulnerability_coverage_mode="ignore",
            endpoint_coverage_mode="ignore",
        )
        staged_host = StagedHostFactory(
            hostname="host1",
            criticality=AssetCriticality.UNKNOWN,
            os__family=OsFamily.MAC,
        )
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assertEqual(
            sorted(merged_asset.coverage_gaps),
            [
                CoverageCategory.ENDPOINT_SECURITY,
                CoverageCategory.VULNERABILITY_MANAGEMENT,
            ],
        )

        # Change the vulnerability coverage mode
        integration.vulnerability_coverage_mode = "enabled"
        integration.save()
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assertEqual(
            merged_asset.coverage_gaps,
            [
                CoverageCategory.ENDPOINT_SECURITY,
            ],
        )

    def test_endpoint_coverage_mode_changed(self):
        integration = IntegrationFactory(
            organization=self.organization,
            technology_id="carbon_black",
            vulnerability_coverage_mode="ignore",
            endpoint_coverage_mode="ignore",
        )
        staged_host = StagedHostFactory(
            hostname="host1",
            criticality=AssetCriticality.UNKNOWN,
            os__family=OsFamily.MAC,
        )
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assertEqual(
            sorted(merged_asset.coverage_gaps),
            [
                CoverageCategory.ENDPOINT_SECURITY,
                CoverageCategory.VULNERABILITY_MANAGEMENT,
            ],
        )

        # Change the endpoint_coverage_mode
        integration.endpoint_coverage_mode = "enabled"
        integration.save()
        merged_asset = self.merge_staged_assets(integration, [staged_host], 1)

        self.assertEqual(
            merged_asset.coverage_gaps,
            [
                CoverageCategory.VULNERABILITY_MANAGEMENT,
            ],
        )

    def merge_staged_assets(self, integration, staged_hosts, expected_length):
        merge_service.update_source_assets(
            integration,
            staged_hosts,
            "Host",
        )

        merged_asset_ids = merge_service.update_merged_asset_ids(
            integration.organization,
            "Host",
        )

        merge_service.reconcile_merged_assets(
            integration.organization_id,
            merged_asset_ids,
        )

        # Retrieve and return the merged asset(s)
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), expected_length)
        if expected_length == 1:
            return merged_assets[0]
        return merged_assets

    def assert_auto_criticality(self, merged_asset, expected_criticality):
        self.assertEqual(merged_asset.merged_data.criticality, expected_criticality)
        self.assertFalse(merged_asset.source_data.has_criticality())

    @staticmethod
    def set_logging(disabled):
        logging.getLogger(
            "apps.assets.services.merging.merge_service"
        ).disabled = disabled
        logging.getLogger("opensearch").disabled = disabled
