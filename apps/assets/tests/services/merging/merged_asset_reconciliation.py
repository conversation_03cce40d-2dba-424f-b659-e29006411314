import logging
from uuid import uuid4

from django.core.management import call_command

from apps.assets.models import (
    IdentificationRule,
    MergedAsset,
    ReconciliationRule,
)
from apps.assets.models.source_host import SourceHost
from apps.assets.services import MergedAssetReconciliation, SourceHostIdentification
from apps.assets.services.criticality_settings import AssetCriticalitySettings
from apps.assets.tests.es_base import ESCaseMixin
from apps.integrations.models import Integration
from apps.tests.base import BaseTestCase
from factories.integration import IntegrationFactory, TechnologyFactory
from factories.source_host import (
    IntegrationMetaFactory,
    SourceHostFactory,
)


class MergedAssetReconciliationTestCase(ESCaseMixin, BaseTestCase):
    @classmethod
    def setUpClass(cls):
        cls.set_logging(disabled=True)
        super().setUpClass()
        call_command("sync_merging_rules")

    def setUp(self):
        super().setUp()
        self.criticality_settings = AssetCriticalitySettings()

        from apps.integrations.models import Technology
        crowdstrike_tech, _ = Technology.objects.get_or_create(
            technology_id="crowdstrike_falcon",
            defaults={'name': 'CrowdStrike Falcon'}
        )
        self.crowdstrike = IntegrationFactory.create(
            organization=self.organization,
            technology=crowdstrike_tech,
        )
        self.crowdstrike_meta = IntegrationMetaFactory.from_model(self.crowdstrike)

        qualys_tech, _ = Technology.objects.get_or_create(
            technology_id="qualys_vmpc",
            defaults={'name': 'Qualys VMPC'}
        )
        self.qualys_vmpc = IntegrationFactory.create(
            organization=self.organization,
            technology=qualys_tech,
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        self.qualys_vmpc_meta = IntegrationMetaFactory.from_model(self.qualys_vmpc)

        identification_rule = IdentificationRule.objects.filter(
            asset_type="host"
        ).first()
        self.id_rule_entries = [
            (identification_rule, entry) for entry in identification_rule.entries.all()
        ]

        rec_rules = ReconciliationRule.objects.filter(asset_type="host")
        self.rec_rule_entries = [(rule, list(rule.entries.all())) for rule in rec_rules]

    def test_update_deleted_merged_asset(self):
        self.set_logging(disabled=False)

        merged_asset_id = str(uuid4())

        reconciliation_service = MergedAssetReconciliation(
            self.organization,
            self.rec_rule_entries,
            self.criticality_settings,
        )

        with self.assertLogs(
            "apps.assets.services.merging.merged_asset_reconciliation",
            level="WARNING",
        ):
            reconciliation_service.reconcile_merged_assets([merged_asset_id])

        merged_assets = self.get_all_merged_assets()
        self.assertEqual(0, len(merged_assets))
        self.set_logging(disabled=True)

    def test_update_non_existing_merged_asset(self):
        self.set_logging(disabled=False)

        merged_asset_id = str(uuid4())
        macs = ["20:e9:b4:ad:5a:08"]
        SourceHostFactory.create(
            attributes__universal_mac_addresses=macs,
            integration=self.crowdstrike_meta,
            merged_asset__id=merged_asset_id,
        )
        SourceHostFactory.create(
            attributes__universal_mac_addresses=macs,
            integration=self.qualys_vmpc_meta,
            merged_asset__id=merged_asset_id,
        )

        # Run reconciliation before creating the merged asset stub.
        # This should not result in an error, but also not create a merged asset,
        # because create is not the responsibility of the reconciliation service.
        reconciliation_service = MergedAssetReconciliation(
            self.organization,
            self.rec_rule_entries,
            self.criticality_settings,
        )

        with self.assertLogs(
            "apps.assets.services.merging.merged_asset_reconciliation",
            level="WARNING",
        ):
            reconciliation_service.reconcile_merged_assets([merged_asset_id])

        merged_assets = self.get_all_merged_assets()
        self.assertEqual(0, len(merged_assets))
        self.set_logging(disabled=True)

    def test_update_existing_merged_asset(self):
        macs = ["20:e9:b4:ad:5a:08"]
        crowdstrike_host = SourceHostFactory.create(
            attributes__universal_mac_addresses=macs,
            integration=self.crowdstrike_meta,
            merged_asset=None,
        )
        SourceHostFactory.create(
            attributes__universal_mac_addresses=macs,
            integration=self.qualys_vmpc_meta,
            merged_asset=None,
        )

        identification = SourceHostIdentification(
            self.organization,
            self.id_rule_entries,
            {},
        )
        merged_asset_ids = identification.update_merged_asset_ids()

        reconciliation_service = MergedAssetReconciliation(
            self.organization,
            self.rec_rule_entries,
            self.criticality_settings,
        )

        reconciliation_service.reconcile_merged_assets(merged_asset_ids)

        merged_asset = self.get_all_merged_assets()[0]
        self.assertEqual(2, len(merged_asset.source_data.all()))
        ma_source = merged_asset.source_data.get_sources_by("crowdstrike_falcon")[0]
        self.assertEqual(
            crowdstrike_host.attributes.hostname,
            ma_source.attributes.hostname,
        )

        # Update the SourceHost
        crowdstrike_host.attributes.hostname = "new-hostname"
        SourceHost.documents.update_bulk(
            [crowdstrike_host], fields=SourceHost.documents.get_update_fetched_fields()
        )

        # Reconcile again
        reconciliation_service = MergedAssetReconciliation(
            self.organization,
            self.rec_rule_entries,
            self.criticality_settings,
        )

        reconciliation_service.reconcile_merged_assets(merged_asset_ids)

        merged_assets = self.get_all_merged_assets()
        self.assertEqual(1, len(merged_assets))
        merged_asset = merged_assets[0]
        ma_source = merged_asset.source_data.get_sources_by("crowdstrike_falcon")[0]
        self.assertEqual(
            "new-hostname",
            ma_source.attributes.hostname,
        )

    def get_all_merged_assets(self):
        return list(
            MergedAsset.documents.get_by_org_asset_type(self.organization.id, "host")
        )

    @staticmethod
    def set_logging(disabled=False):
        logger_names = [
            "opensearch",
            "apps.assets.services.merging.merged_asset_reconciliation",
        ]
        for logger_name in logger_names:
            logging.getLogger(logger_name).disabled = disabled
