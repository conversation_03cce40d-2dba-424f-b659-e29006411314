import logging
from uuid import uuid4

from django.core.management import call_command

from apps.assets.models import IdentificationRule
from apps.assets.models.source_host import SourceHost
from apps.assets.services import SourceHostIdentification
from apps.assets.services.purge_automation_settings import PurgeAutomationSettings
from apps.assets.tests.es_base import ESCaseMixin
from apps.tests.base import BaseTestCase
from factories.integration import IntegrationFactory
from factories.source_host import IntegrationMetaFactory, SourceHostFactory


class SourceHostIdentificationTestCase(ESCaseMixin, BaseTestCase):
    @classmethod
    def setUpClass(cls):
        cls.set_logging(disabled=True)
        super().setUpClass()
        call_command("sync_merging_rules")

    def setUp(self):
        super().setUp()
        self.purge_automation_settings = PurgeAutomationSettings()
        self.integration = IntegrationFactory.create(organization=self.organization)
        self.integration_meta = IntegrationMetaFactory.from_model(self.integration)

        identification_rule = IdentificationRule.objects.filter(
            asset_type="host"
        ).first()
        self.id_rule_entries = [
            (identification_rule, entry) for entry in identification_rule.entries.all()
        ]

    def test_no_data(self):
        identification = SourceHostIdentification(
            self.organization, self.id_rule_entries, {}
        )

        identification.update_merged_asset_ids()

        self.assertTrue(True, "No data should not raise an exception")

    def test_new_host(self):
        host = self.update_single_host("B")

        self.assertEqual("b", host.attributes.hostname)
        self.assertIsNotNone(host.attributes)
        self.assertIsNotNone(host.merged_asset)
        self.assertFalse(host.merged_asset.manually_merged)

    def test_existing_merged_asset(self):
        expected_uuid = str(uuid4())

        host = self.update_single_host("A", merged_asset_id=expected_uuid)

        self.assertIsNotNone(host.attributes)
        self.assertIsNotNone(host.merged_asset)
        self.assertEqual(expected_uuid, host.merged_asset.id)

    def test_existing_manually_merged(self):
        host = self.update_single_host("A", manually_merged=True)

        self.assertIsNotNone(host.attributes)
        self.assertIsNotNone(host.merged_asset)
        self.assertTrue(host.merged_asset.manually_merged)

    def test_merge_two_hosts(self):
        SourceHostFactory.create_batch(
            2,
            attributes__universal_mac_addresses=["f0:20:ff:5a:75:f8"],
            integration=self.integration_meta,
        )

        self.run_update_merged_asset_ids()

        hosts = list(SourceHost.documents.get_by_organization(self.organization.id))
        self.assert_num_merged_assets(1, hosts)

    def test_merge_host_with_manually_merged(self):
        # Create two source hosts that were manually merged
        merged_asset_id_1 = uuid4()
        SourceHostFactory.create(
            attributes__universal_mac_addresses=["f0:20:ff:5a:75:f8"],
            integration=self.integration_meta,
            merged_asset__id=merged_asset_id_1,
            merged_asset__manually_merged=True,
        )
        SourceHostFactory.create(
            attributes__universal_mac_addresses=["ff:30:f2:5b:65:f7"],
            integration=self.integration_meta,
            merged_asset__id=merged_asset_id_1,
            merged_asset__manually_merged=True,
        )

        # Create another source host with the same mac address
        merged_asset_id_2 = uuid4()
        SourceHostFactory.create(
            attributes__universal_mac_addresses=["f0:20:ff:5a:75:f8"],
            integration=self.integration_meta,
            merged_asset__id=merged_asset_id_2,
            merged_asset__manually_merged=False,
        )

        self.run_update_merged_asset_ids()

        hosts = list(SourceHost.documents.get_by_organization(self.organization.id))
        self.assert_num_merged_assets(1, hosts)
        self.assertTrue(all(host.merged_asset.manually_merged for host in hosts))

    def test_excluded_field_values(self):
        # Create two source hosts with the same mac address and the same
        # merged asset id.
        merged_asset_id = uuid4()
        SourceHostFactory.create(
            attributes__universal_mac_addresses=["ff:30:f2:5b:65:f7"],
            integration=self.integration_meta,
            merged_asset__id=merged_asset_id,
        )
        SourceHostFactory.create(
            attributes__universal_mac_addresses=["ff:30:f2:5b:65:f7"],
            integration=self.integration_meta,
            merged_asset__id=merged_asset_id,
        )

        # Exclude the mac address from being used to merge assets
        identification = SourceHostIdentification(
            self.organization,
            self.id_rule_entries,
            {"universal_mac_addresses": ["ff:30:f2:5b:65:f7"]},
        )

        identification.update_merged_asset_ids()

        # The hosts should not be merged
        hosts = list(SourceHost.documents.get_by_organization(self.organization.id))
        self.assert_num_merged_assets(2, hosts)

    def test_nand_exists_fields(self):
        SourceHostFactory.create_batch(
            2,
            attributes__hostname="A",
            attributes__universal_mac_addresses=[],
            attributes__local_mac_addresses=[],
            attributes__aad_id=None,
            integration=self.integration_meta,
        )

        self.run_update_merged_asset_ids()

        hosts = list(SourceHost.documents.get_by_organization(self.organization.id))
        self.assert_num_merged_assets(1, hosts)

    def test_best_merged_asset_id_jaccard_current_gtr(self):
        uuid1 = uuid4()
        uuid2 = uuid4()
        old_components = {uuid1: {"a", "b", "c", "d"}, uuid2: {"x", "y"}}
        new_component = {"x", "a"}
        identification_service = SourceHostIdentification(
            self.organization, self.id_rule_entries, {}
        )
        self.assertEqual(
            uuid2,
            identification_service._SourceHostIdentification__best_merged_asset_id(
                old_components, new_component
            ),
        )

    def update_single_host(self, hostname, merged_asset_id=None, manually_merged=None):
        create_kwargs = {
            "attributes__hostname": hostname,
            "integration": self.integration_meta,
        }

        if merged_asset_id:
            create_kwargs["merged_asset__id"] = merged_asset_id
        if manually_merged is not None:
            create_kwargs["merged_asset__manually_merged"] = manually_merged

        SourceHostFactory.create(**create_kwargs)

        self.run_update_merged_asset_ids()

        return next(SourceHost.documents.get_by_organization(self.organization.id))

    def run_update_merged_asset_ids(self):
        identification = SourceHostIdentification(
            self.organization, self.id_rule_entries, {}
        )

        identification.update_merged_asset_ids()

    def assert_num_merged_assets(self, num, hosts):
        merged_asset_ids = {host.merged_asset.id for host in hosts}
        self.assertNotIn(None, merged_asset_ids)
        self.assertEqual(num, len(merged_asset_ids))

    @staticmethod
    def set_logging(disabled=False):
        logger_names = [
            "opensearch",
            "apps.assets.services.merging.source_host_identification",
        ]
        for logger_name in logger_names:
            logging.getLogger(logger_name).disabled = disabled
