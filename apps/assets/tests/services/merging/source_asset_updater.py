import logging

from django.utils.timezone import now

from apps.assets.models.source_host import SourceHost
from apps.assets.services.merging.source_host_updater import SourceHostUpdater
from apps.assets.services.purge_automation_settings import PurgeAutomationSettings
from apps.assets.tests.es_base import ESCaseMixin
from apps.integrations.host.host_converter import SourceHostConverter
from apps.integrations.models import Integration
from apps.tests.base import BaseTestCase
from factories.integration import IntegrationFactory
from factories.staged_host import StagedHostFactory


class SourceAssetUpdaterTestCase(ESCaseMixin, BaseTestCase):
    @classmethod
    def setUpClass(cls):
        cls.set_logging(disabled=True)
        super().setUpClass()

    def setUp(self):
        super().setUp()
        self.purge_automation_settings = PurgeAutomationSettings()
        self.integration = IntegrationFactory.create(organization=self.organization)

    def test_update_integration_source_assets(self):
        staged_host = StagedHostFactory(hostname="B", last_seen="2021-01-01T00:00:00Z")

        def assert_common(h):
            last_seen = h.attributes.last_seen.isoformat().replace("+00:00", "Z")
            self.assertEqual("b", h.attributes.hostname)
            self.assertEqual(last_seen, "2021-01-01T00:00:00Z")
            self.assertEqual(str(self.integration.id), h.integration.id)
            self.assertEqual(str(self.organization.id), h.integration.organization_id)
            self.assertEqual("crowdstrike_falcon", h.integration.technology_id)

        # Create a source host
        host = self.update([staged_host])
        assert_common(host)
        self.assertEqual(host.created, host.updated)
        self.assertNotEqual(["test_group_name"], host.attributes.group_names)

        # Update the source host
        staged_host["group_names"] = ["test_group_name"]
        host = self.update([staged_host])
        assert_common(host)
        self.assertEqual(["test_group_name"], host.attributes.group_names)

    def test_update_integration_source_assets_no_change(self):
        staged_host = StagedHostFactory(hostname="B", last_seen="2021-01-01T00:00:00Z")
        host = self.update([staged_host])
        self.assertEqual(host.created, host.updated)

        expected_updated = host.updated

        self.update([staged_host])
        self.assertEqual(expected_updated, host.updated)

    def test_update_integration_source_assets_last_seen_changed(self):
        staged_host = StagedHostFactory(hostname="B", last_seen="2021-01-01T00:00:00Z")
        host = self.update([staged_host])
        self.assertEqual(host.created, host.updated)

        previous_updated = host.updated
        staged_host["last_seen"] = "2021-01-02T00:00:00Z"

        host = self.update([staged_host])
        self.assertGreater(host.updated, previous_updated)

    def test_update_integration_source_assets_last_seen_populated(self):
        # Ensure host is created with last_seen as None
        self.purge_automation_settings.days = 1

        staged_host = StagedHostFactory(hostname="B", last_seen=None)
        host = self.update([staged_host])
        self.assertIsNone(host.attributes.last_seen)
        self.assertEqual(host.created, host.updated)

        previous_updated = host.updated
        staged_host["last_seen"] = now()

        host = self.update([staged_host])
        self.assertIsNotNone(host.attributes.last_seen)
        self.assertGreater(host.updated, previous_updated)

    def test_update_integration_source_assets_integration_change(self):
        # Ensure host is created with last_seen
        self.purge_automation_settings.days = 1

        staged_host = StagedHostFactory(hostname="B", last_seen=now())
        host = self.update([staged_host])
        self.update([staged_host])
        self.assertEqual(
            self.integration.vulnerability_coverage_mode,
            Integration.CoverageMode.NOT_APPLICABLE,
        )

        previous_updated = host.updated

        self.integration.vulnerability_coverage_mode = Integration.CoverageMode.ENABLED
        host = self.update([staged_host])
        self.assertEqual(
            self.integration.vulnerability_coverage_mode,
            Integration.CoverageMode.ENABLED,
        )
        self.assertGreater(host.updated, previous_updated)

    def test_update_integration_source_assets_validation_failed(self):
        staged_host = StagedHostFactory(hostname="B", last_seen="2021-01-01T00:00:00Z")
        staged_host["criticality"] = "invalid"
        self.update([staged_host], assert_count=0)

    def test_purge_stale_source_exists(self):
        staged_host = StagedHostFactory(hostname="B", last_seen="2021-01-01T00:00:00Z")
        self.update([staged_host], assert_count=1)

        self.purge_automation_settings.days = 1
        self.update([staged_host], assert_count=0)

    def test_purge_stale_source_new(self):
        staged_host = StagedHostFactory(hostname="B", last_seen="2021-01-01T00:00:00Z")
        self.purge_automation_settings.days = 1
        self.update([staged_host], assert_count=0)

    def test_purge_not_in_source(self):
        staged_host = StagedHostFactory(hostname="B", last_seen="2021-01-01T00:00:00Z")
        self.update([staged_host], assert_count=1)

        self.purge_automation_settings.source_deleted = True
        self.update([], assert_count=0)

    def update(self, staged_hosts, assert_count=1):
        converter = SourceHostConverter()
        source_hosts = converter.try_convert(self.integration, staged_hosts)

        updater = SourceHostUpdater(self.organization, self.purge_automation_settings)
        updater.update_integration_source_assets(self.integration, source_hosts)
        hosts = SourceHost.documents.search({})
        self.assertEqual(assert_count, len(hosts))
        if assert_count == 1:
            return hosts[0]
        return hosts

    @staticmethod
    def set_logging(disabled=False):
        logging.getLogger("opensearch").disabled = disabled
