import datetime
from copy import deepcopy
from unittest.mock import patch

import criticalstart.data_connectors.sdk.models as dc
from django.utils.dateparse import parse_datetime

from apps.assets.merging_rules.reconciliation.rules import reconciliation_rules
from apps.assets.models import (
    AssetType,
    IdentificationRule,
    IdentifierEntry,
    ReconcilerEntry,
    ReconciliationRule,
    SourceHost,
)
from apps.assets.models.asset_field import save_to_database as save_excluded_values
from apps.assets.models.merged_asset import (
    AssetCriticality,
    MergedAsset,
    MergedAssetFieldGroups,
)
from apps.assets.models.merged_source_asset import AssetAttributes
from apps.assets.models.setting import SettingKey
from apps.assets.services import merge_service
from apps.assets.services.purge_automation_settings import (
    PurgeAutomationSettings,
)
from apps.assets.tests.es_base import ESCaseMixin
from apps.integrations.models import Integration
from apps.tests.base import BaseTestCase
from factories.integration import IntegrationFactory
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import MergedSourceHostFactory
from factories.setting import SettingFactory
from factories.staged_host import StagedHostFactory


class MergeServiceCustomRulesTestCase(ESCaseMixin, BaseTestCase):
    TIME_KEYS = [
        "approximateLastSignInDateTime",
    ]

    def setUp(self):
        super().setUp()

        save_excluded_values()

        self.integrations = {}

        self.integration_azure_ad = IntegrationFactory(
            id="ca47f69a-c1d3-4031-a781-45ea5b431fb6",
            technology_id="azure_ad",
            organization=self.organization,
        )
        self.integration_tenable_io = IntegrationFactory(
            id="898890c3-21c2-4b35-b069-983a0b5060ed",
            technology_id="tenable_io",
            organization=self.organization,
        )
        self.integration_defender_atp = IntegrationFactory(
            id="de62f69a-c1d3-4031-a781-45ea5b431fb6",
            technology_id="defender_atp",
            organization=self.organization,
        )
        for integration in [self.integration_azure_ad, self.integration_defender_atp]:
            self.integrations[str(integration.id)] = integration

        self.integration_sentinel_one1 = self.get_integration(
            "bc47f69a-c1d3-4031-a781-45ea5b431fb6"
        )
        self.integration_crowdstrike_falcon1 = self.get_integration(
            "bc47f69a-c1d3-4031-a781-45ea5b431fb7"
        )
        self.integration_sentinel_one2 = self.get_integration(
            "bc47f69a-c1d3-4031-a781-45ea5b431fb8"
        )
        self.integration_sentinel_falcon2 = self.get_integration(
            "bc47f69a-c1d3-4031-a781-45ea5b431fb9"
        )
        self.integration_csv1 = self.get_integration(
            "bc47f69a-c1d3-4031-a781-45ea5b431fc0"
        )

        self.identification_rule = IdentificationRule.objects.create(
            asset_type="host",
            organization=self.organization,
        )
        # TODO move all this to a factory
        self.reconciliation_rule_os = ReconciliationRule.objects.create(
            asset_type="host",
            field="os",
            organization=self.organization,
        )

        ReconcilerEntry.objects.create(
            reconciliation_rule=self.reconciliation_rule_os,
            priority=1,
            technology_id="crowdstrike_falcon",
        )

        ReconcilerEntry.objects.create(
            reconciliation_rule=self.reconciliation_rule_os,
            priority=2,
            technology_id="sentinel_one",
        )

        self.reconciliation_rule_criticality = ReconciliationRule.objects.create(
            asset_type="host",
            field="criticality",
            organization=self.organization,
        )

        self.reconciliation_rule_hostname = ReconciliationRule.objects.create(
            asset_type="host",
            field="hostname",
            organization=self.organization,
        )

        ReconcilerEntry.objects.create(
            reconciliation_rule=self.reconciliation_rule_hostname,
            priority=1,
            technology_id="crowdstrike_falcon",
        )

        ReconcilerEntry.objects.create(
            reconciliation_rule=self.reconciliation_rule_hostname,
            priority=2,
            technology_id="sentinel_one",
        )

    def get_integration(self, integration_id):
        integration = Integration.objects.get(id=integration_id)
        self.integrations[str(integration.id)] = integration
        return integration

    def test_merge_excludes_unsupported_assets(self):
        valid_asset = StagedHostFactory()

        invalid_asset: dc.Host = deepcopy(valid_asset)
        invalid_asset["hostname"] = ""
        merge_service.merge_staged_assets(
            self.integration_sentinel_one1,
            [invalid_asset],
            "Host",
        )
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertTrue(
            merged_assets[0].metadata.asset.technologies.endpoint_security_excluded
        )

        invalid_asset = deepcopy(valid_asset)
        invalid_asset["os"]["host_type"] = "container"
        merge_service.merge_staged_assets(
            self.integration_sentinel_one1,
            [invalid_asset],
            "Host",
        )
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertTrue(
            merged_assets[0].metadata.asset.technologies.endpoint_security_excluded
        )

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1,
            [valid_asset],
            "Host",
        )
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertFalse(
            merged_assets[0].metadata.asset.technologies.endpoint_security_excluded
        )

    def test_merge_identification(self):
        IdentifierEntry.objects.create(
            identification_rule=self.identification_rule,
            fields=["hostname"],
        )

        staged_assets = [
            StagedHostFactory(hostname="host1"),
            StagedHostFactory(hostname="host12"),
        ]

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, staged_assets, "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 2)

        staged_assets = [StagedHostFactory(hostname="host1")]

        merge_service.merge_staged_assets(
            self.integration_crowdstrike_falcon1, staged_assets, "Host"
        )

        merged_assets = MergedAsset.documents.search({})

        self.assertEqual(len(merged_assets), 2)
        merged_asset = next(filter(lambda x: x.source_data.count() == 2, merged_assets))
        self.assertEqual(
            merged_asset.source_data["sentinel_one"][0].attributes.hostname,
            merged_asset.source_data["crowdstrike_falcon"][0].attributes.hostname,
        )

        self.assertEqual(
            merged_asset.source_data["sentinel_one"][0].attributes.os,
            merged_asset.merged_data.os,
        )

    def test_merge_identification_excluded_value(self):
        IdentifierEntry.objects.create(
            identification_rule=self.identification_rule,
            fields=["universal_mac_addresses"],
        )

        staged_assets = [StagedHostFactory(mac_addresses=["00:05:9a:3c:7a:00"])]
        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, staged_assets, "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)

        staged_assets = [StagedHostFactory(mac_addresses=["00:05:9a:3c:7a:00"])]
        merge_service.merge_staged_assets(
            self.integration_crowdstrike_falcon1, staged_assets, "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        # New merged asset should be created, even though the mac address is the same,
        # because it is excluded
        self.assertEqual(len(merged_assets), 2)

    def test_merge_source_identification_change_new_asset(self):
        # Create a merged asset where the sources are CrowdStrike and SentinelOne
        self.test_merge_identification()

        merged_assets = MergedAsset.documents.search({})
        host1 = next(m for m in merged_assets if m.merged_data.hostname == "host1")
        source_to_move = next(
            s
            for s in host1.source_data.all()
            if s.metadata.integration.technology_id == "sentinel_one"
        )

        # Change the hostname in the SentinelOne source
        staged_asset = StagedHostFactory(
            source_id=source_to_move.metadata.asset.source_id,
            hostname="host1-changed",
            last_seen=source_to_move.last_seen + datetime.timedelta(minutes=1),
        )

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        # Check that the merged asset has the new hostname
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 3)
        self.assertTrue(all(m.source_data.count() == 1 for m in merged_assets))
        self.assertSetEqual(
            set(m.merged_data.hostname for m in merged_assets),
            {"host1-changed", "host1", "host12"},
        )

    def test_merge_source_identification_change_moved_all_assets(self):
        IdentifierEntry.objects.create(
            identification_rule=self.identification_rule,
            fields=["fqdns"],
        )
        IdentifierEntry.objects.create(
            identification_rule=self.identification_rule,
            fields=["hostname"],
        )

        staged_assets = StagedHostFactory.build_batch(2)

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, staged_assets, "Host"
        )
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 2)

        staged_asset1, staged_asset2 = staged_assets
        staged_asset1["hostname"] = staged_asset2["hostname"]
        staged_asset1["fqdns"] = staged_asset2["fqdns"]
        staged_asset1["last_seen"] = staged_asset1["last_seen"] + datetime.timedelta(
            minutes=1
        )

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset1], "Host"
        )
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1, "Should have merged the assets")

    def test_nand_exists_no_merge(self):
        IdentifierEntry.objects.create(
            identification_rule=self.identification_rule,
            fields=["hostname"],
            nand_exists_fields=["universal_mac_addresses"],
        )

        staged_assets = [
            StagedHostFactory(hostname="host1", mac_addresses=["00:05:9a:3c:7a:01"]),
            StagedHostFactory(hostname="host1", mac_addresses=["00:05:9a:3c:7a:02"]),
        ]

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, staged_assets, "Host"
        )
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 2, "Should not merge")

        # Test that the remain unmerged on an update
        staged_asset1, _ = staged_assets
        staged_asset1["mac_addresses"] = ["00:05:9a:3c:7a:03"]
        staged_asset1["last_seen"] = staged_asset1["last_seen"] + datetime.timedelta(
            minutes=1
        )
        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset1], "Host"
        )
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 2, "Should remain separate")

    def test_nand_exists_merges(self):
        IdentifierEntry.objects.create(
            identification_rule=self.identification_rule,
            fields=["hostname"],
            nand_exists_fields=["universal_mac_addresses"],
        )

        staged_assets = [
            StagedHostFactory(hostname="host1", mac_addresses=["00:05:9a:3c:7a:01"]),
            StagedHostFactory(hostname="host1", mac_addresses=["00:05:9a:3c:7a:02"]),
        ]

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, staged_assets, "Host"
        )
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 2, "Should not merge")

        # Test the assets merge when the nand_exists field is removed
        staged_asset1, _ = staged_assets
        staged_asset1["mac_addresses"] = []
        staged_asset1["last_seen"] = staged_asset1["last_seen"] + datetime.timedelta(
            minutes=1
        )
        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset1], "Host"
        )
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1, "Should have merged the assets")

    def test_merge_identification_aad_id(self):
        IdentifierEntry.objects.create(
            identification_rule=self.identification_rule,
            fields=["aad_id"],
        )
        self.reconciliation_aad_id = ReconciliationRule.objects.create(
            asset_type="host",
            field="aad_id",
        )

        ReconcilerEntry.objects.create(
            reconciliation_rule=self.reconciliation_aad_id,
            priority=1,
            technology_id="azure_ad",
        )

        ReconcilerEntry.objects.create(
            reconciliation_rule=self.reconciliation_aad_id,
            priority=2,
            technology_id="defender_atp",
        )

        staged_assets = [
            StagedHostFactory(aad_id="9eadbe4e-5adb-4f39-85d0-aaaa8d9e89f0")
        ]

        merge_service.merge_staged_assets(
            self.integration_azure_ad, staged_assets, "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        merged_asset = merged_assets[0]
        self.assertEqual(
            "9eadbe4e-5adb-4f39-85d0-aaaa8d9e89f0",
            merged_asset.merged_data.aad_id,
        )

        staged_assets = [
            StagedHostFactory(aad_id="9eadbe4e-5adb-4f39-85d0-aaaa8d9e89f0")
        ]

        merge_service.merge_staged_assets(
            self.integration_defender_atp, staged_assets, "Host"
        )

        merged_assets = MergedAsset.documents.search({})

        self.assertEqual(len(merged_assets), 1)
        merged_asset = merged_assets[0]
        self.assertEqual(
            merged_asset.source_data["azure_ad"][0].attributes.aad_id,
            merged_asset.source_data["defender_atp"][0].attributes.aad_id,
        )

        self.assertEqual(
            "9eadbe4e-5adb-4f39-85d0-aaaa8d9e89f0",
            merged_asset.merged_data.aad_id,
        )

    def test_merge_same_staged_asset(self):
        staged_asset = StagedHostFactory(os__name="Windows 19045")

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertEqual("Windows 19045", merged_assets[0].merged_data.os.name)

        # Test that an asset with a matching ID doesn't replace
        # the existing asset if the last seen is older.
        staged_asset["os"]["name"] = "Ubuntu"
        staged_asset["last_seen"] -= datetime.timedelta(minutes=1)
        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertEqual("Windows 19045", merged_assets[0].merged_data.os.name)

        # Test that an asset with a matching ID does replace
        # the existing asset if the last seen is newer.
        staged_asset["last_seen"] += datetime.timedelta(minutes=2)
        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertEqual("Ubuntu", merged_assets[0].merged_data.os.name)

        # Test that an asset with a matching ID does replace
        # the existing asset if the last seen is the same, but the attributes have changed.
        staged_asset["os"]["name"] = "Fedora"
        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertEqual("Fedora", merged_assets[0].merged_data.os.name)

    def test_merge_same_staged_asset_id_changed(self):
        IdentifierEntry.objects.create(
            identification_rule=self.identification_rule,
            fields=["hostname"],
        )

        staged_asset1 = StagedHostFactory(
            source_id="sentinel_one_1",
            os__name="Windows 19045",
        )

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset1], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertEqual("Windows 19045", merged_assets[0].merged_data.os.name)

        # Test that an asset that matches an existing asset using an identification
        # rule does not replace the existing asset if the last seen is not newer.
        staged_asset2 = StagedHostFactory(
            source_id="sentinel_one_2",
            os__name="Ubuntu",
            hostname=staged_asset1["hostname"],
            last_seen=staged_asset1["last_seen"] - datetime.timedelta(minutes=1),
        )

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset2], "Host"
        )

        # Asset OS Name on merged asset didn't change when new source was added
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertEqual(len(merged_assets[0].source_data["sentinel_one"]), 2)
        self.assertEqual("Windows 19045", merged_assets[0].merged_data.os.name)

        # Test that an asset that matches an existing asset using an identification
        # rule does replace the existing asset if the last seen is newer.
        staged_asset2["last_seen"] = staged_asset1["last_seen"] + datetime.timedelta(
            minutes=1
        )
        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset2], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertEqual("Ubuntu", merged_assets[0].merged_data.os.name)

    def test_merge_same_staged_asset_new_last_seen(self):
        staged_asset = StagedHostFactory(
            os__name="Windows 19045", last_seen="2023-08-22T00:00:00Z"
        )

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertEqual("Windows 19045", merged_assets[0].merged_data.os.name)

        staged_asset["last_seen"] = "2023-08-23T00:00:00Z"

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        new_last_seen = parse_datetime("2023-08-23T00:00:00Z")
        self.assertEqual(new_last_seen, merged_assets[0].metadata.asset.last_seen)
        self.assertEqual(
            new_last_seen,
            merged_assets[0].source_data["sentinel_one"][0].metadata.asset.last_seen,
        )

    def test_merge_same_staged_asset_new_last_seen_and_update_attribute(self):
        staged_asset = StagedHostFactory(
            os__name="Windows 19045",
            os__host_type="workstation",
            last_seen="2023-08-22T00:00:00Z",
        )

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertEqual("Windows 19045", merged_assets[0].merged_data.os.name)
        self.assertEqual("workstation", merged_assets[0].merged_data.os.host_type)

        staged_asset["last_seen"] = "2023-08-23T00:00:00Z"
        staged_asset["os"]["host_type"] = "server"

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertEqual("server", merged_assets[0].merged_data.os.host_type)
        new_last_seen = parse_datetime("2023-08-23T00:00:00Z")
        self.assertEqual(new_last_seen, merged_assets[0].metadata.asset.last_seen)

    def test_reconcile_criticality(self):
        IdentifierEntry.objects.create(
            identification_rule=self.identification_rule,
            fields=["hostname"],
        )

        for rule, entries in reconciliation_rules.to_models():
            rule_obj, created = ReconciliationRule.objects.get_or_create(
                asset_type=rule.asset_type,
                field=rule.field,
                organization_id=rule.organization_id,
            )

            rule_obj.entries.all().delete()
            for entry in entries:
                rule_obj.entries.add(entry, bulk=False)

        ReconcilerEntry.objects.create(
            reconciliation_rule=self.reconciliation_rule_os,
            priority=99,
            technology_id="user_input",
        )
        ReconcilerEntry.objects.create(
            reconciliation_rule=self.reconciliation_rule_criticality,
            priority=10,
            technology_id="user_input",
        )

        staged_asset = StagedHostFactory(hostname="host1", criticality="tier2")
        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        staged_asset = StagedHostFactory(hostname="host1", criticality="tier1")
        merge_service.merge_staged_assets(
            self.integration_crowdstrike_falcon1,
            [staged_asset],
            "Host",
        )

        staged_asset = StagedHostFactory(hostname="host1", criticality="tier0")
        merge_service.merge_staged_assets(self.integration_csv1, [staged_asset], "Host")

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        merged_asset = next(filter(lambda x: x.source_data.count() == 3, merged_assets))
        self.assertEqual("tier0", merged_asset.merged_data.criticality)

    def test_missing_rules_from_the_code(self):
        # Use a fixed set of technologies for consistent testing
        all_technologies = [
            "crowdstrike_falcon",
            "tenable_io",
            "sentinel_one",
            "defender_atp",
            "azure_ad",
            "ms_intune",
            "qualys_vmpc",
            "carbon_black",
            "user_input",
        ]
        all_fields = AssetAttributes.get_attributes_type(AssetType.HOST).get_members()
        bad_fields = {"hostname", "fqdn"}

        # Remove user_input from the technologies used in the comparison since the function removes it
        comparison_technologies = [
            tech for tech in all_technologies if tech != "user_input"
        ]

        mock_rules = {AssetType.HOST: {"non_existent_field": []}}
        for field in all_fields:
            mock_rules[AssetType.HOST][field] = (
                [] if field in bad_fields else comparison_technologies
            )

        with patch(
            "apps.assets.services.merging.merge_service.static_reconciliation_rules.rules",
            mock_rules,
        ), patch(
            "apps.assets.services.merging.merge_service.get_external_technology_ids",
            return_value=comparison_technologies,
        ):
            misconfigured_fields, extra_fields = merge_service.missing_rules_from_code()

        self.assertSetEqual(
            bad_fields, {field for asset_type, field in misconfigured_fields}
        )
        self.assertSetEqual(
            {"non_existent_field"}, {field for asset_type, field in extra_fields}
        )

    def test_missing_rules_from_db(self):
        # Use a fixed set of technologies for consistent testing
        all_technologies = [
            "crowdstrike_falcon",
            "tenable_io",
            "sentinel_one",
            "defender_atp",
            "azure_ad",
            "ms_intune",
            "qualys_vmpc",
            "carbon_black",
            "user_input",
        ]

        # Create integrations for all technologies to ensure they exist in the database
        for tech_id in all_technologies:
            IntegrationFactory.create(
                technology_id=tech_id, organization=self.organization, enabled=True
            )

        all_fields = set(
            AssetAttributes.get_attributes_type(AssetType.HOST).get_members()
        )
        good_fields = {"hostname"}
        ReconciliationRule.objects.create(
            asset_type=AssetType.HOST,
            field="non_existent_field",
            organization=None,
        )
        rule_hostname = ReconciliationRule.objects.create(
            asset_type=AssetType.HOST,
            field="hostname",
            organization=None,
        )
        # Exclude user_input from reconciliation rules since it's removed in the comparison
        reconciliation_technologies = [
            tech for tech in all_technologies if tech != "user_input"
        ]
        for priority, technology in enumerate(reconciliation_technologies):
            ReconcilerEntry.objects.create(
                reconciliation_rule=rule_hostname,
                priority=priority,
                technology_id=technology,
            )

        with patch(
            "apps.assets.services.merging.merge_service.get_external_technology_ids",
            return_value=reconciliation_technologies,
        ):
            misconfigured_fields, extra_fields = merge_service.missing_rules_from_db()

        bad_fields = all_fields - good_fields

        self.assertSetEqual(
            bad_fields, {field for asset_type, field in misconfigured_fields}
        )
        self.assertSetEqual(
            {"non_existent_field"}, {field for asset_type, field in extra_fields}
        )

    def test_verify_rules(self):
        # There is no merging rules in the database, so error should be raised
        with self.assertRaises(RuntimeError):
            merge_service.verify_rules()

    def sample_last_seen(self, days):
        today_date = datetime.datetime.now()
        delta_days = datetime.timedelta(days=days)
        return today_date - delta_days

    def test_purge_assets_enabled(self):
        SettingFactory.create(
            category="purge_automation",
            key=SettingKey.LAST_SEEN_GTR_X_DAYS,
            default_value={"enabled": True, "days": 33},
        )
        purge_settings = PurgeAutomationSettings().for_organization(
            self.organization.id
        )
        self.assertEqual(purge_settings.days, 33)

    def test_purge_assets_disabled(self):
        SettingFactory.create(
            category="purge_automation",
            key=SettingKey.LAST_SEEN_GTR_X_DAYS,
            default_value={"enabled": False, "days": 33},
        )
        purge_settings = PurgeAutomationSettings().for_organization(
            self.organization.id
        )
        self.assertEqual(purge_settings.days, None)

    def test_purge_automation_when_disabled(self):
        staged_asset = StagedHostFactory(last_seen=self.sample_last_seen(33))

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)

        SettingFactory.create(
            category="purge_automation",
            key=SettingKey.LAST_SEEN_GTR_X_DAYS,
            default_value={"enabled": False, "days": 20},
        )
        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)

    def test_purge_automation_enabled_older_than_x_days(self):
        staged_asset = StagedHostFactory(last_seen=self.sample_last_seen(34))

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)

        SettingFactory.create(
            category="purge_automation",
            key=SettingKey.LAST_SEEN_GTR_X_DAYS,
            default_value={"enabled": True, "days": 33},
        )

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})

        self.assertEqual(len(merged_assets), 0)

    def test_purge_automation_not_older_than_x_days(self):
        staged_asset = StagedHostFactory(last_seen=self.sample_last_seen(32))

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)

        SettingFactory.create(
            category="purge_automation",
            key=SettingKey.LAST_SEEN_GTR_X_DAYS,
            default_value={"enabled": True, "days": 33},
        )

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})

        self.assertEqual(len(merged_assets), 1)

    def test_purge_automation_equal_to_x_days(self):
        staged_asset = StagedHostFactory(last_seen=self.sample_last_seen(33))

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)

        SettingFactory.create(
            category="purge_automation",
            key=SettingKey.LAST_SEEN_GTR_X_DAYS,
            default_value={"enabled": True, "days": 33},
        )

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})

        self.assertEqual(len(merged_assets), 1)

    def test_purge_automation_not_merged_asset(self):
        staged_asset = StagedHostFactory(last_seen=self.sample_last_seen(34))

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)

        SettingFactory.create(
            category="purge_automation",
            key=SettingKey.LAST_SEEN_GTR_X_DAYS,
            default_value={"enabled": True, "days": 33},
        )

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})

        self.assertEqual(len(merged_assets), 0)

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})

        self.assertEqual(len(merged_assets), 0)

    def test_purge_automation_last_seen_none(self):
        staged_asset = StagedHostFactory(last_seen=None)

        SettingFactory.create(
            category="purge_automation",
            key=SettingKey.LAST_SEEN_GTR_X_DAYS,
            default_value={"enabled": True, "days": 33},
        )

        merge_service.merge_staged_assets(
            self.integration_azure_ad, [staged_asset], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)

    def test_purge_automation_not_in_source(self):
        staged_assets = StagedHostFactory.build_batch(2)

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, staged_assets, "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 2)

        SettingFactory.create(
            category="purge_automation",
            key=SettingKey.SOURCE_DELETED,
            default_value={"enabled": True},
        )

        staged_assets.pop()  # remove a single asset
        merge_service.merge_staged_assets(
            self.integration_sentinel_one1, staged_assets, "Host"
        )

        merged_assets = MergedAsset.documents.search({})

        self.assertEqual(len(merged_assets), 1)

    def test_purge_automation_same_technology_different_integration(self):
        IdentifierEntry.objects.create(
            identification_rule=self.identification_rule,
            fields=["hostname"],
        )

        # First create two merged assets for one technology
        staged_asset_one = StagedHostFactory(hostname="host1")
        staged_asset_two = StagedHostFactory(hostname="host2")

        merge_service.merge_staged_assets(
            self.integration_sentinel_one1,
            [staged_asset_one, staged_asset_two],
            "Host",
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 2)
        ma1 = next(m for m in merged_assets if m.merged_data.hostname == "host1")
        ma2 = next(m for m in merged_assets if m.merged_data.hostname == "host2")
        self.assertEqual(ma1.source_data.count(), 1)
        self.assertEqual(ma2.source_data.count(), 1)
        ###################################################################

        # Now merge one of the same assets with a different integration but same technology.
        # This test verifies that the asset is not purged just because it's the same technology.
        SettingFactory.create(
            category="purge_automation",
            key=SettingKey.SOURCE_DELETED,
            default_value={"enabled": True},
        )

        merge_service.merge_staged_assets(
            self.integration_sentinel_one2, [staged_asset_one], "Host"
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 2)
        ma1 = next(m for m in merged_assets if m.merged_data.hostname == "host1")
        ma2 = next(m for m in merged_assets if m.merged_data.hostname == "host2")
        self.assertEqual(
            ma1.source_data.count(),
            2,
            "An additional source was added "
            "because the staged host is from a different integration",
        )
        self.assertEqual(ma2.source_data.count(), 1)


class MergeServiceUpdateTestCase(ESCaseMixin, BaseTestCase):
    def test_rebuild_metadata(self):
        # Manually modify the metadata of the first asset
        merged_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="sentinel_one",
                ),
                MergedSourceHostFactory(
                    metadata__integration__technology_id="tenable_io",
                    metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                    metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
            ],
        )
        self.assertTrue(merged_host.metadata.asset.technologies.full_coverage)

        merged_host.metadata.asset.technologies.full_coverage = False
        MergedAsset.documents.update(merged_host)
        reloaded_host = MergedAsset.documents.get_by_locator(merged_host.locator)
        self.assertFalse(reloaded_host.metadata.asset.technologies.full_coverage)

        merge_service.rebuild_metadata(self.organization, AssetType.HOST)
        reloaded_host = MergedAsset.documents.get_by_locator(merged_host.locator)
        self.assertTrue(reloaded_host.metadata.asset.technologies.full_coverage)

    def test_rebuild_asset_criticality(self):
        IdentificationRule.objects.create(
            asset_type="host",
            organization=None,
        )
        for rule, entries in reconciliation_rules.to_models():
            rule_obj, created = ReconciliationRule.objects.get_or_create(
                asset_type=rule.asset_type,
                field=rule.field,
                organization_id=rule.organization_id,
            )

            rule_obj.entries.all().delete()
            for entry in entries:
                rule_obj.entries.add(entry, bulk=False)

        # Manually modify the criticality of the first asset to be null
        old_crit = AssetCriticality.TIER_2
        merged_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="sentinel_one",
                    attributes__criticality=old_crit,
                )
            ],
        )

        # Change the criticality in source data
        source_host = SourceHost.documents.search({})[0]
        new_crit = AssetCriticality.TIER_3
        source_host.attributes.criticality = new_crit
        SourceHost.documents.update_bulk([source_host])

        reloaded_asset = MergedAsset.documents.get_by_locator(merged_host.locator)
        self.assertEqual(
            old_crit,
            reloaded_asset.merged_data.criticality,
            msg="Merged data criticality should be unchanged",
        )

        merge_service.rebuild_data(self.organization, AssetType.HOST)
        reloaded_asset = MergedAsset.documents.get_by_locator(merged_host.locator)
        self.assertEqual(
            new_crit,
            reloaded_asset.merged_data.criticality,
            msg="Merged data criticality should be updated",
        )

    def test_realign_source_assets_by_id_rules(self):
        identification_rule = IdentificationRule.objects.create(
            asset_type="host",
            organization=None,
        )
        IdentifierEntry.objects.create(
            identification_rule=identification_rule,
            fields=["hostname"],
        )

        rule_hostname = ReconciliationRule.objects.create(
            asset_type=AssetType.HOST,
            field="hostname",
            organization=None,
        )
        for priority, technology in enumerate(["crowdstrike_falcon", "sentinel_one"]):
            ReconcilerEntry.objects.create(
                reconciliation_rule=rule_hostname,
                priority=priority,
                technology_id=technology,
            )

        # Create a merged asset with two different source hostnames
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="sentinel_one",
                    attributes__hostname="host1",
                ),
                MergedSourceHostFactory(
                    metadata__integration__technology_id="crowdstrike_falcon",
                    attributes__hostname="host2",
                ),
            ],
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)

        merge_service.realign_source_assets_by_id_rules(
            self.organization, AssetType.HOST
        )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 2)
        self.assertSetEqual(
            set(m.merged_data.hostname for m in merged_assets), {"host1", "host2"}
        )

    def test_update_metadata_fields_attributes_exist(self):
        merged_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["sentinel_one", "tenable_io"],
        )

        for source in merged_host.source_data.all():
            self.assertIsNotNone(
                source.attributes,
                f"Source should not be none for {source.technology_id}.",
            )

        MergedAsset.documents.update_bulk(
            [merged_host], MergedAssetFieldGroups.metadata_fields()
        )
        reloaded_host = MergedAsset.documents.get_by_locator(merged_host.locator)

        for source in reloaded_host.source_data.all():
            self.assertIsNotNone(
                source.attributes,
                f"Source should not be none for {source.technology_id}.",
            )
