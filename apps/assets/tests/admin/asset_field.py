from unittest.mock import patch

from django.contrib.admin import AdminSite
from django.contrib.auth import get_user_model
from django.test import TestCase

from apps.assets.admin import AssetFieldAdmin
from apps.assets.models import AssetField

User = get_user_model()


class AssetFieldAdminTestCase(TestCase):
    @patch("apps.assets.admin.asset_field.recalculate_mac_address")
    def test_asset_field(self, m_task):
        admin_model = AssetFieldAdmin(model=AssetField, admin_site=AdminSite())
        admin_model.save_model(
            obj=AssetField(asset_type="host", name="test"),
            request=None,
            form=None,
            change=None,
        )
        m_task.delay.assert_called_once()
