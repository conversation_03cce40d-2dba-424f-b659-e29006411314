from django.test import TestCase

from factories.organization_setting import OrganizationSettingFactory


class OrganizationSettingTest(TestCase):
    def test_organization_setting_str(self):
        org_setting = OrganizationSettingFactory.create(organization__alias="test")
        self.assertEqual(
            str(org_setting), "Org:test display/filter_last_seen_gtr_x_days"
        )

    def test_organization_setting_clean(self):
        org_setting = OrganizationSettingFactory.create()
        org_setting.overridden_value = {"enabled": True, "days": "0"}
        org_setting.save()
        org_setting.refresh_from_db()
        self.assertEqual(org_setting.overridden_value, {"enabled": True, "days": 0})
