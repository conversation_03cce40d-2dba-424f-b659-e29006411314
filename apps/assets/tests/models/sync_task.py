from uuid import uuid4

from django.test import TestCase

from apps.assets.models import AssetType, SyncTask
from apps.integrations.models import Integration
from apps.tests.base import BaseCaseMixin


class SyncTaskTestCase(BaseCaseMixin, TestCase):
    def test_sync_task_failed_transition(self):
        correlation_id, task = self.start_sync_task()

        # assert a STARTED task
        self.assert_time_fields(
            task,
            assert_started=self.assertIsNotNone,
            assert_staged=self.assertIsNone,
            assert_finished=self.assertIsNone,
        )
        self.assertEqual("started", task.status)

        # assert a FAILED task
        task = SyncTask.objects.set_status(correlation_id, SyncTask.Status.FAILED)
        self.assert_time_fields(
            task,
            assert_started=self.assertIsNotNone,
            assert_staged=self.assertIsNone,
            assert_finished=self.assertIsNotNone,
        )
        self.assertEqual("failed", task.status)

    def test_sync_task_status_transitions(self):
        correlation_id, task = self.start_sync_task()

        # assert a STARTED task
        self.assert_time_fields(
            task,
            assert_started=self.assertIsNotNone,
            assert_staged=self.assertIsNone,
            assert_finished=self.assertIsNone,
        )
        self.assertEqual("started", task.status)

        # assert a STAGED task
        task = SyncTask.objects.set_status(correlation_id, SyncTask.Status.STAGED)
        self.assert_time_fields(
            task,
            assert_started=self.assertIsNotNone,
            assert_staged=self.assertIsNotNone,
            assert_finished=self.assertIsNone,
        )
        self.assertEqual("staged", task.status)

        # assert a COMPLETED task
        task = SyncTask.objects.set_status(correlation_id, SyncTask.Status.COMPLETED)
        self.assert_time_fields(
            task,
            assert_started=self.assertIsNotNone,
            assert_staged=self.assertIsNotNone,
            assert_finished=self.assertIsNotNone,
        )
        self.assertEqual("completed", task.status)

    @staticmethod
    def start_sync_task():
        integration = Integration.objects.filter(technology_id="sentinel_one").first()
        correlation_id = uuid4()
        task = SyncTask.objects.start_task(
            correlation_id, integration.id, AssetType.HOST
        )
        return correlation_id, task

    @staticmethod
    def assert_time_fields(task, assert_started, assert_staged, assert_finished):
        assert_started(task.started_at)
        assert_staged(task.staged_at)
        assert_finished(task.finished_at)

    def test_sync_task_repr(self):
        integration = Integration.objects.filter(technology_id="sentinel_one").first()
        task = SyncTask.objects.start_task(uuid4(), integration.id, AssetType.HOST)
        self.assertIsNotNone(task)
        self.assertIsNotNone(repr(task))
