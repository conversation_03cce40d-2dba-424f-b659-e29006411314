import inspect

from django.core.exceptions import ValidationError
from django.test import TestCase

from apps.api.v1.schemas.filter import QueryInventoryFilter
from apps.assets.models import Setting
from apps.assets.models.setting import FilterSettingValue


class SettingTest(TestCase):
    def test_save_invalid_setting(self):
        setting = Setting(category="display", key="invalid", default_value={})
        with self.assertRaises(ValidationError):
            setting.save()

    def test_setting_str(self):
        setting = Setting(
            category="display", key="filter_last_seen_gtr_x_days", default_value={}
        )
        self.assertEqual(str(setting), "display/filter_last_seen_gtr_x_days")

    def test_filter_setting_value_fields(self):
        # Get the argument names from QueryInventoryFilter.__call__
        params = inspect.signature(QueryInventoryFilter.__call__).parameters
        query_inventory_filter_kwargs = [
            name for name, _ in params.items() if name != "self"
        ]

        # Get the field names from FilterSettingValue
        filter_setting_value_fields = FilterSettingValue.model_fields

        # Check if the sets are equal
        self.assertSetEqual(
            set(query_inventory_filter_kwargs),
            set(filter_setting_value_fields),
            f"Mismatch between QueryInventoryFilter args and FilterSettingValue fields",
        )
