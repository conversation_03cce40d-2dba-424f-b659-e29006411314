from django.test import TestCase

from apps.assets.models import Asset<PERSON>ield, AssetType
from apps.assets.models.asset_field import STATIC_EXCLUDED_VALUES


class AssetFieldTestCase(TestCase):
    def test_str(self):
        field = AssetField(asset_type="asset_type", name="field")
        self.assertEqual("asset_type.field", str(field))

    def test_universal_mac_address_case(self):
        excluded_values = STATIC_EXCLUDED_VALUES[AssetType.HOST]
        static_values = excluded_values["universal_mac_addresses"]
        self.assertSetEqual(set(static_values), set(v.lower() for v in static_values))
