from apps.assets.models.source_host import SourceHost
from apps.assets.tests.es_base import ESCaseMixin
from apps.integrations.host.host_converter import SourceHostConverter
from apps.tests.base import BaseTestCase
from factories.integration import IntegrationFactory
from factories.staged_host import StagedHostFactory


class SourceHostTestCase(ESCaseMixin, BaseTestCase):
    def test_create_bulk(self):
        converter = SourceHostConverter()
        staged_host = StagedHostFactory(hostname="B", last_seen="2021-01-01T00:00:00Z")
        source_host = converter.try_convert(IntegrationFactory(), [staged_host])

        SourceHost.documents.create_bulk(source_host)

        hosts = SourceHost.documents.search({})
        self.assertEqual(
            len(hosts),
            len(source_host),
        )
        host = hosts[0]
        self.assertEqual(host.attributes.hostname, "b")
        self.assertEqual(
            host.attributes.last_seen.isoformat().replace("+00:00", "Z"),
            "2021-01-01T00:00:00Z",
        )
