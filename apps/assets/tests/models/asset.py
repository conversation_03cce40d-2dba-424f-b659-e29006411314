from copy import deepcopy
from datetime import <PERSON><PERSON><PERSON>
from unittest import TestCase

from apps.assets.models import ReconcilerEntry, ReconciliationRule
from apps.assets.models.merged_asset import MergedAsset, MergedAssetFieldGroups
from apps.assets.models.merged_source_asset import (
    AssetAttributes,
    AssetCriticality,
    AssetFieldGroups,
    AssetType,
    HostAssetAttributes,
    HostType,
    InternetExposure,
    OsAttributes,
    OsFamily,
)
from apps.assets.tests.es_base import ESCaseMixin
from apps.integrations.models import Integration
from apps.tests.base import BaseTestCase
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import HostMetadataFactory, MergedSourceHostFactory


class SourceAssetTestCase(ESCaseMixin, BaseTestCase):
    es_fixtures = ["merged_assets.json"]

    def test_get_technology_assets(self):
        org_id = MergedHostFactory.create(
            technology_ids=["tenable_io"],
        ).metadata.organization_id
        MergedHostFactory.create(
            metadata__organization_id=org_id, technology_ids=["azure_ad"]
        )
        MergedHostFactory.create(
            metadata__organization_id=org_id, technology_ids=["tenable_io", "azure_ad"]
        )

        fields = MergedAssetFieldGroups(technologies=["tenable_io"])
        assets = MergedAsset.documents.get_by_org_asset_type(org_id, "host", fields)
        assets = list(assets)

        # Assert that all 3 merged hosts are returned, each containing
        # only the tenable_io source hosts, if there is one.
        self.assertEqual(3, len(assets))
        for asset in assets:
            self.assertTrue(
                asset.source_data is None
                or (
                    asset.source_data.count() == 1
                    and "tenable_io" in asset.source_data.technology_ids()
                )
            )

    def test_get_no_id(self):
        asset = MergedAsset.documents.get_by_document_id("invalid_id")
        self.assertIsNone(asset)

    def test_get_by_locator(self):
        asset = MergedHostFactory.create()
        from_es = MergedAsset.documents.get_by_locator(asset.locator)
        self.assertEqual(asset.locator, from_es.locator)

    def test_get_by_locator_invalid(self):
        MergedHostFactory.create(locator="invalid_locator")
        from_es = MergedAsset.documents.get_by_locator("invalid_locator")
        self.assertIsNone(from_es)

    def test_criticality(self):
        asset = MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
        merged_host = MergedHostFactory(source_assets=[asset])
        self.assertEqual(AssetCriticality.TIER_2, merged_host.merged_data.criticality)

    def test_no_criticality(self):
        asset = MergedSourceHostFactory(attributes__criticality=None)
        merged_host = MergedHostFactory(source_assets=[asset])
        self.assertEqual(AssetCriticality.UNKNOWN, merged_host.merged_data.criticality)

    def test_criticality_override(self):
        asset = MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
        merged_host = MergedHostFactory(
            source_assets=[asset],
            overrides={"criticality": "tier3"},
        )
        merged_host = merged_host.overridden_merged_asset
        self.assertEqual(AssetCriticality.TIER_3, merged_host.merged_data.criticality)

    def test_none_overrides(self):
        """
        We changed the overrides field from a dict | None to MergedAssetOverrides.
        We have to be sure existing data from ES loads correctly.
        """
        host = MergedHostFactory()
        host_dict = host.to_dict()
        host_dict["overrides"] = None
        host = MergedAsset(locator="test", **host_dict)
        self.assertIsNotNone(host.overrides)

    def test_full_coverage(self):
        edr_cov = MergedSourceHostFactory(
            metadata=HostMetadataFactory(integration__technology_id="sentinel_one")
        )
        self.assertEqual(edr_cov.technology_category, "endpoint_security")
        vul_cov = MergedSourceHostFactory(
            metadata__integration__technology_id="tenable_io",
            metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
            metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        self.assertEqual(vul_cov.technology_category, "vulnerability_management")
        merged_host = MergedHostFactory(source_assets=[edr_cov, vul_cov])
        self.assertTrue(merged_host.full_coverage)

    def test_full_coverage_no_coverage(self):
        asset = MergedSourceHostFactory(
            metadata=HostMetadataFactory(integration__technology_id="azure_ad")
        )
        merged_host = MergedHostFactory(source_assets=[asset])
        self.assertFalse(merged_host.full_coverage)

    def test_metadata_changed_empty_last_seen(self):
        with_last_seen = MergedSourceHostFactory()
        no_last_seen = MergedSourceHostFactory()
        no_last_seen.metadata.asset.last_seen = None
        self.assertTrue(no_last_seen.has_changed(with_last_seen))
        self.assertFalse(with_last_seen.has_changed(no_last_seen))

    def test_metadata_changed_times(self):
        older = MergedSourceHostFactory()
        newer = MergedSourceHostFactory()
        older_last_seen = older.metadata.asset.last_seen
        newer.metadata.asset.last_seen = older_last_seen + timedelta(seconds=1)
        self.assertTrue(older.has_changed(newer))
        self.assertFalse(newer.has_changed(older))
        self.assertFalse(older.has_changed(older))

    def test_metadata_changed_attributes(self):
        asset1 = MergedSourceHostFactory()
        asset2 = deepcopy(asset1)

        self.assertFalse(asset1.has_changed(asset2))
        self.assertFalse(asset2.has_changed(asset1))

        asset2.attributes.fqdn = ["new_fqdn"]
        self.assertTrue(asset1.has_changed(asset2))
        self.assertTrue(asset2.has_changed(asset1))

    def test_missing_attribute_on_source(self):
        merged_host = MergedHostFactory(technology_ids=["tenable_io"])
        raw = merged_host.to_dict()
        # Remove a known attribute
        del raw["source_data"]["tenable_io"][0]["attributes"]["aad_id"]

        # Add a new asset with the missing attribute
        res = MergedAsset.documents.client.index(
            MergedAsset.documents.INDEX_WRITE,
            raw,
        )
        doc_id = res["_id"]
        merged_asset = MergedAsset.documents.get_by_document_id(doc_id)
        self.assertEqual(
            merged_asset.source_data["tenable_io"][0].attributes.aad_id,
            HostAssetAttributes.get_defaults()["aad_id"],
            "Be sure to add default values for all new properties to get_defaults.",
        )


class AssetTypeTestCase(BaseTestCase):
    def test_all_asset_types_have_criticality(self):
        for asset_type in AssetType:
            attribute_fields = AssetAttributes.get_attributes_type(
                asset_type
            ).get_members()
            self.assertIn("criticality", attribute_fields)


class ReconcileMergedAssetsTestCase(BaseTestCase):
    def test_reconcile(self):
        rule_entries = self._get_rule_entries("fqdn")
        merged_host = MergedHostFactory(technology_ids=["sentinel_one", "azure_ad"])

        merged_host.reconcile(rule_entries)
        self.assertNotEqual(
            merged_host.merged_data.fqdn[0],
            merged_host.source_data["sentinel_one"][0].attributes.fqdn[0],
        )
        self.assertEqual(
            merged_host.merged_data.fqdn[0],
            merged_host.source_data["azure_ad"][0].attributes.fqdn[0],
        )

    def test_reconcile_owner(self):
        rule_entries = self._get_rule_entries("owner")
        merged_host = MergedHostFactory(technology_ids=["sentinel_one", "azure_ad"])

        merged_host.reconcile(rule_entries)
        self.assertNotEqual(
            merged_host.merged_data.owner[0],
            merged_host.source_data["sentinel_one"][0].attributes.owner[0],
        )
        self.assertEqual(
            merged_host.merged_data.owner[0],
            merged_host.source_data["azure_ad"][0].attributes.owner[0],
        )

    def test_reconcile_none_value(self):
        losing_value = []
        azure_ad_asset = MergedSourceHostFactory(
            metadata=HostMetadataFactory(integration__technology_id="azure_ad"),
            attributes__fqdn=losing_value,
        )
        sentinel_one_asset = MergedSourceHostFactory(
            metadata=HostMetadataFactory(integration__technology_id="sentinel_one")
        )
        merged_host = MergedHostFactory(
            source_assets=[azure_ad_asset, sentinel_one_asset],
        )

        self._assert_sentinel_one_wins(merged_host, "fqdn", losing_value)

    def test_reconcile_default_os(self):
        losing_value = OsAttributes(HostType.UNKNOWN, OsFamily.UNKNOWN, None)
        azure_ad_asset = MergedSourceHostFactory(
            metadata=HostMetadataFactory(integration__technology_id="azure_ad"),
            attributes__os=losing_value,
        )
        sentinel_one_asset = MergedSourceHostFactory(
            metadata=HostMetadataFactory(integration__technology_id="sentinel_one")
        )
        merged_host = MergedHostFactory(
            source_assets=[azure_ad_asset, sentinel_one_asset],
        )

        self._assert_sentinel_one_wins(merged_host, "os", losing_value)

    def test_reconcile_falsy_criticality(self):
        losing_value = AssetCriticality.UNKNOWN
        azure_ad_asset = MergedSourceHostFactory(
            metadata=HostMetadataFactory(integration__technology_id="azure_ad"),
            attributes__criticality=losing_value,
        )
        winning_value = AssetCriticality.TIER_3
        sentinel_one_asset = MergedSourceHostFactory(
            metadata=HostMetadataFactory(integration__technology_id="sentinel_one"),
            attributes__criticality=winning_value,
        )
        merged_host = MergedHostFactory(
            source_assets=[azure_ad_asset, sentinel_one_asset],
        )

        self._assert_sentinel_one_wins(merged_host, "criticality", losing_value)

    def test_reconcile_default_primary_ip(self):
        losing_value = None
        azure_ad_asset = MergedSourceHostFactory(
            metadata=HostMetadataFactory(integration__technology_id="azure_ad"),
            attributes__primary_ip_address=losing_value,
        )
        sentinel_one_asset = MergedSourceHostFactory(
            metadata=HostMetadataFactory(integration__technology_id="sentinel_one")
        )
        merged_host = MergedHostFactory(
            source_assets=[azure_ad_asset, sentinel_one_asset],
        )

        self._assert_sentinel_one_wins(merged_host, "primary_ip_address", losing_value)

    def test_reconcile_multiple_sources_with_same_last_seen(self):
        sentinel_one_asset_1 = MergedSourceHostFactory(
            metadata=HostMetadataFactory(
                integration__technology_id="sentinel_one",
                asset__last_seen="2021-01-01T00:00:00Z",
            ),
            attributes__criticality=AssetCriticality.TIER_2,
            attributes__primary_ip_address=None,
            attributes__internet_exposure=InternetExposure.INTERNET_FACING,
        )
        sentinel_one_asset_2 = MergedSourceHostFactory(
            metadata=HostMetadataFactory(
                integration__technology_id="sentinel_one",
                asset__last_seen="2021-01-01T00:00:00Z",
            ),
            attributes__criticality=AssetCriticality.TIER_3,
            attributes__internet_exposure=InternetExposure.INTERNET_FACING,
        )
        merged_host = MergedHostFactory(
            source_assets=[sentinel_one_asset_1, sentinel_one_asset_2],
        )

        self.assertEqual(
            merged_host.source_data["sentinel_one"][0].asset_id,
            sentinel_one_asset_1.asset_id,
        )

        self.assertEqual(
            merged_host.source_data["sentinel_one"][1].asset_id,
            sentinel_one_asset_2.asset_id,
        )

        criticality_entries = self._get_rule_entries("criticality")
        merged_host.reconcile(criticality_entries)

        self.assertEqual(merged_host.merged_data.criticality, AssetCriticality.TIER_3)

    @staticmethod
    def _get_rule_entries(field):
        rule = ReconciliationRule(field=field)
        entry1 = ReconcilerEntry(priority=100, technology_id="azure_ad")
        entry2 = ReconcilerEntry(priority=2, technology_id="sentinel_one")
        return [(rule, [entry1, entry2])]

    def _assert_sentinel_one_wins(self, merged_host, attribute, losing_value):
        self.assertEqual(merged_host.source_data.count(), 2)

        rule_entries = self._get_rule_entries(attribute)
        merged_host.reconcile(rule_entries)

        self.assertEqual(
            getattr(merged_host.source_data["azure_ad"][0].attributes, attribute),
            losing_value,
            "Azure AD source should have the losing value",
        )

        # compare source_data with merged_data
        self.assertEqual(
            getattr(merged_host.source_data["sentinel_one"][0].attributes, attribute),
            getattr(merged_host.merged_data, attribute),
            "Sentinel One source should equal the merged value",
        )

        # compare source_data with merged_data
        self.assertNotEqual(
            getattr(merged_host.source_data["azure_ad"][0].attributes, attribute),
            getattr(merged_host.merged_data, attribute),
            "Azure AD source should not equal the merged value",
        )


class MergedAssetFieldGroupsTestCase(TestCase):
    def test_validate_arguments(self):
        with self.assertRaises(ValueError):
            MergedAssetFieldGroups(
                includes=[MergedAssetFieldGroups.METADATA],
                technologies=["azure_ad"],
            )

        with self.assertRaises(ValueError):
            MergedAssetFieldGroups(
                includes=[MergedAssetFieldGroups.METADATA],
                asset_fields=AssetFieldGroups.METADATA,
            )


class MergedAssetOverrideTestCase(BaseTestCase):
    def test_overrides(self):
        asset = MergedHostFactory()
        self.assertFalse(asset.metadata.asset.technologies.endpoint_security_excluded)

        asset.overrides.endpoint_security_excluded = True
        overridden_asset = asset.overrides.override_asset(asset)
        self.assertTrue(
            overridden_asset.metadata.asset.technologies.endpoint_security_excluded
        )
        self.assertFalse(asset.metadata.asset.technologies.endpoint_security_excluded)
