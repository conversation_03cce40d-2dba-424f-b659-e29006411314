from copy import deepcopy
from typing import Optional
from unittest.mock import patch

from django.core.management import call_command

from apps.assets.management.commands import sync_default_settings
from apps.assets.models import Setting, SettingKey
from apps.assets.models.setting import FilterSettingValue, settings_values_map
from apps.tests.base import BaseTestCase


class SyncDefaultSettingsCommandTest(BaseTestCase):
    def test_sync_default_settings_changed_value(self):
        Setting.objects.all().delete()

        with self.assertLogs(sync_default_settings.logger, level="INFO") as logs:
            call_command("sync_default_settings")
            self.assertSetEqual(
                {"INFO"},
                {log.levelname for log in logs.records},
                "All logs should be INFO",
            )

        # Change a default value
        setting = Setting.objects.get(
            category="display",
            key=SettingKey.FILTER_LAST_SEEN_GTR_X_DAYS,
        )
        setting.default_value["days"] += 1
        setting.save()

        with self.assertLogs(sync_default_settings.logger, level="WARNING") as logs:
            call_command("sync_default_settings")
            self.assertIn(
                "Default value for display/filter_last_seen_gtr_x_days has changed. Use --force to override.",
                logs.records[0].getMessage(),
            )

    def test_sync_default_settings_new_value(self):
        Setting.objects.all().delete()

        with self.assertLogs(sync_default_settings.logger, level="INFO") as logs:
            call_command("sync_default_settings")
            self.assertSetEqual(
                {"INFO"},
                {log.levelname for log in logs.records},
                "All logs should be INFO",
            )

        # Add a new value to the settings_values_map
        class TestFilterSettingValue(FilterSettingValue):
            new_value: Optional[list[str]] = None

        test_settings_values_map = deepcopy(settings_values_map)
        test_settings_values_map["display"][
            SettingKey.DASHBOARD_FILTERS
        ] = TestFilterSettingValue()

        with patch.dict(settings_values_map, test_settings_values_map):
            with self.assertLogs(sync_default_settings.logger, level="WARNING") as logs:
                call_command("sync_default_settings")
                self.assertIn(
                    "Default value for display/dashboard_filters has changed. Use --force to override.",
                    logs.records[0].getMessage(),
                )

    def test_sync_default_settings_force(self):
        with self.assertLogs(sync_default_settings.logger, level="INFO") as logs:
            call_command("sync_default_settings")
            self.assertSetEqual(
                {"INFO"},
                {log.levelname for log in logs.records},
                "All logs should be INFO",
            )

        # Change a default value
        setting = Setting.objects.get(
            category="display",
            key=SettingKey.FILTER_LAST_SEEN_GTR_X_DAYS,
        )
        initial_default_value = setting.default_value["days"]
        setting.default_value["days"] = 42
        setting.save()

        with self.assertLogs(sync_default_settings.logger, level="INFO") as logs:
            call_command("sync_default_settings", force=True)
            self.assertSetEqual(
                {"INFO"},
                {log.levelname for log in logs.records},
                "All logs should be INFO",
            )
            self.assertEqual(
                initial_default_value,
                Setting.objects.get(
                    category="display",
                    key=SettingKey.FILTER_LAST_SEEN_GTR_X_DAYS,
                ).default_value["days"],
                "Default value should be restored",
            )
