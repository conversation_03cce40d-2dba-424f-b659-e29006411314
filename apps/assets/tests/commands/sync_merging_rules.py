from unittest.mock import patch

from django.core.management import call_command

from apps.assets.management.commands import sync_merging_rules
from apps.assets.models import (
    IdentificationRule,
    ReconciliationRule,
)
from apps.tests.base import BaseTestCase
from factories.organization import OrganizationFactory


class TestSyncMergingRulesCommand(BaseTestCase):
    def test_sync_merging_rules_command(self):
        call_command(sync_merging_rules.Command())
        self.assertTrue(
            True
        )  # This is just to check if the command runs without errors

    @patch("apps.assets.management.commands.sync_merging_rules.identification_rules")
    def test_sync_merging_rules_existing_org_id_rule(self, m_identification_rules):
        org = OrganizationFactory.create()

        m_identification_rules.to_models.return_value = [
            (IdentificationRule(asset_type="host", organization=org), [])
        ]
        # Create an existing rule for the organization to ensure
        # it's not overridden by the command.
        IdentificationRule.objects.create(asset_type="host", organization=org)

        with self.assertRaises(RuntimeError):
            call_command(sync_merging_rules.Command())

    @patch("apps.assets.management.commands.sync_merging_rules.reconciliation_rules")
    def test_sync_merging_rules_existing_org_rec_rule(self, m_reconciliation_rules):
        org = OrganizationFactory.create()

        m_reconciliation_rules.to_models.return_value = [
            (ReconciliationRule(asset_type="host", field="field", organization=org), [])
        ]
        # Create an existing rule for the organization to ensure
        # it's not overridden by the command.
        ReconciliationRule.objects.create(
            asset_type="host", field="field", organization=org
        )

        with self.assertRaises(RuntimeError):
            call_command(sync_merging_rules.Command())
