import json
from unittest.mock import mock_open, patch

from django.core.management import call_command

from apps.assets.management.commands import import_assets, sync_merging_rules
from apps.assets.models import MergedAsset
from apps.assets.tests.es_base import ESCaseMixin
from apps.tests.base import BaseTestCase
from factories.integration import IntegrationFactory
from factories.staged_host import StagedHostFactory


class ImportSourceAssetsCommandTestCase(ESCaseMixin, BaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        call_command(sync_merging_rules.Command())

    @patch("apps.assets.management.commands.import_assets.boto3")
    @patch("apps.assets.management.commands.import_assets.BytesIO")
    def test_with_integration_id(self, m_bytesio, m_boto3):
        count = 5
        staged_assets = self.get_staged_assets(count)
        m_bytesio.return_value.readlines.return_value = staged_assets

        integration = IntegrationFactory.create()
        params = {
            "integration_id": str(integration.id),
            "filename": "s3://bucket/key",
        }

        call_command(import_assets.Command(), **params)

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), count)

    @patch("apps.assets.management.commands.import_assets.boto3")
    @patch("apps.assets.management.commands.import_assets.BytesIO")
    def test_with_integration_query(self, m_bytesio, m_boto3):
        count = 5
        staged_assets = self.get_staged_assets(count)
        m_bytesio.return_value.readlines.return_value = staged_assets

        integration = IntegrationFactory.create()
        params = {
            "integration_query": f"{integration.organization.alias}:{integration.technology_id}",
            "filename": "s3://bucket/key",
        }

        call_command(import_assets.Command(), **params)

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), count)

    @patch("apps.assets.management.commands.import_assets.boto3")
    @patch("apps.assets.management.commands.import_assets.BytesIO")
    def test_with_technology_id(self, m_bytesio, m_boto3):
        count = 5
        staged_assets = self.get_staged_assets(count)
        m_bytesio.return_value.readlines.return_value = staged_assets

        params = {
            "technology_id": "defender_atp",
            "filename": "s3://bucket/key",
        }

        call_command(import_assets.Command(), **params)
        # Call the command a second time for code coverage using
        # the same Integration creation automation
        call_command(import_assets.Command(), **params)

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), count)

    def test_with_invalid_query(self):
        params = {
            "integration_query": "invalid_query",
            "filename": "s3://bucket/key",
        }

        with self.assertRaises(ValueError):
            call_command(import_assets.Command(), **params)

    def test_with_local_file(self):
        count = 5
        staged_assets = self.get_staged_assets(count)
        data = "\n".join(staged_assets)
        with patch("builtins.open", mock_open(read_data=data)):
            params = {
                "technology_id": "defender_atp",
                "filename": "/path/to/local_file",
            }

            call_command(import_assets.Command(), **params)

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), count)

    def test_delete_existing(self):
        self.test_with_local_file()

        count = 10
        staged_assets = self.get_staged_assets(count)
        data = "\n".join(staged_assets)
        with patch("builtins.open", mock_open(read_data=data)):
            params = {
                "technology_id": "defender_atp",
                "filename": "/path/to/local_file",
                "delete_existing": True,
            }

            call_command(import_assets.Command(), **params)

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), count)

    @staticmethod
    def get_staged_assets(count):
        staged_assets = StagedHostFactory.build_batch(
            count, last_seen="2021-01-01T00:00:00Z"
        )
        return [json.dumps(staged_asset) for staged_asset in staged_assets]
