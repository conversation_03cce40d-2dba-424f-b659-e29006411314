from criticalstart.audit_logs import Entity, EntityTypeId
from django.test import TestCase

from apps.assets.audit_logs.merged_asset import MergedAssetAuditLogManager
from apps.assets.models.merged_source_asset import AssetCriticality
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import MergedSourceHostFactory


class MergedAssetAuditLogManagerTests(TestCase):
    def setUp(self):
        super().setUp()
        self.audit_logs_manager = MergedAssetAuditLogManager()

    def test_returns_correct_create_details(self):
        asset = MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
        merged_asset = MergedHostFactory(source_assets=[asset])
        details = self.audit_logs_manager.get_create_details(merged_asset)
        self.assertEqual(
            details,
            {
                "hostname": merged_asset.merged_data.hostname,
                "ip": merged_asset.merged_data.primary_ip_address,
                "type_id": merged_asset.type,
            },
        )

    def test_returns_correct_delete_details(self):
        asset = MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
        merged_asset = MergedHostFactory(source_assets=[asset])
        details = self.audit_logs_manager.get_delete_details(merged_asset)
        self.assertEqual(
            details,
            {
                "hostname": merged_asset.merged_data.hostname,
                "ip": merged_asset.merged_data.primary_ip_address,
                "type_id": merged_asset.type,
            },
        )

    def test_returns_correct_update_details(self):
        details = self.audit_logs_manager.get_update_details("field", "old", "new")
        self.assertEqual(
            details,
            {
                "field_name": "field",
                "old_value_raw": "old",
                "value_raw": "new",
            },
        )

    def test_returns_correct_entity(self):
        asset = MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
        merged_asset = MergedHostFactory(source_assets=[asset])
        entity = self.audit_logs_manager.get_entity(merged_asset)
        self.assertEqual(
            entity,
            Entity(
                id=merged_asset.id,
                type_id=EntityTypeId.MERGED_ASSET,
                account_id=merged_asset.metadata.account_id,
                organization_id=merged_asset.metadata.organization_id,
            ),
        )
