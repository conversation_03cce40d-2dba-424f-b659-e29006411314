from criticalstart.audit_logs import Entity, EntityTypeId
from django.test import TestCase

from apps.assets.audit_logs.organization_setting import (
    OrganizationSettingAuditLogManager,
)
from factories.organization import OrganizationFactory
from factories.organization_setting import OrganizationSettingFactory
from factories.setting import SettingFactory


class OrganizationSettingAuditLogManagerTests(TestCase):
    def setUp(self):
        super().setUp()
        self.audit_logs_manager = OrganizationSettingAuditLogManager()

    def test_returns_correct_create_details(self):
        org = OrganizationFactory.create()
        win_setting = SettingFactory.create(
            category="asset_criticality",
            key="os_windows_server",
            default_value={"enabled": True, "criticality": "tier3"},
        )
        organization_setting = OrganizationSettingFactory.create(
            organization=org,
            setting=win_setting,
            overridden_value={"enabled": True, "criticality": "tier1"},
        )
        details = self.audit_logs_manager.get_create_details(organization_setting)
        self.assertEqual(
            details,
            {
                "category": organization_setting.setting.category,
                "key": organization_setting.setting.key,
                "overridden_value": organization_setting.overridden_value,
            },
        )

    def test_returns_correct_delete_details(self):
        org = OrganizationFactory.create()
        win_setting = SettingFactory.create(
            category="asset_criticality",
            key="os_windows_server",
            default_value={"enabled": True, "criticality": "tier3"},
        )
        organization_setting = OrganizationSettingFactory.create(
            organization=org,
            setting=win_setting,
            overridden_value={"enabled": True, "criticality": "tier1"},
        )
        details = self.audit_logs_manager.get_delete_details(organization_setting)
        self.assertEqual(
            details,
            {
                "category": organization_setting.setting.category,
                "key": organization_setting.setting.key,
                "overridden_value": organization_setting.overridden_value,
            },
        )

    def test_returns_correct_update_details(self):
        details = self.audit_logs_manager.get_update_details("field", "old", "new")
        self.assertEqual(
            details,
            {
                "field_name": "field",
                "old_value_raw": "old",
                "value_raw": "new",
            },
        )

    def test_returns_correct_entity(self):
        org = OrganizationFactory.create()
        win_setting = SettingFactory.create(
            category="asset_criticality",
            key="os_windows_server",
            default_value={"enabled": True, "criticality": "tier3"},
        )
        organization_setting = OrganizationSettingFactory.create(
            organization=org,
            setting=win_setting,
            overridden_value={"enabled": True, "criticality": "tier1"},
        )
        entity = self.audit_logs_manager.get_entity(organization_setting)
        self.assertEqual(
            entity,
            Entity(
                id=organization_setting.id,
                type_id=EntityTypeId.ASSET_INVENTORY_ORG_SETTING,
                account_id=organization_setting.organization.account.id,
                organization_id=organization_setting.organization.id,
            ),
        )
