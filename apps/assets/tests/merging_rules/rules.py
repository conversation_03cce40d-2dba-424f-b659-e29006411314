import logging

from django.test import TestCase

from apps.assets.merging_rules.identification.rules import (
    IdentificationStaticRules,
)
from apps.assets.merging_rules.reconciliation.rules import (
    ReconciliationStaticRules,
    reconciliation_rules,
)
from apps.assets.models import (
    AssetType,
    IdentificationRule,
    IdentifierEntry,
    ReconcilerEntry,
    ReconciliationRule,
)
from apps.assets.services import merge_service
from apps.integrations.utils import get_external_technology_ids

logger = logging.getLogger(__name__)


class MergingRulesTestCase(TestCase):
    def test_rules_exist(self):
        misconfigured_fields, extra_fields = merge_service.missing_rules_from_code()
        for asset_type, field in misconfigured_fields:
            self.fail(
                f"Field {asset_type}.{field} is missing from RECONCILIATION_RULES"
            )  # pragma: no cover
        for asset_type, field in extra_fields:
            self.fail(
                f"Extra field {asset_type}.{field} in RECONCILIATION_RULES is not on the model."
            )  # pragma: no cover

    def test_technologies_in_entries(self):
        all_technologies = set(get_external_technology_ids())

        for asset_type, fields in reconciliation_rules.rules.items():
            for field, technologies in fields.items():
                self.assertSetEqual(
                    set(technologies),
                    all_technologies,
                    "Missing or invalid technology names in a rule",
                )
                self.assertEqual(
                    len(technologies),
                    len(set(technologies)),
                    "Duplicate technologies in a rule",
                )


class RuleModelsTest(TestCase):
    def test_rules(self):
        id_rule = IdentificationRule.objects.create()
        id_entry = IdentifierEntry(fields=["test"])
        id_rule.entries.add(id_entry, bulk=False)

        self.assertIsNotNone(id_rule)
        self.assertIsNotNone(id_entry)
        self.assertIsNotNone(repr(id_rule))
        self.assertIsNotNone(repr(id_entry))

        rec_rule = ReconciliationRule.objects.create()
        rec_entry = ReconcilerEntry(priority=1)
        rec_rule.entries.add(rec_entry, bulk=False)

        self.assertIsNotNone(rec_rule)
        self.assertIsNotNone(rec_entry)
        self.assertIsNotNone(repr(rec_rule))
        self.assertIsNotNone(repr(rec_entry))

    def test_rec_rules_to_models(self):
        rules = {
            AssetType.HOST: {
                "hostname": [
                    "ms_intune",
                    "crowdstrike_falcon",
                ],
                "fqdn": [
                    "ms_intune",
                    "crowdstrike_falcon",
                ],
                "primary_mac_address": [
                    "ms_intune",
                    "crowdstrike_falcon",
                ],
            }
        }
        rules = ReconciliationStaticRules(rules=rules)
        models = rules.to_models()
        self.assertEqual(len(models), 3)
        for rule, entries in models:
            self.assertIsInstance(rule, ReconciliationRule)
            self.assertEqual(rule.asset_type, AssetType.HOST)
            self.assertIn(rule.field, ["hostname", "fqdn", "primary_mac_address"])
            self.assertEqual(len(entries), 2)
            for entry in entries:
                self.assertIsInstance(entry, ReconcilerEntry)
                self.assertIn(entry.technology_id, ["ms_intune", "crowdstrike_falcon"])
            self.assertEqual(entries[0].priority, 2)
            self.assertEqual(entries[1].priority, 1)

    def test_id_rules_to_models(self):
        rules = {
            AssetType.HOST: [
                {"fields": ["aad_id"], "nand_exists_fields": []},
                {"fields": ["hostname", "mac_address"], "nand_exists_fields": []},
            ],
            AssetType.USER: [
                {"fields": ["aad_id"], "nand_exists_fields": []},
                {"fields": ["username"], "nand_exists_fields": []},
            ],
        }
        rules = IdentificationStaticRules(rules=rules)
        models = rules.to_models()

        self.assertEqual(len(models), 2)
        for rule, entries in models:
            self.assertIsInstance(rule, IdentificationRule)
            if rule.asset_type == AssetType.HOST:
                self.assertEqual(entries[0].fields, ["aad_id"])
                self.assertEqual(entries[1].fields, ["hostname", "mac_address"])
            elif rule.asset_type == AssetType.USER:
                self.assertEqual(entries[0].fields, ["aad_id"])
                self.assertEqual(entries[1].fields, ["username"])

    def test_priorities_in_entries(self):
        models = reconciliation_rules.to_models()
        for rule, entries in models:
            priorities = [entry.priority for entry in entries]
            self.assertListEqual(priorities, list(reversed(range(1, len(entries) + 1))))
