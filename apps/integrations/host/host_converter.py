import ipaddress
import logging
from typing import Optional

import criticalstart.data_connectors.sdk.models as dc
import pydantic
from pydantic import BaseModel
from pydantic_extra_types.mac_address import MacAddress

from apps.assets.models.merged_source_asset import (
    AssetType as model_AssetType,
)
from apps.assets.models.merged_source_asset import (
    HostAssetAttributes,
)
from apps.assets.models.merged_source_asset import (
    OsAttributes as model_OsAttributes,
)
from apps.assets.models.merged_source_asset import (
    OwnerAttributes as model_OwnerAttributes,
)
from apps.assets.models.source_host import HostAttributes
from apps.integrations.converter import SourceAssetConverter

logger = logging.getLogger(__name__)


class Network(BaseModel):
    mac_address: MacAddress


class SourceHostConverter(SourceAssetConverter):
    asset_type = model_AssetType.HOST
    staged_asset_type = dc.Host

    def convert_attributes(self, host: dc.Host) -> HostAttributes:
        last_seen = host.last_seen
        if last_seen:
            last_seen = last_seen.replace(microsecond=0)

        public_ips, private_ips = self.group_ips(host.ip_addresses)
        universal, local = self.normalize_mac_addresses(host.mac_addresses)

        return HostAttributes(
            criticality=host.criticality,
            last_seen=last_seen,
            hostname=host.hostname,
            fqdns=host.fqdns,
            os=model_OsAttributes(
                host_type=host.os.host_type,
                family=host.os.family,
                name=host.os.name,
            ),
            public_ips=public_ips,
            private_ips=private_ips,
            primary_ip_address=HostAssetAttributes.default_primary_ip_addr(
                private_ips, public_ips
            ),
            universal_mac_addresses=universal,
            local_mac_addresses=local,
            primary_mac_address=HostAssetAttributes.default_primary_mac_addr(
                universal,
                local,
                self.excluded_field_values.get("universal_mac_address"),
            ),
            internet_exposure=host.internet_exposure,
            owners=[
                model_OwnerAttributes(name=owner.name, email=owner.email)
                for owner in host.owners
            ],
            group_names=host.group_names,
            aad_id=host.aad_id,
        )

    @staticmethod
    def group_ips(ips: list[str]) -> tuple[list[str], list[str]]:
        # Carrier-Grade NAT (CGN) IP address range. See RFC 6598 for more information.
        # For our purposes, CGN addresses can be considered private.
        cgn_range = ipaddress.ip_network("**********/10")

        public_ips = []
        private_ips = []
        for ip in ips:
            ip_address = ipaddress.ip_address(ip)
            if ip_address.is_private:
                private_ips.append(ip)
            elif ip_address.is_global:
                public_ips.append(ip)
            elif ip_address in cgn_range:
                private_ips.append(ip)
            elif not (
                ip_address.is_multicast
                or ip_address.is_loopback
                or ip_address.is_reserved
                or ip_address.is_link_local
                or ip_address.is_unspecified
            ):  #  pragma: no cover
                # I wasn't able to find any valid IP address that would end up here,
                # but let's log an error just in case
                logger.error(f"Discarding IP: {ip}")

        return public_ips, private_ips

    @staticmethod
    def normalize_mac_addresses(
        mac_addresses: list[str] | Optional[str],
    ) -> tuple[list[str], list[str]]:
        universal = []
        local = []

        for mac_address in mac_addresses:
            if len(mac_address) == 12:
                mac_address = ":".join(mac_address[i : i + 2] for i in range(0, 12, 2))

            try:
                model = Network(mac_address=mac_address)
            except pydantic.ValidationError:
                continue
            else:
                mac_address = model.mac_address

            if mac_address == "00:00:00:00:00:00":
                continue

            # Check the second-least-significant bit of the first octet to determine
            # if the MAC address is local or universal.
            octets = mac_address.split(":")
            is_local = bool(int(octets[0], 16) & 0b10)
            if is_local:
                local.append(mac_address)
            else:
                universal.append(mac_address)

        return universal, local
