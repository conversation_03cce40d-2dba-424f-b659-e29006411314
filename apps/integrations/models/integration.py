from uuid import uuid4

from django.db import models


class Integration(models.Model):
    class CoverageMode(models.TextChoices):
        ENABLED = "enabled"
        IGNORE = "ignore"
        NOT_APPLICABLE = "n/a"

    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    name = models.CharField(max_length=255, blank=True)
    technology = models.ForeignKey("Technology", on_delete=models.RESTRICT, null=True, blank=True)
    organization = models.ForeignKey("accounts.Organization", on_delete=models.RESTRICT)
    category_id = models.Char<PERSON>ield(max_length=255)
    enabled = models.BooleanField(default=True)
    vulnerability_coverage_mode = models.CharField(
        max_length=255,
        choices=CoverageMode.choices,
        default=CoverageMode.NOT_APPLICABLE,
    )
    endpoint_coverage_mode = models.CharField(
        max_length=255,
        choices=CoverageMode.choices,
        default=CoverageMode.NOT_APPLICABLE,
    )

    def __repr__(self):
        tech_id = self.technology.technology_id if self.technology else None
        return (
            f"Integration("
            f"id={self.id!r}, "
            f"technology_id={tech_id!r}, "
            f"organization={self.organization!r}, "
            f"category_id={self.category_id!r}, "
            f"enabled={self.enabled!r}, "
            f")"
        )

    def __str__(self):
        tech_id = self.technology.technology_id if self.technology else "unknown"
        return f"{self.id} [{tech_id} ({self.organization.alias})]"

    @property
    def technology_id(self): # pragma: no cover
        """Get the technology_id from the related Technology model."""
        return self.technology.technology_id if self.technology else None

    @property
    def organization_id(self): # pragma: no cover
        """Get the organization_id from the related Organization model."""
        return self.organization.id if self.organization else None

    def log_extra(self):
        tech_id = self.technology.technology_id if self.technology else None
        return {
            "integration_id": str(self.id),
            "technology_id": tech_id,
            **self.organization.log_extra(),
        }
