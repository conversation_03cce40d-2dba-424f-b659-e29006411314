from datetime import datetime
from uuid import uuid4

import responses
from django.test import TestCase

from apps.integrations.api.data_connectors import (
    ConnectorServiceAPI,
    CoverageMode,
    IntegrationSchema,
)
from apps.integrations.models import Integration, Technology
from core import settings


class DataConnectorsTestCase(TestCase):
    def setUp(self):
        self.base_url = f"{settings.CONNECTORS_SERVICE_URL}/internal/api/v1/"

    @responses.activate
    def test_invoke(self):
        # Create a test technology
        technology, _ = Technology.objects.get_or_create(
            technology_id="technology_id_123",
            defaults={'name': 'Test Technology'}
        )
        integration = Integration(
            id=uuid4(),
            technology=technology,
            organization_id="456",
        )
        url = f"{self.base_url}connectors/{integration.id}/invoke_async/"
        responses.post(
            url,
            json={"task_id": "test_task_id"},
            match=[
                responses.matchers.json_params_matcher(
                    {
                        "action": "get_devices",
                        "kwargs": {
                            "since": datetime(2023, 4, 1, 14, 11, 4).isoformat()
                        },
                    }
                )
            ],
        )

        connector_service = ConnectorServiceAPI()
        task_id = connector_service.invoke(
            integration,
            "get_devices",
            since=datetime(2023, 4, 1, 14, 11, 4).isoformat(),
        )
        self.assertEqual("test_task_id", task_id)

    @responses.activate
    def test_list_integrations(self):
        url = f"{self.base_url}integrations"
        responses.get(
            url,
            json={
                "items": [
                    {
                        "id": "cdff870c-4551-4cb5-99f4-979d597d2bfd",
                        "technology_id": "technology_id_123",
                        "version_id": "version_id_123",
                        "organization_id": "60b6e976-9cfa-45c6-a458-3ba41a20eb1d",
                        "name": "Test Integration Name",
                        "category_id": "category_id_123",
                        "category_name": "category_name_123",
                        "technology_name": "technology_name_123",
                        "enabled_actions": ["action_1", "action_2"],
                        "enabled": True,
                        "vulnerability_coverage_mode": "enabled",
                        "endpoint_coverage_mode": "enabled",
                    }
                ]
            },
        )

        connector_service = ConnectorServiceAPI()
        response = connector_service.list_integrations()
        self.assertEqual(
            [
                IntegrationSchema(
                    id="cdff870c-4551-4cb5-99f4-979d597d2bfd",
                    technology_id="technology_id_123",
                    version_id="version_id_123",
                    organization_id="60b6e976-9cfa-45c6-a458-3ba41a20eb1d",
                    name="Test Integration Name",
                    category_id="category_id_123",
                    category_name="category_name_123",
                    technology_name="technology_name_123",
                    enabled_actions=["action_1", "action_2"],
                    enabled=True,
                    vulnerability_coverage_mode=CoverageMode.ENABLED,
                    endpoint_coverage_mode=CoverageMode.ENABLED,
                )
            ],
            response,
        )
