from faker import Faker
from faker.providers import internet

from apps.integrations.host.host_converter import SourceHostConverter
from apps.tests.base import BaseTestCase
from factories.integration import IntegrationFactory
from factories.staged_host import StagedHostFactory

fake = Faker()
fake.add_provider(internet)


class HostConverterTest(BaseTestCase):
    def test_private_ip_as_primary_ip(self):
        self.assert_primary_ip(fake.ipv4_private())

    def test_public_ip_as_primary_ip(self):
        self.assert_primary_ip(fake.ipv4_public())

    def test_invalid_ip_addresses(self):
        staged = StagedHostFactory(ip_addresses=[None])
        converter = SourceHostConverter()
        result = converter.try_convert(IntegrationFactory(), [staged])
        self.assertEqual(len(result), 0)

    def test_empty_ip_addresses(self):
        staged = StagedHostFactory(ip_addresses=[])
        host = self.convert(staged)
        self.assertIsNone(host.attributes.primary_ip_address)

    def assert_primary_ip(self, ip):
        staged = StagedHostFactory(ip_addresses=[ip])
        host = self.convert(staged)
        self.assertEqual(host.attributes.primary_ip_address, ip)

    def test_zero_mac_address(self):
        staged = StagedHostFactory(mac_addresses=["00:00:00:00:00:00"])
        host = self.convert(staged)
        self.assertEqual(host.attributes.local_mac_addresses, [])
        self.assertEqual(host.attributes.universal_mac_addresses, [])
        self.assertIsNone(host.attributes.primary_mac_address)

    def test_invalid_mac_address(self):
        staged = StagedHostFactory(mac_addresses=["invalid"])
        host = self.convert(staged)
        self.assertEqual(host.attributes.local_mac_addresses, [])
        self.assertEqual(host.attributes.universal_mac_addresses, [])
        self.assertIsNone(host.attributes.primary_mac_address)

    def test_twelve_char_mac_address(self):
        staged = StagedHostFactory(mac_addresses=["002233445566"])
        host = self.convert(staged)
        self.assertEqual(host.attributes.local_mac_addresses, [])
        self.assertEqual(host.attributes.universal_mac_addresses, ["00:22:33:44:55:66"])
        self.assertEqual(host.attributes.primary_mac_address, "00:22:33:44:55:66")

    def test_cgn_ip_address(self):
        staged = StagedHostFactory(ip_addresses=["**********"])
        host = self.convert(staged)
        self.assertEqual(host.attributes.private_ips, ["**********"])

    def convert(self, staged):
        converter = SourceHostConverter()
        return converter.try_convert(IntegrationFactory(), [staged])[0]
