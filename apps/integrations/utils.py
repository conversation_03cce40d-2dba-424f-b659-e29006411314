"""
Utility functions for technology information retrieval from database.
"""
from apps.integrations.models import Integration, Technology


def get_technology_name(technology_id: str) -> str:
    """
    Get technology name by technology_id from the database.
    Falls back to technology_id if not found.
    """
    technology = Technology.objects.filter(technology_id=technology_id).first()
    return technology.name if technology else technology_id


def get_external_technology_ids() -> list[str]:
    """
    Get list of external (enabled) technology IDs from the database.
    Excludes demo_environment as it's an internal technology.
    """

    tech_ids = list(
        Integration.objects.filter(enabled=True, technology__isnull=False)
        .exclude(technology__technology_id="demo_environment")
        .values_list("technology__technology_id", flat=True)
        .distinct()
    )
    # Fallback if database query succeeds but returns empty list
    if not tech_ids:
        return [
            "crowdstrike_falcon",
            "tenable_io",
            "sentinel_one",
            "defender_atp",
            "azure_ad",
            "ms_intune",
            "qualys_vmpc",
            "carbon_black",
        ]
    return tech_ids



def get_all_technology_ids() -> list[str]:
    """
    Get list of all technology IDs from the database.
    """
    # Get technology IDs from Technology model directly
    tech_ids = list(
        Technology.objects.values_list("technology_id", flat=True).distinct()
    )
    # Fallback if database query succeeds but returns empty list
    if not tech_ids:
        return [
            "crowdstrike_falcon",
            "tenable_io",
            "sentinel_one",
            "defender_atp",
            "azure_ad",
            "ms_intune",
            "qualys_vmpc",
            "carbon_black",
            "user_input",
        ]
    return tech_ids



def get_technology_category(technology_id: str) -> str:
    """
    Get the category for a technology ID.
    Uses hardcoded mapping since Technology model no longer stores category.
    """
    # Hardcoded mapping from original TechnologyRegistry
    technology_categories = {
        "absolute": "endpoint_security",
        "azure_ad": "asset_source",
        "carbon_black": "endpoint_security",
        "cb_cloud": "endpoint_security",
        "cb_threat_hunter": "endpoint_security",
        "cisco_duo": "asset_source",
        "cisco_ise": "asset_source",
        "commvault": "backup_agent",
        "cortex_xdr": "endpoint_security",
        "crowdstrike_falcon": "endpoint_security",
        "cyberark_epm": "endpoint_security",
        "cylance": "endpoint_security",
        "defender_atp": "endpoint_security",
        "extrahop_revealx_360": "asset_source",
        "falcon_em": "asset_source",
        "freshworks_service": "asset_source",
        "import_hosts": "asset_source",
        "infoblox_ddi": "asset_source",
        "ivanti_neurons": "asset_source",
        "ivanti_pm": "asset_source",
        "jamf_pro": "endpoint_security",
        "ms_intune": "endpoint_security",
        "netapp_ontap": "asset_source",
        "qualys_gav": "asset_source",
        "qualys_vmpc": "vulnerability_management",
        "rapid7_insightvm": "vulnerability_management",
        "s1_ranger": "asset_source",
        "sentinel_one": "endpoint_security",
        "servicenow_cmdb": "asset_source",
        "sevco_io": "asset_source",
        "solarwinds_sd": "asset_source",
        "tanium_em": "endpoint_security",
        "tenable_io": "vulnerability_management",
        "tm_vision_one": "endpoint_security",
        "ubiquity_unifi": "asset_source",
        "veeam": "backup_agent",
        "veritas_alta_baas": "backup_agent",
        "vmware_aria": "asset_source",
        "zscaler": "asset_source",
        "user_input": "asset_source",
        "demo_environment": "asset_source",
    }

    return technology_categories.get(technology_id, "asset_source")
