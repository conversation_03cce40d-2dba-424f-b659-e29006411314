from django.contrib import admin, messages
from django.shortcuts import redirect
from django.urls import re_path

from apps.assets.tasks import sync_integration
from apps.integrations.models import Integration


@admin.register(Integration)
class IntegrationAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "organization_alias",
        "category_id",
        "technology_id",
    )
    list_filter = (
        "technology_id",
        "category_id",
        "organization__alias",
    )
    search_fields = (
        "organization__id",
        "organization__alias",
        "technology_id",
    )

    change_form_template = "integration_change_form.html"

    def organization_alias(self, obj):  # pragma: no cover
        return obj.organization.alias if obj.organization else None

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            re_path(
                r"^(?P<object_id>.+)/sync_integration/$",
                self.admin_site.admin_view(self.sync_integration),
                name="sync_integration",
            ),
        ]
        return custom_urls + urls

    def sync_integration(self, request, object_id, **kwargs):  # pragma: no cover
        """
        Synchronize integration from Data Connectors Service
        """

        sync_integration(object_id, force=True)
        success = "Integration updated. Sync hosts in progress."
        self.message_user(request, success, messages.SUCCESS)
        return redirect(request.META.get("HTTP_REFERER"))

    organization_alias.short_description = "Organization Alias"
