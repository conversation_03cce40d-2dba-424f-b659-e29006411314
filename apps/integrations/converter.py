import logging
from abc import ABC, abstractmethod
from typing import Any

from apps.assets.models.asset_field import <PERSON><PERSON><PERSON><PERSON>
from apps.assets.models.merged_source_asset import (
    MergedSourceAsset,
)
from apps.assets.models.source_host import (
    IntegrationMeta,
    SourceHost,
)

logger = logging.getLogger(__name__)


class SourceAssetConverter(ABC):
    asset_type = None
    staged_asset_type = None
    __registry = {}

    def __init__(self):
        self.excluded_field_values = AssetField.objects.get_excluded_values(
            self.asset_type
        )

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)

        cls.__registry[cls.staged_asset_type.__name__] = cls

    @classmethod
    def get_converter(cls, staged_asset_type) -> type["SourceAssetConverter"]:
        return cls.__registry[staged_asset_type]()

    @abstractmethod
    def convert_attributes(self, staged_asset_obj: Any):
        ...

    def convert(self, integration, staged_asset_obj: Any) -> SourceHost:
        return SourceHost(
            source_id=staged_asset_obj.source_id,
            attributes=self.convert_attributes(staged_asset_obj),
            integration=IntegrationMeta(
                id=integration.id,
                organization_id=integration.organization.id,
                technology_id=integration.technology_id,
                category_id=integration.category_id,
                vulnerability_coverage_mode=integration.vulnerability_coverage_mode,
                endpoint_coverage_mode=integration.endpoint_coverage_mode,
            ),
        )

    def try_convert(self, integration, staged_assets: list) -> list[MergedSourceAsset]:
        """
        Normalize a list of staged assets into a list of normalized source hosts.
        If any of the staged assets fail to normalize, they will be logged and skipped.
        """
        assets = []
        for staged_asset in staged_assets:
            staged_asset_obj = None
            try:
                staged_asset_obj = self.staged_asset_type(**staged_asset)
                assets.append(self.convert(integration, staged_asset_obj))
            except (TypeError, KeyError, AttributeError, ValueError):
                logger.exception(
                    "Failed to normalize staged asset",
                    extra=self.global_identifiers(integration, staged_asset_obj)
                    if staged_asset_obj
                    else {"staged_asset": str(staged_asset)},
                )
        return assets
