# Generated by Django 4.2.8 on 2024-08-26 10:40

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("accounts", "0003_alter_account_alias"),
        ("integrations", "0002_remove_integration_category_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="Setting",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("display", "Display"),
                            ("configuration", "Configuration"),
                            ("asset_criticality", "Asset Criticality"),
                            ("purge_automation", "Purge Automation"),
                        ],
                        max_length=255,
                    ),
                ),
                ("key", models.CharField(max_length=255)),
                ("default_value", models.JSONField()),
            ],
            options={
                "unique_together": {("category", "key")},
            },
        ),
        migrations.CreateModel(
            name="OrganizationSetting",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("overridden_value", models.JSONField()),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="accounts.organization",
                    ),
                ),
                (
                    "setting",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="integrations.setting",
                    ),
                ),
            ],
            options={
                "unique_together": {("organization", "setting")},
            },
        ),
    ]
