# Generated by Django 4.2.5 on 2023-09-20 07:45

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("accounts", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Integration",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("technology_id", models.<PERSON>r<PERSON>ield(max_length=255)),
                ("category_id", models.<PERSON>r<PERSON>ield(max_length=255)),
                ("category_name", models.Char<PERSON>ield(max_length=255)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.RESTRICT,
                        to="accounts.organization",
                    ),
                ),
            ],
        ),
    ]
