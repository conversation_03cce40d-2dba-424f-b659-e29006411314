import csv
import json
from itertools import zip_longest

import boto3
from ata_common.file_utils.read_env import read_env
from ata_common.storage.s3 import S3<PERSON><PERSON><PERSON>, S3Uri
from django.core.management.base import BaseCommand

read_env()

s3client = boto3.client("s3")

SUPPORTED_PRODUCTS = {
    "carbon_black": [
        "carbonblack",
        "sensor",
        "virtualappliance_uuid",
    ],
    "cb_threat_hunter": [
        "cb-threat-hunter",
        "device",
        "org_key",
    ],
    "crowdstrike_falcon": [
        "crowdstrike-falcon",
        "host",
        "product_id",
    ],
    "defender_atp": [
        "microsoft-defender_atp",
        "machine",
        "tenant_id",
    ],
    "azure_ad": [
        "microsoft-graph",
        "device",
        "tenant_id",
    ],
    "ms_intune": [
        "microsoft-graph",
        "managed-device",
        "tenant_id",
    ],
    "sentinel_one": [
        "sentinel-one",
        "agent",
        "site_id",
    ],
    "cortex_xdr": [
        "xdr",
        "endpoint",
        "product_id",
    ],
}


class Command(BaseCommand):
    """
    Management command to collect unique values for raw hosts to a CSV file.
    Example:
        python manage.py get_host_field_values
            --bucket ata-entity
            --product defender_atp
            --fields osPlatform osVersion osProcessor osBuild osArchitecture
            --filename /path/to/file.csv
            --max_count 1000000
    """

    help = "Get unique values and combinations of values for a set of raw asset fields"

    def add_arguments(self, parser):
        parser.add_argument(
            "--bucket",
            dest="bucket",
            required=True,
            type=str,
            help="Bucket to query",
        )
        parser.add_argument(
            "--product",
            dest="product",
            required=True,
            type=str,
            choices=SUPPORTED_PRODUCTS.keys(),
            help="Product Integration key",
        )
        parser.add_argument(
            "--fields",
            nargs="*",
            dest="fields",
            default=[],
            type=str,
            help="Fields for which to get unique values",
        )
        parser.add_argument(
            "--filename",
            dest="filename",
            required=True,
            type=str,
            help="Name of the output CSV file",
        )
        parser.add_argument(
            "--max_count",
            dest="max_count",
            required=False,
            type=int,
            help="Maximum number of raw hosts to process",
        )

    def handle(self, *args, **options):
        bucket = options["bucket"]
        ata_product = options["product"]
        filename = options["filename"]
        interesting_keys = options["fields"]
        max_count = options.get("max_count", 0)

        s3_product = SUPPORTED_PRODUCTS[ata_product][0]
        asset_type = SUPPORTED_PRODUCTS[ata_product][1]
        sub_key = SUPPORTED_PRODUCTS[ata_product][2]

        folders = f"entity-fetcher/{s3_product}/{asset_type}"

        key_sets = {k: set() for k in interesting_keys}
        combo_set = set()
        token = None
        raw_host_count = 0
        sub_key_value = ""
        sub_key_count = 0
        while True:
            if token:
                folder_objects = s3client.list_objects_v2(
                    Bucket=bucket,
                    Prefix=folders,
                    ContinuationToken=token,
                )
            else:
                folder_objects = s3client.list_objects_v2(
                    Bucket=bucket,
                    Prefix=folders,
                )

            # TODO: Don't process every file since they are mostly duplicate devices.
            for i, obj in enumerate(folder_objects["Contents"]):
                if folders == obj["Key"].rstrip("/"):
                    continue

                uri = S3Uri.from_uri(f"s3://{bucket}/{obj['Key']}")
                if sub_key_value != uri.partitions[sub_key]:
                    sub_key_value = uri.partitions[sub_key]
                    sub_key_count += 1
                    print("\n")
                    print(
                        f"***** Processing {sub_key}={sub_key_value} ({sub_key_count})..."
                    )

                with S3Reader(uri, chunk_size=1000000) as reader:
                    raw_hosts = [json.loads(line) for line in reader.readlines()]

                for raw_host in raw_hosts:
                    raw_host_count += 1
                    for k in interesting_keys:
                        if k in raw_host:
                            key_sets[k].add(raw_host[k] if raw_host[k] else "None")
                        else:
                            key_sets[k].add("<MISSING>")

                    if any(k in raw_host for k in interesting_keys):
                        field_values = tuple(
                            (raw_host[k] if raw_host[k] else "None")
                            if k in raw_host
                            else "<MISSING>"
                            for k in interesting_keys
                        )
                        combo_set.add(field_values)

                    if max_count and raw_host_count >= max_count:
                        break

                print(f"Processed {raw_host_count} total raw hosts.")
                if max_count and raw_host_count >= max_count:
                    break

            if (max_count and raw_host_count >= max_count) or folder_objects[
                "IsTruncated"
            ] is False:
                break

            token = folder_objects["NextContinuationToken"]

        for k in interesting_keys:
            print(f"{k}: {key_sets[k]}")
        print(f"COMBOS: {combo_set}")
        with open(filename, "w") as f:
            csvwriter = csv.writer(f)
            csvwriter.writerow([f"EXAMINED {raw_host_count} RAW HOSTS"])
            csvwriter.writerow("")
            csvwriter.writerow(["UNIQUE VALUES"])
            csvwriter.writerow("")
            csvwriter.writerow(interesting_keys)
            csvwriter.writerows(zip_longest(*key_sets.values()))
            csvwriter.writerow("")
            csvwriter.writerow("")
            csvwriter.writerow(["UNIQUE COMBOS"])
            csvwriter.writerow("")
            csvwriter.writerow(interesting_keys)
            csvwriter.writerows(combo_set)
