"""
Management command to populate Technology table from data connectors API
"""
from django.core.management.base import BaseCommand
from django.db import transaction

from apps.integrations.api.data_connectors import data_connectors_api
from apps.integrations.models import Technology
from apps.integrations.utils import get_technology_category


class Command(BaseCommand):
    help = 'Populate Technology table from data connectors API and registry'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            dest='dry_run',
            help='Run without making changes to the database',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write("Running in dry-run mode. No changes will be made.")
        
        # Get technologies from hardcoded mapping (from migration data)
        technology_names = {
            "absolute": "Absolute",
            "azure_ad": "Microsoft Entra ID (Azure AD)",
            "carbon_black": "Carbon Black",
            "cb_cloud": "Carbon Black Cloud Endpoint Standard",
            "cb_threat_hunter": "Carbon Black Threat Hunter",
            "cisco_duo": "Duo",
            "cisco_ise": "Cisco Ise AssetView",
            "commvault": "Commvault",
            "cortex_xdr": "Palo Alto Cortex XDR",
            "crowdstrike_falcon": "CrowdStrike Falcon",
            "cyberark_epm": "CyberArk Endpoint Privilege Manager",
            "cylance": "Cylance",
            "defender_atp": "Microsoft Defender for Endpoint",
            "extrahop_revealx_360": "ExtraHop RevealX 360",
            "falcon_em": "CrowdStrike Discover",
            "freshworks_service": "Freshworks Freshservice",
            "import_hosts": "Import Hosts",
            "infoblox_ddi": "Infoblox DDI",
            "ivanti_neurons": "Ivanti Neurons",
            "ivanti_pm": "Ivanti Patch Management",
            "jamf_pro": "Jamf Pro",
            "ms_intune": "Microsoft Intune",
            "netapp_ontap": "NetApp ONTAP",
            "qualys_gav": "Qualys Global AssetView",
            "qualys_vmpc": "Qualys VMDR",
            "rapid7_insightvm": "Rapid7 InsightVM",
            "s1_ranger": "SentinelOne Ranger",
            "sentinel_one": "SentinelOne",
            "servicenow_cmdb": "ServiceNow CMDB",
            "sevco_io": "Sevco",
            "solarwinds_sd": "SolarWinds Server & Application Monitor",
            "tanium_em": "Tanium",
            "tenable_io": "Tenable IO",
            "tm_vision_one": "Trend Micro Vision One",
            "ubiquity_unifi": "Ubiquity UniFi",
            "veeam": "Veeam",
            "veritas_alta_baas": "Veritas Alta BaaS",
            "vmware_aria": "VMware Aria",
            "zscaler": "Zscaler",
            "user_input": "(Internal) User Input",
            "demo_environment": "Demo Environment",
        }

        registry_technologies = {}
        for tech_id, tech_name in technology_names.items():
            registry_technologies[tech_id] = {
                'name': tech_name,
            }

        # Try to get technologies from data connectors API
        api_technologies = {}
        try:
            integrations = data_connectors_api.list_integrations()
            for integration in integrations:
                tech_id = integration.technology_id
                if tech_id not in api_technologies:
                    api_technologies[tech_id] = {
                        'name': integration.technology_name,
                    }
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"Failed to fetch from data connectors API: {e}")
            )
            self.stdout.write("Using only registry data.")
        
        # Merge data - registry takes precedence
        all_technologies = {}
        all_technologies.update(api_technologies)
        all_technologies.update(registry_technologies)
        
        created_count = 0
        updated_count = 0
        
        with transaction.atomic():
            for tech_id, tech_data in all_technologies.items():
                if dry_run:
                    existing = Technology.objects.filter(technology_id=tech_id).first()
                    if existing:
                        self.stdout.write(f"Would update: {tech_id} -> {tech_data['name']}")
                    else:
                        self.stdout.write(f"Would create: {tech_id} -> {tech_data['name']}")
                    continue
                
                technology, created = Technology.objects.update_or_create(
                    technology_id=tech_id,
                    defaults={
                        'name': tech_data['name'],
                    }
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f"Created: {technology}")
                    )
                else:
                    updated_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f"Updated: {technology}")
                    )
        
        if not dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully populated Technology table. "
                    f"Created: {created_count}, Updated: {updated_count}"
                )
            )
        else:
            self.stdout.write("Dry run completed. No changes made.")
