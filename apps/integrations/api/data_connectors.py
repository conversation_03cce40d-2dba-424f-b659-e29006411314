from enum import StrE<PERSON>
from urllib.parse import urljoin
from uuid import UUID

from ata_common.http import TimeoutSession
from pydantic import BaseModel, ConfigDict

from core import settings

USER_AGENT = __name__
TIMEOUT = 60  # seconds


class CoverageMode(StrEnum):
    ENABLED = "enabled"
    IGNORE = "ignore"
    NOT_APPLICABLE = "n/a"


class IntegrationSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    technology_id: str
    version_id: str
    name: str
    organization_id: UUID

    category_id: str
    category_name: str
    technology_name: str
    enabled_actions: list
    enabled: bool
    vulnerability_coverage_mode: CoverageMode
    endpoint_coverage_mode: CoverageMode


class ConnectorServiceAPI:
    def __init__(self, *args, **kwargs):
        self.__session = TimeoutSession(TIMEOUT)
        headers = {
            "User-Agent": USER_AGENT,
            "Authorization": "Token %s" % settings.CONNECTOR_SERVICE_TOKEN,
        }
        self.__session.headers.update(headers)

    @staticmethod
    def url(endpoint):
        base = f"{settings.CONNECTORS_SERVICE_URL}/internal/api/v1/"
        return urljoin(base, endpoint)

    def list_integrations(self):
        url = self.url("integrations")
        response = self._get(url)
        return [IntegrationSchema.model_validate(i) for i in response["items"]]

    def get_integration(self, integration_id):
        url = self.url(f"integrations/{integration_id}")
        response = self._get(url)
        return IntegrationSchema.model_validate(response)

    def invoke(self, integration, action, **kwargs):
        url = self.url(f"connectors/{integration.id}/invoke_async/")
        data = {"action": action, "kwargs": kwargs}
        response = self._post(url, json=data)
        return response["task_id"]

    def _get(self, url, params=None):
        response = self.__session.get(url, params=params)
        response.raise_for_status()

        return response.json()

    def _post(self, url, json=None):
        response = self.__session.post(url, json=json)
        response.raise_for_status()
        return response.json()


data_connectors_api = ConnectorServiceAPI()
