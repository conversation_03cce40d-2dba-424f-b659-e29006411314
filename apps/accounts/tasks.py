from celery import shared_task
from criticalstart.auth.v2.client import AuthInternalClient
from criticalstart.auth.v2.integrations.httpx import AnonymousService
from django.conf import settings

from apps.accounts.models import Account, Organization

client = AuthInternalClient(
    base_url=settings.ATA_AUTH_MICROSERVICE_URL,
    auth=AnonymousService(settings.ATA_AUTH_MICROSERVICE_SECRET_KEY),
)


@shared_task()
def sync_organizations():
    """
    Sync organizations from the auth service.
    """
    org_list = client.organizations.list()  # raises exceptions automatically

    for org in org_list.items:
        if org.account_id:
            account, _ = Account.objects.update_or_create(
                id=org.account_id,
                defaults={
                    "alias": org.account_alias,
                },
            )
        else:
            account, _ = Account.objects.get_or_create(alias="None")

        Organization.objects.update_or_create(
            id=org.id,
            defaults={
                "alias": org.alias,
                "account": account,
            },
        )
