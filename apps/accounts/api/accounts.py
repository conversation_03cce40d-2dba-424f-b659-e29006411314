import logging

from ata_common.http import TimeoutSession
from criticalstart.auth.v2.integrations.fastapi.headers import ORGANIZATION
from criticalstart.auth.v2.integrations.requests import HTTPBearer
from requests.compat import urljoin

from core import settings

logger = logging.getLogger(__name__)

TIMEOUT = 60  # seconds


class AccountsApi:
    api_url = urljoin(settings.ACCOUNTS_MICROSERVICE_URL, "api/v1/")
    # reuse the session for all requests
    __session = TimeoutSession(TIMEOUT)

    def __init__(self, token, organization_id):
        self.__auth = HTTPBearer(token)
        self.__headers = {ORGANIZATION.alias: str(organization_id)}

    def _get(self, url, params=None):
        response = self.__session.get(
            url, params=params, auth=self.__auth, headers=self.__headers
        )
        response.raise_for_status()
        return response.json()

    def get_entitlement(self, account_id, entitlement_module):
        url = urljoin(
            self.api_url,
            f"accounts/{account_id}/entitlements/module/{entitlement_module}/active",
        )
        return self._get(url)
