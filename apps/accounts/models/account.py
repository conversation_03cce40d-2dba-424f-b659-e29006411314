import uuid

from django.db import models


class Account(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    alias = models.CharField(
        max_length=255, verbose_name="Account alias", null=True, blank=True
    )

    def __repr__(self):
        return f"Account(id={self.id!r}, alias={self.alias!r})"

    def __str__(self):
        return self.alias or str(self.id)


class Organization(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    alias = models.CharField(max_length=255, verbose_name="Organization alias")
    account = models.ForeignKey(Account, on_delete=models.RESTRICT)

    def __repr__(self):
        return (
            f"Organization("
            f"id={self.id!r}, "
            f"alias={self.alias!r}, "
            f"account={self.account!r})"
        )

    def __str__(self):
        return self.alias or str(self.id)

    def log_extra(self):
        return {
            "org_alias": self.alias,
            "org_id": str(self.id),
        }
