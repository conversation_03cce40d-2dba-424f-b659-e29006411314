from unittest.mock import patch
from uuid import uuid4

from criticalstart.auth.v2.test import factory
from django.test import TestCase

from apps.accounts.models import Account, Organization
from apps.accounts.tasks import sync_organizations


@patch("apps.accounts.tasks.client.organizations.list", autospec=True)
class SyncOrganizationTests(TestCase):
    def test_sync_organization_add(self, m_list):
        m_list.return_value = factory.OrganizationList(
            items=[
                factory.Organization(
                    id="559a4855-b353-48b7-8ae7-19bc2e12e929",
                    alias="fake-organization",
                    name="Fake Organization",
                    account_id="63a0b5a0-1b1a-4f1a-9b0a-9e8b7d6c5e4e",
                    account_alias="fake-account",
                )
            ]
        )

        sync_organizations()

        self.assertEqual(Organization.objects.count(), 1)
        self.assertEqual(Account.objects.count(), 1)
        org = Organization.objects.get(id="559a4855-b353-48b7-8ae7-19bc2e12e929")
        self.assertEqual(org.alias, "fake-organization")
        self.assertEqual(str(org.account.id), "63a0b5a0-1b1a-4f1a-9b0a-9e8b7d6c5e4e")
        self.assertEqual(org.account.alias, "fake-account")

    def test_sync_organization_add_none_account(self, m_list):
        m_list.return_value = factory.OrganizationList(
            items=[
                factory.Organization(
                    id="559a4855-b353-48b7-8ae7-19bc2e12e929",
                    alias="fake-organization",
                    name="Fake Organization",
                    account_id=None,
                    account_alias=None,
                )
            ]
        )

        sync_organizations()

        self.assertEqual(Organization.objects.count(), 1)
        self.assertEqual(Account.objects.count(), 1)
        org = Organization.objects.get(id="559a4855-b353-48b7-8ae7-19bc2e12e929")
        self.assertEqual(org.alias, "fake-organization")
        self.assertEqual(org.account.alias, "None")

    def test_sync_organization_update_account(self, m_list):
        Organization.objects.create(
            id="559a4855-b353-48b7-8ae7-19bc2e12e929",
            alias="fake-organization",
            account=Account.objects.create(
                id=uuid4(),
                alias="None",
            ),
        )
        m_list.return_value = factory.OrganizationList(
            items=[
                factory.Organization(
                    id="559a4855-b353-48b7-8ae7-19bc2e12e929",
                    alias="fake-organization",
                    account_id="63a0b5a0-1b1a-4f1a-9b0a-9e8b7d6c5e4e",
                    account_alias="fake-account",
                )
            ]
        )

        sync_organizations()

        self.assertEqual(Account.objects.count(), 2)
        org = Organization.objects.get(id="559a4855-b353-48b7-8ae7-19bc2e12e929")
        self.assertEqual(str(org.account.id), "63a0b5a0-1b1a-4f1a-9b0a-9e8b7d6c5e4e")
        self.assertEqual(org.account.alias, "fake-account")

    def test_sync_organization_update_account_alias(self, m_list):
        Organization.objects.create(
            id="559a4855-b353-48b7-8ae7-19bc2e12e929",
            alias="fake-organization",
            account=Account.objects.create(
                id="63a0b5a0-1b1a-4f1a-9b0a-9e8b7d6c5e4e",
                alias="None",
            ),
        )
        m_list.return_value = factory.OrganizationList(
            items=[
                factory.Organization(
                    id="559a4855-b353-48b7-8ae7-19bc2e12e929",
                    alias="fake-organization",
                    account_id="63a0b5a0-1b1a-4f1a-9b0a-9e8b7d6c5e4e",
                    account_alias="fake-account",
                )
            ]
        )

        org = Organization.objects.get(id="559a4855-b353-48b7-8ae7-19bc2e12e929")
        self.assertEqual(org.alias, "fake-organization")
        self.assertEqual(org.account.alias, "None")

        sync_organizations()

        org = Organization.objects.get(id="559a4855-b353-48b7-8ae7-19bc2e12e929")
        self.assertEqual(str(org.account.id), "63a0b5a0-1b1a-4f1a-9b0a-9e8b7d6c5e4e")
        self.assertEqual(org.account.alias, "fake-account")

    def test_sync_organization_update_org_alias(self, m_list):
        Organization.objects.create(
            id="559a4855-b353-48b7-8ae7-19bc2e12e929",
            alias="None",
            account=Account.objects.create(
                id="63a0b5a0-1b1a-4f1a-9b0a-9e8b7d6c5e4e",
                alias="None",
            ),
        )
        m_list.return_value = factory.OrganizationList(
            items=[
                factory.Organization(
                    id="559a4855-b353-48b7-8ae7-19bc2e12e929",
                    alias="fake-organization",
                    account_id="63a0b5a0-1b1a-4f1a-9b0a-9e8b7d6c5e4e",
                    account_alias="fake-account",
                )
            ]
        )

        org = Organization.objects.get(id="559a4855-b353-48b7-8ae7-19bc2e12e929")
        self.assertEqual(org.alias, "None")

        sync_organizations()

        self.assertEqual(Organization.objects.count(), 1)
        org = Organization.objects.get(id="559a4855-b353-48b7-8ae7-19bc2e12e929")
        self.assertEqual(org.alias, "fake-organization")
