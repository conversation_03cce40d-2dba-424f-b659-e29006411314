from urllib.parse import urljoin
from uuid import uuid4

import responses
from django.test import TestCase
from requests import HTTPError
from responses import matchers

from apps.accounts.api.accounts import AccountsApi
from core import settings


class AccountsApiTestCase(TestCase):
    def setUp(self):
        self.api_url = f"{settings.ACCOUNTS_MICROSERVICE_URL}/api/v1/"

    @responses.activate
    def test_get_entitlements(self):
        account_id = str(uuid4())
        entitlement_module = "test_entitlement_module"
        token = "test_auth_token"
        org_id = uuid4()
        url = urljoin(
            self.api_url,
            f"accounts/{account_id}/entitlements/module/{entitlement_module}/active",
        )
        result = {
            "id": "b9ff49fa-c917-4f8e-8545-16bed568f51e",
            "account_id": account_id,
            "active": True,
            "requestable": False,
            "requested": False,
            "tags": [],
            "module": entitlement_module,
        }
        responses.get(
            url,
            json=result,
            match=[
                matchers.header_matcher(
                    {
                        "Authorization": f"Bearer {token}",
                        "X-CS-Organization": str(org_id),
                    }
                )
            ],
        )

        accounts_api = AccountsApi(token, org_id)
        response = accounts_api.get_entitlement(account_id, entitlement_module)
        self.assertDictEqual(result, response)

    @responses.activate
    def test_get_entitlements_404(self):
        account_id = str(uuid4())
        entitlement_module = "test_entitlement_module"
        token = "test_auth_token"
        org_id = uuid4()
        url = urljoin(
            self.api_url,
            f"accounts/{account_id}/entitlements/module/{entitlement_module}/active",
        )
        responses.get(
            url,
            json={"detail": "No entitlement found"},
            match=[
                matchers.header_matcher(
                    {
                        "Authorization": f"Bearer {token}",
                        "X-CS-Organization": str(org_id),
                    }
                )
            ],
            status=404,
        )

        accounts_api = AccountsApi(token, org_id)
        with self.assertRaises(HTTPError) as exc_context:
            accounts_api.get_entitlement(account_id, entitlement_module)
        self.assertEqual(404, exc_context.exception.response.status_code)
