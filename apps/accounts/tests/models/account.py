from django.test import TestCase

from apps.accounts.models import Account, Organization


class AccountTest(TestCase):
    def test_account_str(self):
        account = Account(alias="test")
        self.assertEqual(str(account), "test")

    def test_organization_str(self):
        account = Account(alias="test")
        organization = Organization(alias="test", account=account)
        self.assertEqual(str(organization), "test")
