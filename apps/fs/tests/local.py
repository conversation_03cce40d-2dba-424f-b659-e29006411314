import json
import os
import pathlib
from io import BytesIO

from django.test import TestCase

from apps.fs.local import LocalFilesystem


class TestLocalFileSystem(TestCase):
    def setUp(self):
        super().setUp()
        self.path = pathlib.Path(__file__).parent.absolute() / "dummy-fs"
        self.fs = LocalFilesystem(self.path)

    def tearDown(self):
        write_file = self.path / "test-dir/test-write.json"
        write_file.unlink(missing_ok=True)

        new_path = self.path / "test-dir/new-path"
        if new_path.exists():
            os.rmdir(new_path)
        super().tearDown()

    def test_not_exists(self):
        self.assertFalse(self.fs.exists("not-exists-path"))

    def test_exists(self):
        self.assertTrue(self.fs.exists("exists-path"))

    def test_makedirs_not_exists(self):
        self.assertFalse(self.fs.exists("test-dir/new-path"))
        self.fs.makedirs("test-dir/new-path")
        self.assertTrue(self.fs.exists("test-dir/new-path"))

    def test_makedirs_exists(self):
        self.assertTrue(self.fs.exists("exists-path"))
        self.fs.makedirs("exists-path")

    def test_write_file(self):
        buffer = BytesIO()
        buffer.write(json.dumps({"write": "data"}).encode())
        buffer.seek(0)
        self.assertFalse(self.fs.exists("test-dir/test-write.json"))
        self.fs.write_file("test-dir/test-write.json", buffer)
        self.assertTrue(self.fs.exists("test-dir/test-write.json"))

    def test_read_file(self):
        buffer = BytesIO()
        self.fs.read_file("test-dir/test-read.json", buffer)
        buffer.seek(0)
        for line in buffer:
            data = json.loads(line.decode("utf-8"))
        self.assertDictEqual({"some": "data"}, data)
