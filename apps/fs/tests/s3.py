from unittest.mock import Mock

from django.test import TestCase

from apps.fs.s3 import S3Filesystem


class TestS3FileSystem(TestCase):
    def setUp(self):
        self.fs = S3Filesystem("s3://asset-inventory-testing/raw_assets/")
        self.s3 = Mock()
        self.fs.client = self.s3

    def test_init(self):
        self.assertEqual(self.fs.bucket_name, "asset-inventory-testing")
        self.assertEqual(self.fs.prefix, "raw_assets")

        # Also test without the trailing slash
        fs = S3Filesystem("s3://asset-inventory-testing/raw_assets")
        self.assertEqual(fs.bucket_name, "asset-inventory-testing")
        self.assertEqual(fs.prefix, "raw_assets")

    def test_exists(self):
        self.assertTrue(self.fs.exists("any_path"))

    def test_makedirs(self):
        self.assertTrue(self.fs.makedirs("any_path"))

    def test_write_file(self):
        buffer = Mock()
        self.fs.write_file("path/file.json", buffer)
        self.s3.upload_fileobj.assert_called_with(
            buffer,
            "asset-inventory-testing",
            "raw_assets/path/file.json",
        )

    def test_download(self):
        buffer = Mock()
        self.fs.read_file("path/file.json", buffer)
        self.s3.download_fileobj.assert_called_with(
            "asset-inventory-testing", "raw_assets/path/file.json", buffer
        )
