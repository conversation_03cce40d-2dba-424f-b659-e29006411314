from django.test import TestCase

from apps.fs.memory import MemoryFilesystem


class TestMemoryFileSystem(TestCase):
    def setUp(self):
        super().setUp()
        self.fs = MemoryFilesystem()

    def test_not_exists(self):
        self.assertFalse(self.fs.exists("not-exists-path"))

    def test_makedirs_not_exists(self):
        self.assertFalse(self.fs.exists("test-dir/new-path"))
        self.fs.makedirs("test-dir/new-path")
        self.assertTrue(self.fs.exists("test-dir/new-path"))

    def test_makedirs_exists(self):
        self.fs.makedirs("exists-path")
        self.fs.makedirs("exists-path")
