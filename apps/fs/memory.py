from io import BytesIO
from pathlib import Path

from .base import Filesystem


# This is only used in testing
class MemoryFilesystem(Filesystem):
    def __init__(self):
        self.files = {}

    def exists(self, dirname):
        path = self.to_path(dirname)
        lookup = self.files
        for part in path.parts:
            lookup = lookup.get(part)
            if lookup is None:
                return False

        return lookup is not None

    def makedirs(self, dirname):
        path = self.to_path(dirname)
        lookup = self.files
        for part in path.parts:
            lookup = lookup.setdefault(part, {})

    def write_file(self, file, buffer):
        path = self.to_path(file)
        lookup = self.files
        for part in path.parts[:-1]:
            lookup = lookup[part]

        memory_file = BytesIO()
        lookup[path.parts[-1]] = memory_file
        memory_file.write(buffer.read())

    def read_file(self, file, buffer):
        path = self.to_path(file)
        lookup = self.files
        for part in path.parts:
            lookup = lookup[part]
        memory_file = lookup
        memory_file.seek(0)
        buffer.write(memory_file.read())

    def to_path(self, raw):
        return Path(raw)
