from urllib.parse import urlparse

import boto3

from .base import Filesystem


class S3Filesystem(Filesystem):
    def __init__(self, root):
        parsed = urlparse(root)
        self.client = boto3.client("s3")
        self.bucket_name = parsed.netloc
        self.prefix = parsed.path.strip("/")

    def exists(self, dirname):
        return True

    def makedirs(self, dirname):
        return True

    def write_file(self, path, buffer):
        key = self.prefix + "/" + path.lstrip("/")
        self.client.upload_fileobj(buffer, self.bucket_name, key)

    def read_file(self, path, buffer):
        key = self.prefix + "/" + path.lstrip("/")
        self.client.download_fileobj(self.bucket_name, key, buffer)
