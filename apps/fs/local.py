from pathlib import Path

from .base import Filesystem


# This class is only used in the local dev environment
class LocalFilesystem(Filesystem):
    def __init__(self, root):
        self.root = Path(root)

    def exists(self, dirname):
        path = self.to_path(dirname)
        return path.exists()

    def makedirs(self, dirname):
        path = self.to_path(dirname)
        path.mkdir(parents=True, exist_ok=True)

    def write_file(self, file, buffer):
        path = self.to_path(file)
        with path.open("wb") as f:
            for data in buffer:
                f.write(data)

    def read_file(self, file, buffer):
        path = self.to_path(file)
        with path.open("rb") as f:
            buffer.write(f.read())

    def to_path(self, raw):
        return self.root / Path(raw)
