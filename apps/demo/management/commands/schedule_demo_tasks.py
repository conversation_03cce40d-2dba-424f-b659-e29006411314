import logging

from django.core.management.base import BaseCommand
from django_celery_beat.models import CrontabSchedule, PeriodicTask

from apps.demo.demo_env_check import ensure_demo_env
from apps.demo.settings import DEMO_TASK_SCHEDULES

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Schedule periodic tasks for the demo environment."

    def handle(self, *args, **options):
        ensure_demo_env()

        tasks = DEMO_TASK_SCHEDULES

        for name, config in tasks.items():
            schedule, __ = CrontabSchedule.objects.get_or_create(**config["schedule"])

            defaults = {
                "task": config["task"],
                "crontab": schedule,
            }

            task, created = PeriodicTask.objects.get_or_create(
                name=name, defaults=defaults
            )

            if created:
                logger.info(f"Created task {name}")

            if task.crontab != schedule:
                logger.info(f"Updating schedule for {name} to {schedule}")
                task.crontab = schedule
                task.save()

            if not task.enabled:
                logger.info(f"Enabling task {name}")
                task.enabled = True
                task.save()
