from copy import deepcopy  # pragma: no cover


def patch_reconciliation_rules():  # pragma: no cover
    import apps.assets.merging_rules.reconciliation.rules as to_patch

    modify_reconciliation_rules(to_patch.reconciliation_rules.rules)


def modify_reconciliation_rules(rules):  # pragma: no cover
    for asset_type, entries in rules.items():
        for field in entries:
            rules[asset_type][field] = deepcopy(entries[field])
            rules[asset_type][field].append("demo_environment")

    return rules
