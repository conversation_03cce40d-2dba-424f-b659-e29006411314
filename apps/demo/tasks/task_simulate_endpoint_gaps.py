import logging
from datetime import timed<PERSON><PERSON>
from random import Random

from ata_common.utils.dict import get_nested_key
from celery import shared_task
from django.utils.timezone import now

from apps.accounts.models import Organization
from apps.demo.demo_env_check import ensure_demo_env
from apps.integrations.models import Integration
from apps.stats.inventory.endpoint_gaps import InventoryEndpointGaps
from apps.stats.models.persistent_stats import PersistentStat, StatType

logger = logging.getLogger(__name__)


def is_demo_organization(organization):
    return Integration.objects.filter(
        organization=organization,
        technology_id="demo_environment",
    ).exists()


# Simulated gaps go from this percent to about 15% (configured in data connectors)
GAP_START_PERCENT = 0.10
# Total goes from this percent to 100%
TOTAL_START_PERCENT = 0.9
JITTER = 0.05
WEEKEND_PERCENT = 0.9
STAT_DAYS = 31

ENDPOINT_GAP_KEYS = [
    "total",
    "mac.total",
    "mac.with_coverage",
    "mac.no_coverage",
    "linux.total",
    "linux.with_coverage",
    "linux.no_coverage",
    "windows.total",
    "windows.with_coverage",
    "windows.no_coverage",
]


@shared_task
def simulate_endpoint_gaps():
    """
    Make historical stats consistent for demo organizations.
    Since the current data never changes, data in the past must
    be updated so that there is a change over time from the last 30 days.
    """
    ensure_demo_env()

    for organization in Organization.objects.all():
        if is_demo_organization(organization):
            simulate_endpoint_gaps_for.delay(organization.id)


def set_nested_key(data, key, value):
    lookup = data
    for key_part in key.split(".")[0:-1]:
        lookup = lookup.setdefault(key_part, {})
    lookup[key.split(".")[-1]] = value


def linear_data_between(start_data, end_data, count, keys):
    """
    Extropolate linearly from starting data to ending data.
    :param start_data: Starting data
    :param end_data: Ending data
    :param count: Total number of data points, including start and end
    :param keys: Keys to interpolate
    """
    datas = []
    for i in range(count - 1):
        new_data = {}
        for key in keys:
            start_value = get_nested_key(start_data, key, default=0)
            end_value = get_nested_key(end_data, key)
            new_value = int(start_value + (i / (count - 1)) * (end_value - start_value))
            set_nested_key(new_data, key, new_value)
        datas.append(new_data)

    datas.append(end_data)
    return datas


def bumpify_datas(datas, keys):
    """
    Apply randomness to make data less uniform.
    1. Apply a random offset to each data point.
    2. Reduce every 7 weeks to simulate a "weekend".
    """
    generator = Random()
    generator.seed("bumpy_seed")
    for i, data in enumerate(datas):
        ratio = generator.uniform(1 - JITTER, 1 + JITTER)
        for key in keys:
            value = get_nested_key(data, key)
            value = value * ratio

            # Make the weekend numbers lower
            if i % 6 == 0 or i % 7 == 0:
                value = value * WEEKEND_PERCENT

            set_nested_key(data, key, int(value))


def calc_start_data(current_data):
    """
    Simulate the data from 30 days ago.
    Totals are reduced to the TOTAL_START_PERCENT.
    Gaps are reduced so that the coverage percent matches GAP_START_PERCENT.
    Since with_coverage is not used by the stat its value does not matter.
    """
    # The perentage of gaps per org is determined by data connectors,
    # so it must be calculated from the current stat
    gap_percent_end = (
        sum(
            counts["no_coverage"]
            for counts in current_data.values()
            if isinstance(counts, dict)
        )
        / current_data["total"]
    )

    start_data = {}
    for key in ENDPOINT_GAP_KEYS:
        current_value = get_nested_key(current_data, key)
        new_value = current_value * TOTAL_START_PERCENT
        if key.endswith("no_coverage") and gap_percent_end > 0:
            new_value = new_value * GAP_START_PERCENT / gap_percent_end
        set_nested_key(start_data, key, int(new_value))

    return start_data


def build_stat(organization_id, data, index):
    # Ensure that stat times are always newer than the previous run
    # of this task for any particular day
    # This task runs every hour for a total of 30 days.
    # days ago -> 30 minute intervals (30 > 24 so we must use minutes)
    # current hour -> minutes past 0/30 minute mark
    # current minute -> seconds
    days_ago = STAT_DAYS - index
    current_time = now()
    new_time = current_time.replace(
        hour=days_ago // 2,
        minute=days_ago % 2 * 30 + current_time.hour,
        second=current_time.minute,
    )
    created = new_time - timedelta(days=days_ago)

    return PersistentStat(
        organization_id=organization_id,
        type=StatType.ENDPOINT_GAPS,
        data=data,
        created=created,
    )


@shared_task
def simulate_endpoint_gaps_for(organization_id: str):
    ensure_demo_env()

    # Confirm organization is valid
    organization = Organization.objects.get(id=organization_id)

    if not is_demo_organization(organization):
        return

    # This the stats currently generated
    current_stat = InventoryEndpointGaps.calculate_persistent_stat(
        [organization_id], filters=None
    )

    if current_stat.data["total"] == 0:
        return

    stats_to_create = []

    current_data = current_stat.data
    start_data = calc_start_data(current_data)
    datas = linear_data_between(start_data, current_data, STAT_DAYS, ENDPOINT_GAP_KEYS)
    bumpify_datas(datas[1:-1], ENDPOINT_GAP_KEYS)

    for i in range(STAT_DAYS):
        new_stat = build_stat(organization_id, datas[i], i)
        stats_to_create.append(new_stat)

    for stat in stats_to_create:
        PersistentStat.documents.create(stat)
