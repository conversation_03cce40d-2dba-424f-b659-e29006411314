import logging

from celery import shared_task

from apps.accounts.models import Organization
from apps.assets.models import MergedAsset
from apps.assets.services import merge_service
from apps.demo.demo_env_check import ensure_demo_env

logger = logging.getLogger(__name__)


@shared_task
def unmerge_demo_assets():
    """
    Reset state of manually merged assets nightly.
    """
    ensure_demo_env()

    for organization in Organization.objects.all():
        demo_unmerge_assets(organization)


def demo_unmerge_assets(organization):
    count = 0
    body = {
        "query": {
            "bool": {
                "filter": [
                    {"term": {"metadata.organization_id": str(organization.id)}},
                    {"term": {"metadata.manually_merged": True}},
                ]
            }
        }
    }

    for host in MergedAsset.documents.scan(body):
        merge_service.manually_unmerge(organization, host.id)
        count += 1

    if count:
        logger.info(f"Unmerged {count} assets for organization {organization.alias}")
