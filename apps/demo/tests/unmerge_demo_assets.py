from django.test import override_settings

from apps.assets.management.commands.sync_merging_rules import _sync_merging_rules
from apps.assets.models import MergedAsset
from apps.assets.tests.es_base import ESCaseMixin
from apps.demo.tasks.task_unmerge_demo_assets import unmerge_demo_assets
from apps.tests.base import BaseTestCase
from factories.integration import IntegrationFactory
from factories.merged_host import MergedHostFactory


@override_settings(CELERY_TASK_ALWAYS_EAGER=True, DEMO_ENVIRONMENT=True)
class UnmergeDemoAssetsTestCase(ESCaseMixin, BaseTestCase):
    def setUp(self):
        super().setUp()
        _sync_merging_rules()

        self.integrations = {
            technology_id: IntegrationFactory(
                technology_id=technology_id, organization=self.organization
            )
            for technology_id in [
                "defender_atp",
                "sentinel_one",
                "falcon_em",
            ]
        }

    def test_unmerge_no_merged_hosts(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            metadata__manually_merged=False,
            technology_ids=["defender_atp", "sentinel_one", "falcon_em"],
        )

        unmerge_demo_assets()

        hosts = MergedAsset.documents.search({})
        self.assertEqual(1, len(hosts))

    def test_unmerge_hosts(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            metadata__manually_merged=True,
            technology_ids=["defender_atp", "sentinel_one", "falcon_em"],
        )
        unmerge_demo_assets()

        hosts = MergedAsset.documents.search({})
        self.assertEqual(3, len(hosts))
