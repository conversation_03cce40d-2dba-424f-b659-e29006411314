import logging

from django.test import TestCase

from apps.assets.merging_rules.reconciliation.rules import reconciliation_rules
from apps.assets.services import merge_service
from apps.integrations.utils import get_external_technology_ids

logger = logging.getLogger(__name__)


class DemoMergingRulesTestCase(TestCase):
    def test_rules_exist(self):
        misconfigured_fields, extra_fields = merge_service.missing_rules_from_code()
        for asset_type, field in misconfigured_fields:
            self.fail(
                f"Field {asset_type}.{field} has a missing technology in RECONCILIATION_RULES"
            )  # pragma: no cover
        for asset_type, field in extra_fields:
            self.fail(
                f"Extra field {asset_type}.{field} in RECONCILIATION_RULES is not on the model."
            )  # pragma: no cover

    def test_technologies_in_entries(self):
        for rule, entries in reconciliation_rules.to_models():
            technologies = [entry.technology_id for entry in entries]
            all_technologies = set(get_external_technology_ids())

            self.assertSetEqual(
                set(technologies),
                all_technologies,
                "Missing or invalid technology names in a rule in demo",
            )
            self.assertEqual(
                len(technologies),
                len(set(technologies)),
                "Duplicate technologies in a rule in demo",
            )
