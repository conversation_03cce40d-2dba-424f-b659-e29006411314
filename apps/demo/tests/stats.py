from apps.api.tests.stats import BaseHostStatsTests, BaseInventoryStatsTests
from apps.integrations.models import Integration, Technology
from factories.merged_host import MergedHostFactory


class DemoInventoryStatsTestCase(BaseInventoryStatsTests):
    def setUp(self):
        super().setUp()

        technology, _ = Technology.objects.get_or_create(
            technology_id="demo_environment",
            defaults={'name': 'Demo Environment'}
        )
        self.integration = Integration.objects.get_or_create(
            organization=self.organization, technology=technology
        )

    def test_sources(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["demo_environment"],
        )

        response = self.get(types=["sources"])
        body = response.json()
        expected_technology_ids = {"crowdstrike_falcon", "tenable_io", "sentinel_one"}
        actual_technology_ids = set(t["id"] for t in body["sources"]["totals"])
        self.assertNotIn(
            "demo_environment",
            actual_technology_ids,
        )
        self.assertSetEqual(expected_technology_ids, actual_technology_ids)


class DemoHostStatsTestCase(BaseHostStatsTests):
    def setUp(self):
        super().setUp()

        technology, _ = Technology.objects.get_or_create(
            technology_id="demo_environment",
            defaults={'name': 'Demo Environment'}
        )
        self.integration = Integration.objects.get_or_create(
            organization=self.organization, technology=technology
        )

    def test_sources(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["demo_environment"],
        )

        response = self.get(types=["sources"])
        body = response.json()
        expected_technology_ids = {"crowdstrike_falcon", "tenable_io", "sentinel_one"}
        actual_technology_ids = set(t["id"] for t in body["sources"]["totals"])
        self.assertNotIn(
            "demo_environment",
            actual_technology_ids,
        )
        self.assertSetEqual(expected_technology_ids, actual_technology_ids)
