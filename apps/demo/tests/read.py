from apps.api.tests.hosts.base import HostBaseTestMixin
from apps.assets.tests.es_base import ESCaseMixin
from apps.tests.base import BaseTestCase
from factories.merged_host import MergedHostFactory


class DemoHostReadTests(HostBaseTestMixin, ESCaseMixin, BaseTestCase):
    def test_read_hosts_sources(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["demo_environment", "crowdstrike_falcon"],
        )

        response = self.get().json()
        self.assertEqual(1, len(response["items"]))
        host = response["items"][0]
        self.assertEqual(1, len(host["technologies"]))
        self.assertEqual("crowdstrike_falcon", host["technologies"][0]["id"])
