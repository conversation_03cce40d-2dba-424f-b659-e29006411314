from datetime import timedelta

from django.test import override_settings
from django.utils.dateparse import parse_datetime

from apps.api.v1.search import add_must_clause
from apps.assets.models import MergedAsset
from apps.assets.tests.es_base import ESCaseMixin
from apps.demo.tasks import simulate_endpoint_gaps
from apps.demo.tasks.task_simulate_endpoint_gaps import simulate_endpoint_gaps_for
from apps.integrations.models import Integration
from apps.stats.models.persistent_stats import PersistentStat
from apps.tests.base import BaseTestCase
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import MergedSourceHostFactory


@override_settings(CELERY_TASK_ALWAYS_EAGER=True, DEMO_ENVIRONMENT=True)
class SimulateGapsTestCase(ESCaseMixin, BaseTestCase):
    def build_hosts(self, count, **kwargs):
        return MergedHostFactory.build_batch(
            count,
            metadata__organization_id=str(self.organization.id),
            **kwargs,
        )

    def setUp(self):
        super().setUp()
        Integration.objects.get_or_create(
            organization=self.organization, technology_id="demo_environment"
        )

        self.now = parse_datetime("2023-08-01")
        self.mock_now = self.patch("apps.demo.tasks.task_simulate_endpoint_gaps.now")
        self.mock_now.return_value = self.now

    def test_simulate_gaps(self):
        hosts = []
        crowdstrike_falcon = MergedSourceHostFactory(
            metadata__integration__technology_id="crowdstrike_falcon",
            metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.IGNORE,
            metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        tenable_io = MergedSourceHostFactory(
            metadata__integration__technology_id="tenable_io",
            metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
            metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.IGNORE,
        )
        hosts.extend(self.build_hosts(100, source_assets=[crowdstrike_falcon]))
        hosts.extend(self.build_hosts(50, source_assets=[tenable_io]))
        MergedAsset.documents.create_bulk(hosts)

        simulate_endpoint_gaps()

        body = {"size": 100}
        add_must_clause(body, {"term": {"metadata.stat.type": "endpoint_gaps"}})
        response = PersistentStat.documents.search_raw(body)
        stats = sorted(
            [s["_source"] for s in response["hits"]["hits"]],
            key=lambda s: s["metadata"]["created"],
        )
        stats = [
            s
            for s in stats
            if s["metadata"]["organization_id"] == str(self.organization.id)
        ]
        self.assertEqual(31, len(stats))

        today = stats[-1]["data"]
        self.assertEqual(150, today["total"])
        self.assertEqual(50, today["windows"]["no_coverage"])

        first_day = stats[0]["data"]
        self.assertEqual(135, first_day["total"])
        self.assertEqual(13, first_day["windows"]["no_coverage"])

    def test_simulate_gaps_overwrite(self):
        hosts = []
        crowdstrike_falcon = MergedSourceHostFactory(
            metadata__integration__technology_id="crowdstrike_falcon",
            metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.IGNORE,
            metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        tenable_io = MergedSourceHostFactory(
            metadata__integration__technology_id="tenable_io",
            metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
            metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.IGNORE,
        )
        hosts.extend(self.build_hosts(20, source_assets=[crowdstrike_falcon]))
        hosts.extend(self.build_hosts(10, source_assets=[tenable_io]))
        MergedAsset.documents.create_bulk(hosts)

        simulate_endpoint_gaps()

        additional_hosts = []
        additional_hosts.extend(
            self.build_hosts(80, source_assets=[crowdstrike_falcon])
        )
        additional_hosts.extend(self.build_hosts(40, source_assets=[tenable_io]))
        MergedAsset.documents.create_bulk(additional_hosts)

        self.mock_now.return_value = self.now + timedelta(minutes=59)
        simulate_endpoint_gaps()

        body = {
            "size": 100,
        }
        add_must_clause(body, {"term": {"metadata.stat.type": "endpoint_gaps"}})
        response = PersistentStat.documents.search_raw(body)
        stats = sorted(
            [s["_source"] for s in response["hits"]["hits"]],
            key=lambda s: s["metadata"]["created"],
        )
        self.assertEqual(62, len(stats))

        today = stats[-1]["data"]
        self.assertEqual(150, today["total"])
        self.assertEqual(50, today["windows"]["no_coverage"])

        first_day = stats[1]["data"]
        self.assertEqual(135, first_day["total"])
        self.assertEqual(13, first_day["windows"]["no_coverage"])

    def test_simulate_ignore_no_demo(self):
        simulate_endpoint_gaps_for(self.child_organization.id)
        response = PersistentStat.documents.search_raw({})
        self.assertEqual(0, response["hits"]["total"]["value"])

    def test_simulate_ignore_no_stats(self):
        from apps.integrations.models import Technology
        technology, _ = Technology.objects.get_or_create(
            technology_id="demo_environment",
            defaults={'name': 'Demo Environment'}
        )
        Integration.objects.get_or_create(
            organization=self.child_organization,
            technology=technology,
            defaults={'category_id': 'asset_source'}
        )
        simulate_endpoint_gaps_for(self.child_organization.id)
        response = PersistentStat.documents.search_raw({})
        self.assertEqual(0, response["hits"]["total"]["value"])
