from importlib import import_module
from pkgutil import walk_packages

from django.apps import AppConfig


class DemoAppConfig(AppConfig):
    name = "apps.demo"
    verbose_name = "Demo"

    # import demo only integrations
    registry = ("apps.demo.tasks",)

    def ready(self):
        from apps.demo.integrations import patch_technology_types

        patch_technology_types()

        for module in self.registry:
            path = import_module(module).__path__
            for loader, name, is_pkg in walk_packages(path):
                import_module(".".join([module, name]))

        super().ready()
