DEMO_TASK_SCHEDULES = {
    "DEMO - Simulate Endpoint Gaps": {
        "task": ("apps.demo.tasks.task_simulate_endpoint_gaps.simulate_endpoint_gaps"),
        "schedule": {
            # This should be run in between imports
            "hour": "*/1",
            "minute": "30",
        },
    },
    "DEMO - Unmerge demo assets": {
        "task": ("apps.demo.tasks.task_unmerge_demo_assets.unmerge_demo_assets"),
        "schedule": {
            # Same schedule as resetting on call schedules in ata_portal
            "hour": "4",
            "minute": "0",
        },
    },
}
