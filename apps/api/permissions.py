from enum import Str<PERSON>num
from functools import cache
from typing import Callable, Iterable
from uuid import UUID

from criticalstart.auth.v2.models import UserSession
from fastapi import HTTPException, status


class AssetInventoryPermission(StrEnum):
    DELETE_ASSET = "asset_inventory:delete_asset"
    EDIT_SETTINGS = "asset_inventory:edit_settings"


def check_permissions(session: UserSession, permission: str, org_ids: Iterable[UUID]):
    for org_id in org_ids:
        if permission not in session.get_permissions(org_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have privileges to perform this action",
            )
    return True


def cached_permissions_checker(
    session: UserSession, permission: str
) -> Callable[[UUID], bool]:
    @cache
    def _check(org_id: UUID):
        return check_permissions(session, permission, [org_id])

    return _check
