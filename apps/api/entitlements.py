from enum import StrEnum

from criticalstart.auth.v2.integrations.fastapi import UserSession
from fastapi import status
from fastapi.exceptions import HTTPException
from requests import HTTPError

from apps.accounts.api.accounts import AccountsApi
from apps.assets.models.merged_source_asset import CoverageCategory
from apps.assets.search.host import InventoryFilter


class EntitlementModule(StrEnum):
    FULL_VISIBILITY = "assets:full_visibility"
    COVERAGE_GAPS = "assets:coverage_gaps"


def check_entitlement(session, entitlement):
    def error_response():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Your account does not have access to this feature.",
        )

    account_id = session.organization.account_id
    if not account_id:
        error_response()

    accounts_api = AccountsApi(session.token, session.organization.id)
    try:
        accounts_api.get_entitlement(account_id, entitlement)
    except HTTPError as e:
        if e.response.status_code in [404, 422]:
            error_response()
        raise e


def check_host_entitlements(session: UserSession):
    check_entitlement(session, EntitlementModule.FULL_VISIBILITY)


def check_configuration_entitlements(session: UserSession):
    check_entitlement(session, EntitlementModule.FULL_VISIBILITY)


def check_filter_entitlements(session: UserSession, filters: InventoryFilter):
    full_visibility_filters = [
        filters.last_seen,
        filters.last_seen_endpoint_security,
        filters.last_seen_vulnerability_management,
        filters.os_family,
        filters.criticality,
        filters.technology_ids,
    ]
    if any(full_visibility_filters):
        check_entitlement(session, EntitlementModule.FULL_VISIBILITY)
    elif not filters.missing_coverage:
        # If no filters are provided, we need to check for full visibility
        check_entitlement(session, EntitlementModule.FULL_VISIBILITY)
    elif not set(filters.missing_coverage).issubset(set(CoverageCategory.supported())):
        check_entitlement(session, EntitlementModule.FULL_VISIBILITY)
    else:
        check_entitlement(session, EntitlementModule.COVERAGE_GAPS)
