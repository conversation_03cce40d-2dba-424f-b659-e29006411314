import logging

from criticalstart.fastapi_utils.exceptions.handlers import (
    add_exception_handlers as add_criticalstart_exception_handlers,
)
from criticalstart.fastapi_utils.exceptions.handlers import (
    error_content_factory,
    error_response_factory,
)
from fastapi import FastAPI, Request
from fastapi import status as http_status

from apps.assets.services.merging.merge_service import MergeService

logger = logging.getLogger(__name__)


async def merge_service_lock_error(
    request: Request,
    exc: MergeService.LockError,
):
    content = error_content_factory(
        http_status.HTTP_423_LOCKED,
        "The requested operation could not be completed because the resource is locked.",
        None,
    )
    return error_response_factory(content)


def add_exception_handlers(app: FastAPI):
    handlers = [
        (MergeService.LockError, merge_service_lock_error),
    ]

    add_criticalstart_exception_handlers(app)

    for exception, handler in handlers:
        app.add_exception_handler(exception, handler)
