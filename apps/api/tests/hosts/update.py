from unittest.mock import patch

from criticalstart.audit_logs import COMMAND_AUDIT_LOG_CREATE, Action, EntityTypeId

from apps.api.tests.hosts.base import HostBaseTestMixin
from apps.assets.models.merged_asset import MergedAsset
from apps.assets.tests.es_base import ESCaseMixin
from apps.tests.base import BaseTestCase
from core.service_bus import service_bus
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import MergedSourceHostFactory


class UpdateHostTest(HostBaseTestMixin, ESCaseMixin, BaseTestCase):
    def test_set_coverage_gap_exclude(self):
        host = MergedHostFactory.create(metadata__organization_id=self.organization.id)
        self.assertFalse(host.metadata.asset.technologies.endpoint_security_excluded)
        self.assertFalse(host.overrides.endpoint_security_excluded)

        body = {"exclude": True, "coverage_gap": "endpoint_security"}
        self.put(host_id=host.id, field="coverage_gap_excluded", body=body)
        merged_asset = MergedAsset.documents.search({})[0]
        self.assertTrue(merged_asset.overrides.endpoint_security_excluded)
        self.assertTrue(
            merged_asset.overridden_merged_asset.metadata.asset.technologies.endpoint_security_excluded
        )

        # Check if coverage_gaps_excluded is returned in the API
        merged_asset = self.get(host_id=host.id).json()
        self.assertTrue(merged_asset["endpoint_security_excluded"])
        self.assertFalse(merged_asset["vulnerability_management_excluded"])

        body = {"exclude": False, "coverage_gap": "endpoint_security"}
        self.put(host_id=host.id, field="coverage_gap_excluded", body=body)
        merged_asset = MergedAsset.documents.search({})[0]
        self.assertFalse(
            merged_asset.metadata.asset.technologies.endpoint_security_excluded
        )
        self.assertFalse(merged_asset.overrides.endpoint_security_excluded)

    def test_list_set_coverage_gap_exclude(self):
        hosts = []
        for i in range(10):
            hosts.append(
                MergedHostFactory.create(
                    metadata__organization_id=self.organization.id,
                    source_assets=[
                        MergedSourceHostFactory(attributes__hostname=str(i))
                    ],
                )
            )

        body = {"exclude": True, "coverage_gap": "vulnerability_management"}
        self.put(field="coverage_gap_excluded", body=body, query="hostname: 3")
        merged_assets = MergedAsset.documents.search({})
        for asset in merged_assets:
            if asset.merged_data.hostname == "3":
                self.assertTrue(asset.overrides.vulnerability_management_excluded)
                self.assertTrue(
                    asset.overridden_merged_asset.metadata.asset.technologies.vulnerability_management_excluded
                )
            else:
                self.assertFalse(asset.overrides.vulnerability_management_excluded)
                self.assertFalse(
                    asset.overridden_merged_asset.metadata.asset.technologies.vulnerability_management_excluded
                )

        # Check if coverage_gaps_excluded is returned in the API
        response = self.get()
        objects = response.json()["items"]
        for asset in objects:
            if asset["hostname"] == "3":
                self.assertTrue(asset["vulnerability_management_excluded"])
                self.assertFalse(asset["endpoint_security_excluded"])
            else:
                self.assertFalse(asset["vulnerability_management_excluded"])
                self.assertFalse(asset["endpoint_security_excluded"])

        body = {"exclude": False, "coverage_gap": "vulnerability_management"}
        self.put(field="coverage_gap_excluded", body=body)
        merged_assets = MergedAsset.documents.search({})
        for asset in merged_assets:
            self.assertFalse(asset.overrides.vulnerability_management_excluded)
            self.assertFalse(
                asset.overridden_merged_asset.metadata.asset.technologies.vulnerability_management_excluded
            )

    def test_set_coverage_gap_exclude_error(self):
        host = MergedHostFactory.create(metadata__organization_id=self.organization.id)
        body = {"exclude": True, "coverage_gap": "no_gaps"}
        self.put(
            host_id=host.id,
            field="coverage_gap_excluded",
            body=body,
            status_code=422,
        )

    def test_set_criticality(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__criticality="tier4")],
        )
        body = {"value": "tier2"}
        response = self.put(host_id=host.id, field="criticality", body=body)
        self.assertEqual(response.json(), body)
        merged_asset = MergedAsset.documents.search({})[0]
        self.assertEqual(merged_asset.overrides.criticality, "tier2")

        body = {"value": "tier3"}
        response = self.put(host_id=host.id, field="criticality", body=body)
        self.assertEqual(response.json(), body)
        merged_asset = MergedAsset.documents.search({})[0]
        self.assertEqual(merged_asset.overrides.criticality, "tier3")

        body = {"value": "unknown"}
        response = self.put(host_id=host.id, field="criticality", body=body)
        self.assertEqual(response.json(), body)
        merged_asset = MergedAsset.documents.search({})[0]
        self.assertFalse(merged_asset.overrides.criticality)

    def test_list_set_criticality(self):
        hosts = []
        for i in range(10):
            hosts.append(
                MergedHostFactory.create(
                    metadata__organization_id=self.organization.id,
                    source_assets=[
                        MergedSourceHostFactory(attributes__criticality="tier4")
                    ],
                )
            )

        hostname = hosts[0].merged_data.hostname

        # first update by hostname query
        body = {"value": "tier2"}
        response = self.put(field="criticality", body=body, query=hostname)
        self.assertEqual(response.json(), body)
        merged_asset = MergedAsset.documents.search({})
        self.assertEqual(10, len(merged_asset))
        for asset in merged_asset:
            self.assertEqual(asset.merged_data.criticality, "tier4")
            if asset.merged_data.hostname == hostname:
                self.assertEqual(asset.overrides.criticality, "tier2")
                self.assertEqual(
                    asset.overridden_merged_asset.merged_data.criticality, "tier2"
                )
            else:
                self.assertFalse(asset.overrides.criticality)
                self.assertEqual(
                    asset.overridden_merged_asset.merged_data.criticality, "tier4"
                )

        # now update all
        response = self.put(field="criticality", body=body)
        self.assertEqual(response.json(), body)
        merged_asset = MergedAsset.documents.search({})
        self.assertEqual(10, len(merged_asset))
        for asset in merged_asset:
            self.assertEqual(asset.overrides.criticality, "tier2")

        # now reset criticality
        body = {"value": "unknown"}
        response = self.put(field="criticality", body=body)
        self.assertEqual(response.json(), body)
        merged_asset = MergedAsset.documents.search({})
        self.assertEqual(10, len(merged_asset))
        for asset in merged_asset:
            self.assertFalse(asset.overrides.criticality)
            self.assertEqual(
                asset.overridden_merged_asset.merged_data.criticality, "tier4"
            )

    def test_set_criticality_on_empty_overrides(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__criticality="tier4")],
        )
        body = {"value": "tier2"}
        self.put(field="criticality", body=body)
        merged_asset = MergedAsset.documents.search({})[0]
        self.assertEqual(merged_asset.overrides.criticality, "tier2")

    def test_set_coverage_gap_exclude_audit_log(self):
        host = MergedHostFactory.create(metadata__organization_id=self.organization.id)
        body = {"exclude": True, "coverage_gap": "endpoint_security"}

        with patch.object(service_bus, "send_command") as m_send_command:
            self.put(
                host_id=host.id,
                field="coverage_gap_excluded",
                body=body,
            )

        m_send_command.assert_called_once()
        command, audit_log = m_send_command.call_args[0]

        self.assertEqual(COMMAND_AUDIT_LOG_CREATE, command)

        self.assertEqual(str(audit_log.entity.id), host.id)
        self.assertEqual(audit_log.entity.type_id, EntityTypeId.MERGED_ASSET)
        self.assertEqual(str(audit_log.entity.account_id), host.metadata.account_id)
        self.assertEqual(
            str(audit_log.entity.organization_id), host.metadata.organization_id
        )

        self.assertEqual(audit_log.action, Action.UPDATE)
        self.assertEqual(audit_log.action_by_id, self.user_id)
        self.assertDictEqual(
            audit_log.details,
            {
                "field_name": "endpoint_security_excluded",
                "old_value_raw": None,
                "value_raw": True,
            },
        )
