class HostBaseTestMixin:
    fixtures = ["accounts.json"]

    def setUp(self):
        super().setUp()
        self.patch("apps.api.entitlements.check_entitlement")

    def get(self, host_id=None, status_code=200, query=None, params=None):
        params = params or {}
        path = "/api/v1/hosts"
        if host_id:
            path += f"/{host_id}"

        if query:
            params["query"] = query

        response = self.api_client.get(path, params=params)
        self.assertEqual(status_code, response.status_code, response.json())
        return response

    def put(
        self, field, host_id=None, body=None, status_code=200, query=None, params=None
    ):
        params = params or {}
        path = "/api/v1/hosts"
        if host_id:
            path += f"/{host_id}"

        path += f"/{field}"

        if query:
            params["query"] = query

        response = self.api_client.put(path, params=params, json=body)
        self.assertEqual(status_code, response.status_code)
        return response

    def delete(self, host_id=None, status_code=200, query=None, params=None):
        params = params or {}
        path = "/api/v1/hosts"
        if host_id:
            path += f"/{host_id}"

        if query:
            params["query"] = query

        response = self.api_client.delete(path, params=params)
        self.assertEqual(status_code, response.status_code, response.json())
        return response
