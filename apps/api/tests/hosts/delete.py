from unittest.mock import patch

from ata_common.chunking import chunks
from criticalstart.audit_logs import COMMAND_AUDIT_LOG_CREATE, Action, EntityTypeId

from apps.api.permissions import AssetInventoryPermission
from apps.api.tests.hosts.base import HostBaseTestMixin
from apps.assets.models.merged_asset import MergedAsset
from apps.assets.tests.es_base import ESCaseMixin
from apps.tests.base import ApiDatabaseTestCase
from core.service_bus import service_bus
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import MergedSourceHostFactory


class HostDeleteTests(HostBaseTestMixin, ESCaseMixin, ApiDatabaseTestCase):
    def test_delete_host(self):
        self.set_org_permissions(
            self.organization, {AssetInventoryPermission.DELETE_ASSET}
        )
        host = MergedHostFactory.create(metadata__organization_id=self.organization.id)

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(1, len(merged_assets))

        self.delete(host_id=host.id)
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(0, len(merged_assets))

    def test_delete_host_invalid_permissions(self):
        host = MergedHostFactory.create(metadata__organization_id=self.organization.id)
        self.delete(host_id=host.id, status_code=403)
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(1, len(merged_assets))

    def test_delete_hosts_by_query(self):
        self.set_org_permissions(
            self.organization, {AssetInventoryPermission.DELETE_ASSET}
        )
        for i in range(4):
            MergedHostFactory.create(
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        attributes__hostname=f"host{i+1}",
                        attributes__criticality="tier2",
                    )
                ],
            )

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(4, len(merged_assets))

        query = "hostname: host1"
        self.delete(query=query)
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(3, len(merged_assets))

        params = {"criticality": "tier3"}
        self.delete(params=params)
        merged_assets = MergedAsset.documents.search({})
        # nothing should get deleted
        self.assertEqual(3, len(merged_assets))

        params = {"criticality": "tier2"}
        self.delete(params=params)
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(0, len(merged_assets))

    def test_delete_hosts_by_query_permissions(self):
        self.set_org_permissions(
            self.organization, {AssetInventoryPermission.DELETE_ASSET}
        )
        for i, org in enumerate(
            [self.organization] * 2 + [self.child_organization] * 2
        ):
            MergedHostFactory.create(
                metadata__organization_id=org.id,
                source_assets=[
                    MergedSourceHostFactory(
                        attributes__hostname=f"host{i + 1}",
                        attributes__criticality="tier2",
                    )
                ],
            )

        # Deleting hosts from the parent org should succeed
        query = "hostname: host1"
        self.delete(query=query)
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(3, len(merged_assets))

        # Deleting hosts from both orgs should raise 403
        params = {"criticality": "tier2"}
        self.delete(params=params, status_code=403)
        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(3, len(merged_assets))

    def test_delete_hosts_permissions_batch(self):
        self.set_org_permissions(
            self.organization, {AssetInventoryPermission.DELETE_ASSET}
        )
        MergedHostFactory.create_batch(
            size=5, metadata__organization_id=self.organization.id
        )
        MergedHostFactory.create_batch(
            size=5, metadata__organization_id=self.child_organization.id
        )

        batch_size = 3
        with patch(
            "apps.api.v1.routers.hosts.chunks",
            side_effect=lambda *args: chunks(args[0], batch_size),
        ):
            self.delete(status_code=403)

        merged_assets = MergedAsset.documents.search({})
        # everything in first batch should be deleted, second batch should fail
        self.assertEqual(10 - batch_size, len(merged_assets))

    def test_delete_hosts_audit_logs(self):
        self.set_org_permissions(
            self.organization, {AssetInventoryPermission.DELETE_ASSET}
        )
        asset = MergedHostFactory.create(metadata__organization_id=self.organization.id)

        with patch.object(service_bus, "send_command") as m_send_command:
            self.delete(status_code=200)

        m_send_command.assert_called_once()
        command, audit_log = m_send_command.call_args[0]

        self.assertEqual(COMMAND_AUDIT_LOG_CREATE, command)

        self.assertEqual(str(audit_log.entity.id), asset.id)
        self.assertEqual(audit_log.entity.type_id, EntityTypeId.MERGED_ASSET)
        self.assertEqual(str(audit_log.entity.account_id), asset.metadata.account_id)
        self.assertEqual(
            str(audit_log.entity.organization_id), asset.metadata.organization_id
        )

        self.assertEqual(audit_log.action, Action.DELETE)
        self.assertEqual(audit_log.action_by_id, self.user_id)
        self.assertDictEqual(
            audit_log.details,
            {
                "hostname": asset.merged_data.hostname,
                "ip": asset.merged_data.primary_ip_address,
                "type_id": "host",
            },
        )
