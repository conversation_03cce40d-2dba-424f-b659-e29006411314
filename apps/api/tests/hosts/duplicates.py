from apps.api.tests.hosts.base import HostBaseTestMixin
from apps.api.v1.schemas.filter import DuplicateHostSearchFieldname
from apps.assets.tests.es_base import ESCaseMixin
from apps.tests.base import BaseTestCase
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import MergedSourceHostFactory


class DuplicateHostsTests(HostBaseTestMixin, ESCaseMixin, BaseTestCase):
    def setUp(self):
        super().setUp()

    def test_get_duplicate_hosts_by_hostname(self):
        hosts = self.create_hosts(
            "attributes__hostname",
            ["localhost1", "localhost1", "localhost2", "localhost2", "localhost3"],
        )
        self.assert_duplicates("hostname", hosts, 2)
        self.assert_duplicates("hostname", hosts, 2, include_details=False)

    def test_get_duplicate_hosts_by_hostname_blank_values(self):
        hosts = self.create_hosts(
            "attributes__hostname",
            ["", "", "localhost2", "localhost2", "localhost3"],
        )
        self.assert_duplicates("hostname", hosts, 1)
        self.assert_duplicates("hostname", hosts, 1, include_details=False)

    def test_get_duplicate_hosts_by_mac_address(self):
        hosts = self.create_hosts(
            "attributes__universal_mac_address",
            [
                ["00:00:00:00:00:00"],
                ["00:00:00:00:00:00"],
                ["00:00:00:00:00:01"],
                ["00:00:00:00:00:01"],
                ["00:00:00:00:00:02"],
            ],
        )
        self.assert_duplicates("primary_mac_address", hosts, 2)
        self.assert_duplicates("primary_mac_address", hosts, 2, include_details=False)

    def test_get_duplicate_hosts_by_ip_address(self):
        hosts = self.create_hosts(
            "attributes__primary_ip_address",
            ["127.0.0.1", "127.0.0.1", "*********", "*********", "*********"],
        )
        self.assert_duplicates("primary_ip_address", hosts, 2)
        self.assert_duplicates("primary_ip_address", hosts, 2, include_details=False)

    def test_get_duplicate_hosts_no_duplicates(self):
        MergedHostFactory.create_batch(
            5, metadata__organization_id=self.organization.id
        )
        query = {
            "duplicate_search_fieldname": DuplicateHostSearchFieldname.HOSTNAME.value
        }
        response = self.get_duplicates(query=query)
        objects = response.json()["duplicates"]

        # Check that the response contains no duplicate hosts
        self.assertEqual(
            0,
            len(objects),
            f"Expected no duplicate groups, but got {len(objects)}",
        )

    def get_duplicates(self, status_code=200, query=None, params=None):
        params = params or {}
        path = "/api/v1/hosts/duplicates"

        if query:
            params.update(query)

        response = self.api_client.get(path, params=params)
        self.assertEqual(status_code, response.status_code, response.json())
        return response

    def create_hosts(self, attribute, values):
        hosts = []
        for value in values:
            host = MergedHostFactory.create(
                metadata__organization_id=self.organization.id,
                source_assets=[MergedSourceHostFactory(**{attribute: value})],
            )
            hosts.append(host)
        return hosts

    def assert_duplicates(
        self, attribute, hosts, expected_duplicates_count, include_details=True
    ):
        query = {
            "duplicate_search_fieldname": attribute,
            "include_details": include_details,
        }
        response = self.get_duplicates(query=query)
        duplicates = response.json()["duplicates"]

        # Check that the response contains the expected number of duplicate groups
        self.assertEqual(
            expected_duplicates_count,
            len(duplicates),
            f"Expected {expected_duplicates_count} duplicate groups, but got {len(duplicates)}",
        )

        # Determine the expected duplicate values
        value_counts = {}
        for host in hosts:
            value = getattr(host.merged_data, attribute)
            if value not in value_counts:
                value_counts[value] = 0
            value_counts[value] += 1

        expected_duplicate_values = [
            value for value, count in value_counts.items() if count > 1 and value != ""
        ]

        # Check that the response contains the correct attribute values
        for value in expected_duplicate_values:
            self.assertIn(
                value,
                duplicates.keys(),
                f"{attribute} {value} not found in duplicates",
            )

        for value in value_counts:
            if value not in expected_duplicate_values:
                self.assertNotIn(
                    value,
                    duplicates.keys(),
                    f"{attribute} {value} should not be in duplicates",
                )

        if include_details:
            # Check that the response contains the correct host ids
            for value in expected_duplicate_values:
                expected_ids = [
                    str(host.id)
                    for host in hosts
                    if getattr(host.merged_data, attribute) == value
                ]
                actual_ids = [d["id"] for d in duplicates[value]]
                self.assertCountEqual(
                    expected_ids,
                    actual_ids,
                    f"Host IDs for {attribute} {value} do not match",
                )
        else:
            # Check that the response contains the correct host counts from the DuplicateHostSummaryResult
            for value in expected_duplicate_values:
                self.assertEqual(
                    value_counts[value],
                    duplicates[value]["count"],
                    f"Expected count for {attribute} {value} does not match",
                )
