from datetime import timed<PERSON><PERSON>
from uuid import uuid4

from django.utils import timezone

from apps.accounts.models import Organization
from apps.api.tests.hosts.base import HostBaseTestMixin
from apps.assets.models import (
    AssetCriticality,
    AssetType,
    HostType,
    MergedAsset,
    OsFamily,
)
from apps.assets.models.merged_asset import MergedAssetElasticSearchManager
from apps.assets.tests.es_base import ESCaseMixin
from apps.integrations.models import Integration, Technology
from apps.tests.base import ApiDatabaseTestCase, BaseTestCase
from factories.integration import IntegrationFactory, TechnologyFactory
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import HostMetadataFactory, MergedSourceHostFactory


class HostReadTests(HostBaseTestMixin, ESCaseMixin, BaseTestCase):
    def test_get_by_id(self):
        host = MergedHostFactory.create(metadata__organization_id=self.organization.id)
        response = self.get(host_id=host.id)
        self.assertEqual(host.id, response.json()["id"])

    def test_get_by_id_assure_aad_id(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["defender_atp"],
        )
        response = self.get(host_id=host.id)
        api_host = response.json()
        self.assertEqual(
            host.merged_data.aad_id,
            api_host["aad_id"],
        )
        self.assertEqual(
            host.source_data["defender_atp"][0].attributes.aad_id,
            api_host["sources"][0]["attributes"]["aad_id"],
        )

    def test_get_by_id_override_criticality(self):
        asset = MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_1)
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[asset],
            overrides={"criticality": "tier2"},
        )
        response = self.get(host_id=host.id)
        self.assertEqual(host.id, response.json()["id"])
        self.assertEqual(response.json()["criticality"], "tier2")

    def test_get_by_id_wrong_org(self):
        # add a host for an existing org the current user has no access to
        user_account_id = self.organization.account_id
        wrong_org = Organization.objects.exclude(account_id=user_account_id).first()
        host = MergedHostFactory.create(metadata__organization_id=wrong_org.id)
        self.get(host_id=host.id, status_code=404)

    def test_get_by_id_invalid(self):
        self.get(host_id="invalid", status_code=422)

    def test_get_by_id_not_found(self):
        MergedHostFactory.create(metadata__organization_id=self.organization.id)
        self.get(host_id=uuid4(), status_code=404)

    def test_get_detail_owner(self):
        host = MergedHostFactory.create(metadata__organization_id=self.organization.id)
        response = self.get(host_id=host.id)
        self.assertEqual(1, len(response.json()["owners"]))
        self.assertEqual(
            host.merged_data.owner[0].name, response.json()["owners"][0]["name"]
        )

    def test_get_detail_owner_no_email(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__owner__0__email=None)],
        )
        response = self.get(host_id=host.id)
        self.assertEqual(1, len(response.json()["owners"]))
        self.assertEqual(
            host.merged_data.owner[0].name, response.json()["owners"][0]["name"]
        )
        self.assertIsNone(response.json()["owners"][0]["email"])

    def test_get_detail_integration_id(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["sentinel_one"],
        )
        response = self.get(host_id=host.id)
        self.assertEqual(
            str(host.source_data["sentinel_one"][0].integration_id),
            response.json()["sources"][0]["integration"]["id"],
        )

    def test_list_parent(self):
        host_from_parent = MergedHostFactory.create(
            metadata__organization_id=self.organization.id
        )
        host_from_child = MergedHostFactory.create(
            metadata__organization_id=self.child_organization.id,
        )
        user_account_id = self.organization.account_id
        wrong_org = Organization.objects.exclude(account_id=user_account_id).first()
        MergedHostFactory.create(
            metadata__organization_id=wrong_org.id,
        )

        response = self.get()
        objects = response.json()["items"]

        # should include 1 asset from parent, 1 from child and not the 1 from an
        # unrelated org
        self.assertSetEqual(
            {host_from_parent.id, host_from_child.id},
            {host["id"] for host in objects},
        )

    def test_list_child(self):
        self.auth_as_child_org()
        host_from_child = MergedHostFactory.create(
            metadata__organization_id=self.child_organization.id,
        )
        response = self.get()
        objects = response.json()["items"]

        # should include only the 1 asset from child (leaf org)
        self.assertEqual(1, len(objects))
        self.assertEqual(host_from_child.id, objects[0]["id"])

    def test_search_host_simple(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
            ],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_3)
            ],
        )
        response = self.get(query="tier2")
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual("tier2", objects[0]["criticality"])

    def test_search_host_simple_wildcard(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
            ],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_3)
            ],
        )

        # wildcard is added by the api
        response = self.get(query="tier")
        objects = response.json()["items"]
        self.assertSetEqual({"tier2", "tier3"}, {obj["criticality"] for obj in objects})

    def test_search_host_leading_wildcard(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
            ],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_3)
            ],
        )

        # wildcard is added by the api
        response = self.get(query="ier")
        objects = response.json()["items"]
        self.assertSetEqual({"tier2", "tier3"}, {obj["criticality"] for obj in objects})

    def test_search_merged_data(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
            ],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_3)
            ],
        )
        response = self.get(query="criticality: tier2")
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual("tier2", objects[0]["criticality"])

    def test_search_host_mac_address(self):
        should_find = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    attributes__universal_mac_address=["20:e9:b4:ad:5a:08"]
                )
            ],
        )
        response = self.get(query="20:e9:b4:ad:5a:08")
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(str(should_find.id), objects[0]["id"])

        response = self.get(query='"20:e9:b4:ad:5a:08"')
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(str(should_find.id), objects[0]["id"])

    def test_search_product_data(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["sentinel_one", "azure_ad"],
        )
        sentinel_one_fqdn = host.source_data["sentinel_one"][0].attributes.fqdn[0]
        response = self.get(query=f"sentinel_one.fqdn: {sentinel_one_fqdn}")
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(host.id, objects[0]["id"])

    def test_search_by_source_id(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["sentinel_one", "azure_ad"],
        )
        sentinel_one_id = host.source_data["sentinel_one"][0].metadata.asset.source_id
        response = self.get(query=f"sentinel_one.source_id: {sentinel_one_id}")
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(host.id, objects[0]["id"])

    def test_search_by_aad_id(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["sentinel_one", "azure_ad"],
        )
        aad_id = host.merged_data.aad_id
        response = self.get(query=f"aad_id: {aad_id}")
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(host.id, objects[0]["id"])

    def test_search_by_identifier_in(self):
        sentinel_one_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["sentinel_one"],
        )
        azure_ad_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["azure_ad"],
        )
        # Create unrelated host to make sure the search is not returning them
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["azure_ad"],
        )
        sentinel_one_id = sentinel_one_host.source_data["sentinel_one"][
            0
        ].metadata.asset.source_id
        aad_id = azure_ad_host.merged_data.aad_id
        response = self.get(
            query=f'identifier in ("sentinel_one:{sentinel_one_id}", "aad_id:{aad_id}")'
        )
        objects = response.json()["items"]
        self.assertEqual(2, len(objects))
        self.assertSetEqual(
            {sentinel_one_host.id, azure_ad_host.id},
            {host["id"] for host in objects},
        )

    def test_search_by_id_not_in(self):
        sentinel_one_host_1 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["sentinel_one"],
        )
        sentinel_one_host_2 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["sentinel_one"],
        )

        response = self.get(query=f'not id in ("{sentinel_one_host_1.id}")')
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertSetEqual(
            {sentinel_one_host_2.id},
            {host["id"] for host in objects},
        )

    def test_search_by_source_id_in(self):
        sentinel_one_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["sentinel_one", "azure_ad"],
        )
        defender_atp_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["defender_atp", "azure_ad"],
        )
        sentinel_one_id = sentinel_one_host.source_data["sentinel_one"][
            0
        ].metadata.asset.source_id
        defender_atp_id = defender_atp_host.source_data["defender_atp"][
            0
        ].metadata.asset.source_id
        response = self.get(
            query=f'source_id in ("sentinel_one:{sentinel_one_id}", "defender_atp:{defender_atp_id}")'
        )
        objects = response.json()["items"]
        self.assertEqual(2, len(objects))
        self.assertSetEqual(
            {sentinel_one_host.id, defender_atp_host.id},
            {host["id"] for host in objects},
        )

    def test_search_by_criticality_in(self):
        host_tier_2 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
            ],
        )
        host_tier_3 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_3)
            ],
        )
        response = self.get(
            query=f'criticality in ("{AssetCriticality.TIER_2}", "{AssetCriticality.TIER_3}")'
        )
        objects = response.json()["items"]
        self.assertEqual(2, len(objects))
        self.assertSetEqual(
            {host_tier_2.id, host_tier_3.id},
            {host["id"] for host in objects},
        )

    def test_search_by_identifier_not_in(self):
        sentinel_one_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["sentinel_one"],
        )
        azure_ad_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["azure_ad"],
        )
        other_azure_ad_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["azure_ad"],
        )
        sentinel_one_id = sentinel_one_host.source_data["sentinel_one"][
            0
        ].metadata.asset.source_id
        aad_id = azure_ad_host.merged_data.aad_id
        response = self.get(
            query=f'not identifier in ("sentinel_one:{sentinel_one_id}", "aad_id:{aad_id}")'
        )
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertSetEqual(
            {other_azure_ad_host.id},
            {host["id"] for host in objects},
        )

    def test_search_lt_gt(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__hostname="host1")],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__hostname="host3")],
        )

        response = self.get(query="hostname > host0 hostname < host2")
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual("host1", objects[0]["hostname"])

    def test_search_invert_lt_gt(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__hostname="host1")],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__hostname="host3")],
        )

        response = self.get(query="not hostname < host1 not hostname > host1")
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual("host1", objects[0]["hostname"])

    def test_search_invert_exact(self):
        should_be_found_hosts = [
            MergedHostFactory.build(
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        attributes__criticality=AssetCriticality.TIER_3
                    )
                ],
            ),
            MergedHostFactory.build(
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        attributes__criticality=AssetCriticality.UNKNOWN
                    )
                ],
            ),
        ]
        MergedAssetElasticSearchManager.create_bulk(should_be_found_hosts)
        should_not_be_found_hosts = [
            MergedHostFactory.build(
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        attributes__criticality=AssetCriticality.TIER_0
                    )
                ],
            ),
            MergedHostFactory.build(
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        attributes__criticality=AssetCriticality.TIER_0
                    )
                ],
            ),
        ]
        MergedAssetElasticSearchManager.create_bulk(should_not_be_found_hosts)

        response = self.get(query="not criticality: tier0")
        objects = response.json()["items"]
        self.assertSetEqual(
            {host.id for host in should_be_found_hosts},
            {host["id"] for host in objects},
        )

    def test_search_any(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__hostname="host1")],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__hostname="host2")],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__hostname="host3")],
        )
        response = self.get(query="hostname: *")
        objects = response.json()["items"]
        self.assertEqual(3, len(objects))

    def test_search_invert_any(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__hostname="host1")],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__hostname="host2")],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__hostname="host3")],
        )
        response = self.get(query="not hostname: *")
        objects = response.json()["items"]
        self.assertEqual(0, len(objects))

    def test_search_no_matches(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__hostname="host1")],
        )
        response = self.get(query="hostname: notfound")
        objects = response.json()["items"]
        self.assertEqual(0, len(objects))

    def test_filter_last_seen(self):
        now = timezone.now()
        host_now = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(metadata__asset__last_seen=now)],
        )
        host_yesterday = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__asset__last_seen=now - timedelta(days=1)
                )
            ],
        )
        host_day_and_half_ago = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__asset__last_seen=now - timedelta(days=1.5)
                )
            ],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__asset__last_seen=now - timedelta(days=3)
                )
            ],
        )

        response = self.get(params={"last_seen": "2 days"})
        objects = response.json()["items"]
        self.assertEqual(3, len(objects))
        self.assertSetEqual(
            {host.id for host in [host_now, host_yesterday, host_day_and_half_ago]},
            {host["id"] for host in objects},
        )

    def test_filter_last_seen_onetech_sort(self):
        now = timezone.now()
        host_now = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__asset__last_seen=now,
                    metadata__integration__technology_id="sentinel_one",
                ),
                MergedSourceHostFactory(
                    metadata__asset__last_seen=now - timedelta(days=1.5),
                    metadata__integration__technology_id="sentinel_one",
                ),
            ],
            technology_ids=["sentinel_one"],
        )
        host_yesterday = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__asset__last_seen=now - timedelta(days=1),
                    metadata__integration__technology_id="sentinel_one",
                )
            ],
            technology_ids=["sentinel_one"],
        )

        response = self.get(
            params={
                "technology": "sentinel_one",
                "order_by": "last_seen",
            }
        )
        objects = response.json()["items"]
        self.assertEqual(2, len(objects))
        self.assertEqual(
            [str(host_yesterday.id), str(host_now.id)],
            [objects[0]["id"], objects[1]["id"]],
        )

    def test_filter_last_seen_in_range(self):
        now = timezone.now()
        host_now = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(metadata__asset__last_seen=now)],
        )
        host_yesterday = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__asset__last_seen=now - timedelta(days=1)
                )
            ],
        )
        host_day_and_half_ago = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__asset__last_seen=now - timedelta(days=1.5)
                )
            ],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__asset__last_seen=now - timedelta(days=3)
                )
            ],
        )

        now = timezone.now()
        older = now - timedelta(days=2)
        response = self.get(params={"last_seen": f"{older}&{now}"})
        objects = response.json()["items"]
        self.assertEqual(3, len(objects))
        self.assertSetEqual(
            {host.id for host in [host_now, host_yesterday, host_day_and_half_ago]},
            {host["id"] for host in objects},
        )

    def test_filter_last_seen_out_range(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(metadata__asset__last_seen=timezone.now())
            ],
        )
        response = self.get(
            params={"last_seen": "2022-01-01T00:00:00Z&2022-12-01T00:00:00Z"}
        )
        objects = response.json()["items"]
        self.assertEqual(0, len(objects))

    def test_filter_last_seen_not_out_range(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(metadata__asset__last_seen=timezone.now())
            ],
        )
        response = self.get(
            params={"last_seen": "!2022-01-01T00:00:00Z&2022-12-01T00:00:00Z"}
        )
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))

    def test_filter_last_seen_endpoint_security_with_gap(self):
        now = timezone.now()
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="tenable_io", asset__last_seen=now
                    ),
                ),
            ],
        )
        response = self.get(
            params={"last_seen": "1day", "last_seen_endpoint_security": "!1day"}
        )
        objects = response.json()["items"]
        self.assertEqual(
            0, len(objects), "Should find 0 hosts because it has an edr coverage gap"
        )

    def test_filter_last_seen_endpoint_security(self):
        now = timezone.now()
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="tenable_io",
                        asset__last_seen=now,
                        integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                    ),
                ),
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="sentinel_one",
                        asset__last_seen=now - timedelta(days=10),
                        integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                    ),
                ),
            ],
        )
        response = self.get(
            params={"last_seen": "1day", "last_seen_endpoint_security": "!1day"}
        )
        objects = response.json()["items"]
        self.assertEqual(1, len(objects), "Should find 1 host")

        response = self.get(
            params={"last_seen": "1day", "last_seen_endpoint_security": "!11day"}
        )
        objects = response.json()["items"]
        self.assertEqual(0, len(objects), "Should find 0 hosts")

    def test_filter_last_seen_vulnerability_management(self):
        now = timezone.now()
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="azure_ad",
                        integration__vulnerability_coverage_mode=Integration.CoverageMode.NOT_APPLICABLE,
                        asset__last_seen=now,
                    ),
                ),
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="falcon_em",
                        integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                        asset__last_seen=now - timedelta(days=10),
                    ),
                ),
            ],
        )
        response = self.get(
            params={"last_seen": "1day", "last_seen_vulnerability_management": "!1day"}
        )
        objects = response.json()["items"]
        self.assertEqual(1, len(objects), "Should find 1 host")

        response = self.get(
            params={"last_seen": "1day", "last_seen_vulnerability_management": "!11day"}
        )
        objects = response.json()["items"]
        self.assertEqual(0, len(objects), "Should find 0 hosts")

    def test_filter_last_seen_with_endpoint_security(self):
        now = timezone.now()
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="azure_ad",
                        integration__endpoint_coverage_mode=Integration.CoverageMode.NOT_APPLICABLE,
                        asset__last_seen=now,
                    ),
                ),
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="cb_cloud",
                        integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                        asset__last_seen=now - timedelta(days=10),
                    ),
                ),
            ],
        )
        response = self.get(
            params={"last_seen": "1day", "last_seen_endpoint_security": "!1day"}
        )
        objects = response.json()["items"]
        self.assertEqual(1, len(objects), "Should find 1 host")

        response = self.get(
            params={"last_seen": "1day", "last_seen_endpoint_security": "!11day"}
        )
        objects = response.json()["items"]
        self.assertEqual(0, len(objects), "Should find 0 hosts")

    def test_filter_created(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
        )
        response = self.get(params={"created": "2day"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))

        response = self.get(params={"created": "!2day"})
        objects = response.json()["items"]
        self.assertEqual(0, len(objects))

    def test_filter_criticality_unknown(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
            ],
        )
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    attributes__criticality=AssetCriticality.UNKNOWN
                )
            ],
        )

        response = self.get(params={"criticality": "unknown"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(host.id, objects[0]["id"])

    def test_filter_criticality_tiered(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
            ],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    attributes__criticality=AssetCriticality.UNKNOWN
                )
            ],
        )
        response = self.get(params={"criticality": "tier2"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(host.id, objects[0]["id"])

    def test_filter_criticality_override(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    attributes__criticality=AssetCriticality.UNKNOWN
                )
            ],
            overrides={"criticality": AssetCriticality.TIER_2},
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    attributes__criticality=AssetCriticality.UNKNOWN
                )
            ],
        )
        response = self.get(params={"criticality": "tier2"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(host.id, objects[0]["id"])

    def test_filter_criticality_multi(self):
        should_be_found_hosts = [
            MergedHostFactory.build(
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        attributes__criticality=AssetCriticality.TIER_2
                    )
                ],
            ),
            MergedHostFactory.build(
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        attributes__criticality=AssetCriticality.UNKNOWN
                    )
                ],
            ),
        ]
        MergedAssetElasticSearchManager.create_bulk(should_be_found_hosts)
        should_not_be_found_hosts = [
            MergedHostFactory.build(
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        attributes__criticality=AssetCriticality.TIER_1
                    )
                ],
            ),
            MergedHostFactory.build(
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        attributes__criticality=AssetCriticality.TIER_4
                    )
                ],
            ),
        ]
        MergedAssetElasticSearchManager.create_bulk(should_not_be_found_hosts)

        response = self.get(
            params={
                "criticality": [
                    AssetCriticality.TIER_2.value,
                    AssetCriticality.UNKNOWN.value,
                ]
            }
        )
        objects = response.json()["items"]
        self.assertSetEqual(
            {host.id for host in should_be_found_hosts},
            {host["id"] for host in objects},
        )

    def test_filter_criticality_multi_override(self):
        should_be_found_hosts = [
            MergedHostFactory.build(
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        attributes__criticality=AssetCriticality.TIER_3
                    )
                ],
                overrides={"criticality": AssetCriticality.TIER_2},
            ),
            MergedHostFactory.build(
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        attributes__criticality=AssetCriticality.UNKNOWN
                    )
                ],
            ),
        ]
        MergedAssetElasticSearchManager.create_bulk(should_be_found_hosts)
        should_not_be_found_hosts = [
            MergedHostFactory.build(
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        attributes__criticality=AssetCriticality.UNKNOWN
                    )
                ],
                overrides={"criticality": AssetCriticality.TIER_1},
            ),
            MergedHostFactory.build(
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        attributes__criticality=AssetCriticality.TIER_4
                    )
                ],
            ),
        ]
        MergedAssetElasticSearchManager.create_bulk(should_not_be_found_hosts)

        response = self.get(
            params={
                "criticality": [
                    AssetCriticality.TIER_2.value,
                    AssetCriticality.UNKNOWN.value,
                ]
            }
        )
        objects = response.json()["items"]
        self.assertSetEqual(
            {host.id for host in should_be_found_hosts},
            {host["id"] for host in objects},
        )

    def test_filter_os_family(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__os__family=OsFamily.WINDOWS)
            ],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__os__family=OsFamily.LINUX)
            ],
        )
        response = self.get(params={"os_family": "windows"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual("Windows 10", objects[0]["os"]["name"])

    def test_filter_host_type(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__os__host_type=HostType.SERVER)
            ],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__os__host_type=HostType.WORKSTATION)
            ],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__os__host_type=HostType.OTHER)
            ],
        )

        response = self.get(params={"host_type": ["server", "other"]})
        objects = response.json()["items"]
        self.assertEqual(2, len(objects))
        self.assertSetEqual(
            {HostType.SERVER, HostType.OTHER},
            {host["os"]["host_type"] for host in objects},
        )

    def test_filter_technology(self):
        sentinel_one_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["sentinel_one"],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["crowdstrike_falcon"],
        )

        response = self.get(params={"technology": "sentinel_one"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(sentinel_one_host.id, objects[0]["id"])

    def test_filter_integration(self):
        sentinel_one_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["sentinel_one"],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["crowdstrike_falcon"],
        )

        sentinel_one_source = sentinel_one_host.source_data["sentinel_one"][0]
        integration_id = sentinel_one_source.metadata.integration.id

        response = self.get(params={"integration": integration_id})
        objects = response.json()["items"]

        self.assertEqual(1, len(objects))
        self.assertEqual(sentinel_one_host.id, objects[0]["id"])

    def test_filter_technologies(self):
        sentinel_one_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["sentinel_one", "tenable_io"],
        )
        crowdstrike_host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["crowdstrike_falcon", "tenable_io"],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["tenable_io"],
        )
        response = self.get(
            params={"technology": ["sentinel_one", "crowdstrike_falcon"]}
        )
        objects = response.json()["items"]
        self.assertEqual(2, len(objects))
        self.assertSetEqual(
            {host.id for host in [sentinel_one_host, crowdstrike_host]},
            {host["id"] for host in objects},
        )

    def test_vulnerability_management_combined_filter(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="qualys_vmpc",
                    metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
                MergedSourceHostFactory(
                    metadata__integration__technology_id="tenable_io",
                    metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
            ],
        )

        host2 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="qualys_vmpc",
                    metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
            ],
        )

        # Search for the combined qualys_vmpc and tenable_io hosts
        merged_assets = self.get(
            params={"vulnerability_management_combined": "qualys_vmpc,tenable_io"}
        ).json()

        self.assertEqual(1, len(merged_assets["items"]))
        self.assertEqual(host.id, merged_assets["items"][0]["id"])

        # Search for the qualys_vmpc only hosts
        merged_assets = self.get(
            params={"vulnerability_management_combined": "qualys_vmpc"}
        ).json()

        self.assertEqual(1, len(merged_assets["items"]))
        self.assertEqual(host2.id, merged_assets["items"][0]["id"])

    def test_endpoint_security_combined_filter(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="carbon_black",
                    metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
                MergedSourceHostFactory(
                    metadata__integration__technology_id="cb_cloud",
                    metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
            ],
        )

        host2 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="carbon_black",
                    metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                ),
            ],
        )

        # Search for the combined qualys_vmpc and tenable_io hosts
        merged_assets = self.get(
            params={"endpoint_security_combined": "carbon_black,cb_cloud"}
        ).json()

        self.assertEqual(1, len(merged_assets["items"]))
        self.assertEqual(host.id, merged_assets["items"][0]["id"])

        # Search for the qualys_vmpc only hosts
        merged_assets = self.get(
            params={"endpoint_security_combined": "carbon_black"}
        ).json()

        self.assertEqual(1, len(merged_assets["items"]))
        self.assertEqual(host2.id, merged_assets["items"][0]["id"])

    def test_order_by_frontend_fields(self):
        MergedHostFactory.create(metadata__organization_id=self.organization.id)
        frontend_sort_fields = [
            "hostname",
            "last_seen",
            "created",
            "os_name",
            "primary_ip_address",
            "primary_mac_address",
            "criticality",
        ]

        for field in frontend_sort_fields:
            self.get(params={"order_by": field})
            self.get(params={"order_by": "-" + field})

    def test_order_by_technology_last_seen(self):
        technologies = [
            "sentinel_one",
            "crowdstrike_falcon",
            "tenable_io",
        ]
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=technologies,
        )
        for t in technologies:
            self.get(params={"order_by": "last_seen", "technology": t})
            self.get(params={"order_by": "-last_seen", "technology": t})

    def test_order_by_no_ip_address(self):
        MergedHostFactory.create(metadata__organization_id=self.organization.id)
        for i in range(3):
            MergedHostFactory.create(
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(attributes__primary_ip_address=None)
                ],
            )
        self.get(params={"order_by": "primary_ip_address", "limit": 2})

    def test_order_by_criticality(self):
        host1 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__criticality="tier4")],
        )
        host2 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__criticality="tier1")],
        )
        host3 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            overrides={"criticality": "tier2"},
        )

        response = self.get(params={"order_by": "criticality"})
        sorted_ids = [item["id"] for item in response.json()["items"]]
        self.assertEqual([host2.id, host3.id, host1.id], sorted_ids)

        response = self.get(params={"order_by": "-criticality"})
        sorted_ids = [item["id"] for item in response.json()["items"]]
        self.assertEqual([host1.id, host3.id, host2.id], sorted_ids)

    def test_order_by_created(self):
        host1 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["defender_atp"],
        )
        host2 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["sentinel_one"],
        )

        response = self.get(params={"order_by": "created"})
        items = response.json()["items"]
        item1, item2 = items
        self.assertTrue(item1["created"] < item2["created"])
        self.assertEqual([host1.id, host2.id], [item1["id"], item2["id"]])

        response = self.get(params={"order_by": "-created"})
        items = response.json()["items"]
        item1, item2 = items
        self.assertTrue(item1["created"] > item2["created"])
        self.assertEqual([host2.id, host1.id], [item1["id"], item2["id"]])

    def test_search_invalid_criticality(self):
        self.get(params={"criticality": "invalid"}, status_code=422)

    def test_search_invalid_order_by(self):
        self.get(params={"order_by": "invalid"}, status_code=422)

    def test_filter_manually_merged(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["defender_atp"],
            metadata__manually_merged=True,
        )
        items = self.get().json()["items"]
        self.assertEqual(1, len(items))
        self.assertTrue(items[0]["manually_merged"])

        items = self.get(params={"manually_merged": True}).json()["items"]
        self.assertEqual(1, len(items))
        self.assertTrue(items[0]["manually_merged"])

        items = self.get(params={"manually_merged": False}).json()["items"]
        self.assertEqual(0, len(items))


class HostReadTestWithDatabase(HostBaseTestMixin, ESCaseMixin, ApiDatabaseTestCase):
    def test_endpoint_coverage_gap_exclude_filter(self):
        tenable_tech = TechnologyFactory.create(technology_id="tenable_io")
        falcon_em_tech = TechnologyFactory.create(technology_id="falcon_em")
        defender_atp_tech = TechnologyFactory.create(technology_id="defender_atp")

        IntegrationFactory.create(
            organization=self.organization,
            technology=tenable_tech,
            vulnerability_coverage_mode=Integration.CoverageMode.IGNORE,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology=falcon_em_tech,
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology=defender_atp_tech,
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # Create a host that has only endpoint security coverage gap.
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="tenable_io",
                    ),
                ),
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="falcon_em",
                        integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                    ),
                ),
            ],
        )
        self._assert_coverage_gap_excluded(
            host,
            "endpoint_security",
            "endpoint_security_excluded",
        )

    def test_endpoint_coverage_gap_exclude_filter_for_endpoint_security(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="carbon_black",
            endpoint_coverage_mode=Integration.CoverageMode.IGNORE,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="cb_cloud",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="tenable_io",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # Create a host that has only endpoint security coverage gap.
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="carbon_black",
                    ),
                ),
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="cb_cloud",
                        integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                    ),
                ),
            ],
        )
        self._assert_coverage_gap_excluded(
            host,
            "vulnerability_management",
            "vulnerability_management_excluded",
        )

    def test_vuln_coverage_gap_exclude_filter(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="tenable_io",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["defender_atp"],
        )
        self._assert_coverage_gap_excluded(
            host,
            "vulnerability_management",
            "vulnerability_management_excluded",
        )

    def test_vuln_coverage_gap_exclude_filter_for_endpoint_security(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="carbon_black",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["tenable_io"],
        )
        self._assert_coverage_gap_excluded(
            host,
            "endpoint_security",
            "endpoint_security_excluded",
        )

    def _assert_coverage_gap_excluded(self, host, coverage_gap, excluded_param):
        # Ensure the host is returned as a coverage gap.
        merged_assets = self.get(params={"coverage_gap": coverage_gap}).json()
        self.assertEqual(1, len(merged_assets["items"]))
        self.assertEqual(host.id, merged_assets["items"][0]["id"])
        self.assertEqual([coverage_gap], merged_assets["items"][0]["coverage_gaps"])

        # Request the host as not an endpoint security excluded host.
        merged_assets = self.get(params={excluded_param: False}).json()
        self.assertEqual(1, len(merged_assets["items"]))

        # Exclude the host from the coverage gap.
        body = {"exclude": True, "coverage_gap": coverage_gap}
        self.put(host_id=host.id, field="coverage_gap_excluded", body=body)

        # Ensure the host is no longer returned as a coverage gap.
        merged_assets = self.get(params={"coverage_gap": coverage_gap}).json()
        self.assertEqual(0, len(merged_assets["items"]))

        # Request the host as an endpoint security excluded host.
        merged_assets = self.get(params={excluded_param: True}).json()
        self.assertEqual(1, len(merged_assets["items"]))
        self.assertEqual([], merged_assets["items"][0]["coverage_gaps"])

    def test_filter_any_gaps(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="tenable_io",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="defender_atp",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # create host seen by no endpoint security sources
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["tenable_io"],
        )
        response = self.get(params={"coverage_gap": "any"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(
            {"endpoint_security", "vulnerability_management"},
            set(objects[0]["coverage_gaps"]),
        )

    def test_filter_any_gaps_for_endpoint_security(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="carbon_black",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="tenable_io",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # create host seen by no endpoint security sources
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["carbon_black"],
        )
        response = self.get(params={"coverage_gap": "any"})
        objects = response.json()["items"]

        self.assertEqual(1, len(objects))
        self.assertEqual(
            {
                "vulnerability_management",
                "endpoint_security",
            },
            set(objects[0]["coverage_gaps"]),
        )

    def test_filter_all_explicit(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="tenable_io",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="crowdstrike_falcon",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # this odd query means "give me everything" so let's make sure it does
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["tenable_io"],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["crowdstrike_falcon"],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["tenable_io", "crowdstrike_falcon"],
        )
        response = self.get(params={"coverage_gap": ["any", "no_gaps"]})
        objects = response.json()["items"]
        self.assertEqual(3, len(objects))

    def test_filter_all_explicit_for_endpoint_security(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="carbon_black",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="tenable_io",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # this odd query means "give me everything" so let's make sure it does
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["carbon_black"],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["tenable_io"],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["carbon_black", "tenable_io"],
        )
        response = self.get(params={"coverage_gap": ["any", "no_gaps"]})
        objects = response.json()["items"]
        self.assertEqual(3, len(objects))

    def test_filter_azure_ad_gap(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="tenable_io",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # create host seen by crowdstrike EDR and EM
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["crowdstrike_falcon", "falcon_em"],
        )
        response = self.get(params={"coverage_gap": "azure_ad"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(["vulnerability_management"], objects[0]["coverage_gaps"])

    def test_filter_azure_ad_gap_for_endpoint_security(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="carbon_black",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # create host seen by crowdstrike EDR and EM
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["tenable_io", "crowdstrike_falcon"],
        )
        response = self.get(params={"coverage_gap": "azure_ad"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(["endpoint_security"], objects[0]["coverage_gaps"])

    def test_filter_endpoint_gaps(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="tenable_io",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="crowdstrike_falcon",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # create host only seen by tenable
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["tenable_io"],
        )
        response = self.get(params={"coverage_gap": "endpoint_security"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(
            {"endpoint_security", "vulnerability_management"},
            set(objects[0]["coverage_gaps"]),
        )

    def test_endpoint_gaps_disabled(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="tenable_io",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="crowdstrike_falcon",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
            enabled=False,
        )
        # create host only seen by tenable
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["tenable_io"],
        )
        response = self.get()
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(
            {"vulnerability_management"},
            set(objects[0]["coverage_gaps"]),
        )

    def test_filter_vulnerability_gaps(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="tenable_io",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # create host only seen by crowdstrike (EDR)
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["crowdstrike_falcon"],
        )
        response = self.get(params={"coverage_gap": "vulnerability_management"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(["vulnerability_management"], objects[0]["coverage_gaps"])

    def test_vulnerability_gaps_disabled(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="tenable_io",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
            enabled=False,
        )
        # create host only seen by crowdstrike (EDR)
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["crowdstrike_falcon"],
        )
        response = self.get()
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual([], objects[0]["coverage_gaps"])

    def test_filter_technical_security_control(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="tenable_io",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="crowdstrike_falcon",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # create host only seen by tenable
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["tenable_io"],
        )
        response = self.get(params={"coverage_gap": "technical_security_control"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(
            {"endpoint_security", "vulnerability_management"},
            set(objects[0]["coverage_gaps"]),
        )

    def test_filter_technical_security_control_endpoint_security(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="carbon_black",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="tenable_io",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # create host only seen by carbon_black
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["carbon_black"],
        )
        response = self.get(params={"coverage_gap": "technical_security_control"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(
            {"endpoint_security", "vulnerability_management"},
            set(objects[0]["coverage_gaps"]),
        )

    def test_filter_no_gaps(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="falcon_em",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="crowdstrike_falcon",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # create host seen by crowdstrike EDR and EM
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="crowdstrike_falcon",
                        integration__vulnerability_coverage_mode=Integration.CoverageMode.NOT_APPLICABLE,
                        integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                    ),
                ),
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="falcon_em",
                        integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                        integration__endpoint_coverage_mode=Integration.CoverageMode.NOT_APPLICABLE,
                    ),
                ),
            ],
        )
        response = self.get(params={"coverage_gap": "no_gaps"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual([], objects[0]["coverage_gaps"])

    def test_filter_no_gaps_for_endpoint_security(self):
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="carbon_black",
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology_id="tenable_io",
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # create host seen by crowdstrike EDR and EM
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="tenable_io",
                        integration__endpoint_coverage_mode=Integration.CoverageMode.NOT_APPLICABLE,
                        integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                    ),
                ),
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="carbon_black",
                        integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                        integration__vulnerability_coverage_mode=Integration.CoverageMode.NOT_APPLICABLE,
                    ),
                ),
            ],
        )
        response = self.get(params={"coverage_gap": "no_gaps"})
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual([], objects[0]["coverage_gaps"])

    def test_filter_multiple_gaps(self):
        from apps.integrations.models import Technology
        falcon_em_tech, _ = Technology.objects.get_or_create(
            technology_id="falcon_em",
            defaults={'name': 'Falcon EM'}
        )
        crowdstrike_tech, _ = Technology.objects.get_or_create(
            technology_id="crowdstrike_falcon",
            defaults={'name': 'CrowdStrike Falcon'}
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology=falcon_em_tech,
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology=crowdstrike_tech,
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # create host only seen by crowdstrike (EDR)
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["crowdstrike_falcon"],
        )
        # create host only seen by tenable
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["tenable_io"],
        )
        # create host seen by crowdstrike EDR and EM
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="crowdstrike_falcon",
                        integration__vulnerability_coverage_mode=Integration.CoverageMode.NOT_APPLICABLE,
                    ),
                ),
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="falcon_em",
                        integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                    ),
                ),
            ],
        )
        response = self.get(params={"coverage_gap": ["vulnerability_management"]})
        objects = response.json()["items"]
        self.assertEqual(2, len(objects))

    def test_filter_multiple_gaps_for_endpoint_security(self):
        crowdstrike_tech, _ = Technology.objects.get_or_create(
            technology_id="crowdstrike_falcon",
            defaults={'name': 'CrowdStrike Falcon'}
        )
        falcon_em_tech, _ = Technology.objects.get_or_create(
            technology_id="falcon_em",
            defaults={'name': 'Falcon EM'}
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology=crowdstrike_tech,
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        IntegrationFactory.create(
            organization=self.organization,
            technology=falcon_em_tech,
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        # create host only seen by crowdstrike (EDR)
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["falcon_em"],
        )
        # create host only seen by tenable
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["carbon_black"],
        )
        # create host seen by crowdstrike EDR and EM
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="falcon_em",
                        integration__endpoint_coverage_mode=Integration.CoverageMode.NOT_APPLICABLE,
                    ),
                ),
                MergedSourceHostFactory(
                    metadata=HostMetadataFactory(
                        integration__technology_id="crowdstrike_falcon",
                        integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                    ),
                ),
            ],
        )
        response = self.get(
            params={"coverage_gap": ["vulnerability_management", "endpoint_security"]}
        )
        objects = response.json()["items"]
        self.assertEqual(3, len(objects))

    def test_list_does_not_return_shells(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["falcon_em"],
        )

        ma = MergedAsset.create_shell(
            org=self.organization,
            _id="shell_id",
            asset_type=AssetType.HOST,
            created=timezone.now(),
        )
        MergedAsset.documents.create(ma)

        response = self.get()
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
