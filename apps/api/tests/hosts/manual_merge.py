from uuid import uuid4

from ata_common.utils.advisory_lock import advisory_lock

from apps.api.tests.hosts.base import HostBaseTestMixin
from apps.assets.models import IdentificationRule, MergedAsset
from apps.assets.services import merge_service
from apps.assets.tests.es_base import ESCaseMixin
from apps.tests.base import ApiDatabaseTestCase
from factories.merged_host import MergedHostFactory


class ManualMergeTests(HostBaseTestMixin, ESCaseMixin, ApiDatabaseTestCase):
    def setUp(self):
        super().setUp()

        IdentificationRule.objects.create(asset_type="host", organization=None)

    def test_manual_merge_review(self):
        hosts = MergedHostFactory.create_batch(
            metadata__organization_id=self.organization.id,
            size=3,
            technology_ids=["defender_atp"],
        )
        self.assert_manual_merge([str(host.id) for host in hosts], 200, review=True)

    def test_manual_merge_review_cross_org(self):
        host1 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["defender_atp"],
        )
        host2 = MergedHostFactory.create(
            metadata__organization_id=self.child_organization.id,
            technology_ids=["sentinel_one"],
        )
        self.assert_manual_merge([str(host1.id), str(host2.id)], 403, review=True)

    def test_manual_merge_review_cross_org_non_exist(self):
        self.assert_manual_merge([str(uuid4()), str(uuid4())], 404, review=True)

    def test_manual_merge_success(self):
        hosts = MergedHostFactory.create_batch(
            metadata__organization_id=self.organization.id,
            size=3,
            technology_ids=["defender_atp", "sentinel_one", "crowdstrike_falcon"],
        )
        host = self.assert_manual_merge([str(host.id) for host in hosts], 200)
        self.assertEqual(
            sum([host.source_data.count() for host in hosts]),
            len(host["sources"]),
        )

    def test_manual_merge_cross_org(self):
        host1 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            technology_ids=["defender_atp"],
        )
        host2 = MergedHostFactory.create(
            metadata__organization_id=self.child_organization.id,
            technology_ids=["sentinel_one"],
        )
        self.assert_manual_merge([str(host1.id), str(host2.id)], 403)

    def test_manual_merge_cross_org_non_exist(self):
        self.assert_manual_merge([str(uuid4()), str(uuid4())], 404)

    def test_manual_merge_lock_error(self):
        lock_key = merge_service._asset_modify_lock_key(self.organization, "host")
        with advisory_lock(lock_key):
            hosts = MergedHostFactory.create_batch(
                metadata__organization_id=self.organization.id,
                size=3,
                technology_ids=["defender_atp", "sentinel_one", "crowdstrike_falcon"],
            )
            self.assert_manual_merge([str(host.id) for host in hosts], 423)

    def assert_manual_merge(self, host_ids, expected_status_code, review=False):
        path = "/api/v1/hosts/merge"
        if review:
            path += "/review"
        response = self.api_client.post(path, json={"ids": host_ids})
        self.assertEqual(expected_status_code, response.status_code)
        if expected_status_code == 200:
            host = response.json()
            self.assertIsNotNone(host)
            self.assertIsNotNone(host.get("id"))

            expected_persisted_count = len(host_ids) if review else 1
            hosts = MergedAsset.documents.search({})
            self.assertEqual(
                len(hosts),
                expected_persisted_count,
                f"Expected to find {expected_persisted_count} hosts persisted. Found {len(hosts)}",
            )

        return response.json()

    def test_manual_unmerge_review_success(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            metadata__manually_merged=True,
            technology_ids=["defender_atp", "sentinel_one", "crowdstrike_falcon"],
        )
        self.assert_manual_unmerge(host.id, 200, expected_count=3, review=True)

    def test_manual_unmerge_review_fail(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            metadata__manually_merged=False,
            technology_ids=["defender_atp", "sentinel_one", "crowdstrike_falcon"],
        )
        self.assert_manual_unmerge(host.id, 403, expected_count=3, review=True)

    def test_manual_unmerge_success(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            metadata__manually_merged=True,
            technology_ids=["defender_atp", "sentinel_one", "crowdstrike_falcon"],
        )
        self.assert_manual_unmerge(host.id, 200, expected_count=3)

    def test_manual_unmerge_fail(self):
        host = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            metadata__manually_merged=False,
            technology_ids=["defender_atp", "sentinel_one", "crowdstrike_falcon"],
        )
        self.assert_manual_unmerge(host.id, 403, expected_count=3)

    def test_manual_unmerge_lock_error(self):
        lock_key = merge_service._asset_modify_lock_key(self.organization, "host")
        with advisory_lock(lock_key):
            host = MergedHostFactory.create(
                metadata__organization_id=self.organization.id,
                metadata__manually_merged=True,
                technology_ids=["defender_atp", "sentinel_one", "crowdstrike_falcon"],
            )
            self.assert_manual_unmerge(host.id, 423)

    def assert_manual_unmerge(
        self,
        host_id,
        expected_status_code,
        expected_count=3,
        review=False,
    ):
        path = f"/api/v1/hosts/{host_id}/unmerge"
        if review:
            path += "/review"
        response = self.api_client.post(path, json={})
        self.assertEqual(expected_status_code, response.status_code)
        if expected_status_code == 200:
            items = response.json()["items"]
            self.assertEqual(
                len(items),
                expected_count,
                f"Expected to find {expected_count} items in the response. Found {len(items)}",
            )
            expected_persisted_count = 1 if review else expected_count
            hosts = MergedAsset.documents.search({})
            self.assertEqual(
                len(hosts),
                expected_persisted_count,
                f"Expected to find {expected_persisted_count} hosts persisted. Found {len(hosts)}",
            )
            return items
