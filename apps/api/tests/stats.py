import uuid
from datetime import timedelta

import factory
from django.utils import dateparse

from apps.assets.models import AssetCriticality, CoverageCategory, MergedAsset, OsFamily
from apps.assets.tests.es_base import ESCaseMixin
from apps.integrations.api.data_connectors import CoverageMode
from apps.integrations.models.integration import Integration
from apps.stats.models.daily_coverage_gaps import (
    DailyCoverageGapsCriteria,
    DailyCoverageGapsData,
    DailyCoverageGapsMetadata,
    PersistentDailyCoverageGaps,
)
from apps.stats.models.persistent_stats import PersistentStat, StatType
from apps.tests.base import ApiDatabaseTestCase
from factories.merged_host import MergedHostFactory, MergedSourceHostFactory


def get_endpoint_gap_stat_data(n):
    data = {
        "total": n * 10,
        "windows": {"no_coverage": n * 4},
        "linux": {"no_coverage": n * 3},
        "mac": {"no_coverage": n * 2},
        "unknown": {"no_coverage": n},
    }

    # Test missing key for old data
    if n == 3:
        del data["mac"]

    return data


def get_stat_day(n):
    start = dateparse.parse_datetime("2023-08-01T00:00:00Z")
    stat_day = start - timedelta(days=30 - n - 1)
    return stat_day


class EndpointGapStatFactory(factory.Factory):
    class Meta:
        model = PersistentStat

    organization_id = uuid.uuid4()
    type = StatType.ENDPOINT_GAPS
    data = factory.Sequence(get_endpoint_gap_stat_data)
    created = factory.Sequence(get_stat_day)


class DailyCoverageGapsDataFactory(factory.Factory):
    class Meta:
        model = DailyCoverageGapsData

    with_coverage = factory.Sequence(lambda n: n * 6)
    no_coverage = factory.Sequence(lambda n: n * 4)

    @factory.lazy_attribute
    def total(self):
        return self.with_coverage + self.no_coverage


class DailyCoverageGapsCriteriaFactory(factory.Factory):
    class Meta:
        model = DailyCoverageGapsCriteria

    last_seen = factory.Faker("date_time_between")
    os_family = factory.Faker("random_element", elements=list(OsFamily))
    technology_ids = factory.Faker(
        "random_elements",
        elements=["crowdstrike_falcon", "sentinel_one", "carbon_black"],
        length=2,
    )
    criticality = factory.Faker("random_element", elements=list(AssetCriticality))


class DailyCoverageGapsMetadataFactory(factory.Factory):
    class Meta:
        model = DailyCoverageGapsMetadata

    organization_id = uuid.uuid4()
    coverage_category = CoverageCategory.ENDPOINT_SECURITY
    created = factory.Sequence(get_stat_day)


class PersistentDailyCoverageGapsFactory(factory.Factory):
    class Meta:
        model = PersistentDailyCoverageGaps

    data = factory.SubFactory(DailyCoverageGapsDataFactory)
    criteria = factory.SubFactory(DailyCoverageGapsCriteriaFactory)
    metadata = factory.SubFactory(DailyCoverageGapsMetadataFactory)


class BaseStatsTestCase(ESCaseMixin, ApiDatabaseTestCase):
    es_fixtures = ["merged_assets.json"]
    category = None

    def total_by_key(self, totals, key, field="id"):
        if field == "id":
            lookup = key
        else:
            lookup = ".".join(sorted(key))

        def normalize(total):
            value = total[field]
            if field == "id":
                return value
            return ".".join(sorted(total[field]))

        return next(iter(t["value"] for t in totals if normalize(t) == lookup), None)

    def build_hosts(self, count, **kwargs):
        organization_id = kwargs.pop("organization_id", self.organization.id)

        return MergedHostFactory.build_batch(
            count,
            metadata__organization_id=organization_id,
            **kwargs,
        )


# Intentionally leave one day missing
MISSING_INDEX = 15


class BaseInventoryStatsTests(BaseStatsTestCase):
    def setup_endpoint_gaps(self, organization_id=None, fake_now="2023-08-01"):
        fake_now = dateparse.parse_datetime(fake_now)
        self.patch("apps.stats.inventory.endpoint_gaps.now", return_value=fake_now)

        organization_id = organization_id or self.organization.id
        EndpointGapStatFactory.reset_sequence()
        endpoint_gaps = EndpointGapStatFactory.create_batch(
            organization_id=organization_id,
            size=30,
        )
        for i, stat in enumerate(endpoint_gaps):
            if i != MISSING_INDEX:
                PersistentStat.documents.create(stat)

    def get(self, types, available=False, status_code=200, params=None):
        path = "/api/v1/stats/inventory"
        if available:
            path += "/available"

        params = params or {}
        params["types"] = types

        response = self.api_client.get(path, params=params)
        self.assertEqual(status_code, response.status_code, response.json())
        return response

    def setup_daily_coverage_gaps(
        self,
        organization_id=None,
        fake_now="2023-08-02",
        coverage_category=CoverageCategory.ENDPOINT_SECURITY,
    ):
        fake_now = dateparse.parse_datetime(fake_now)

        to_patch_lookup = {
            CoverageCategory.ENDPOINT_SECURITY: "apps.stats.inventory.endpoint_daily_coverage_gaps_by_criticality.now",
            CoverageCategory.VULNERABILITY_MANAGEMENT: "apps.stats.inventory.vulnerability_daily_coverage_gaps_by_criticality.now",
        }

        if to_patch := to_patch_lookup.get(coverage_category):
            self.patch(to_patch, return_value=fake_now)
        else:  # pragma: no cover
            raise ValueError("Invalid coverage category")

        organization_id = organization_id or self.organization.id
        coverage_gaps = [
            PersistentDailyCoverageGapsFactory.build(
                metadata__organization_id=organization_id,
                metadata__coverage_category=coverage_category,
                metadata__created="2023-08-01",
                criteria__last_seen=dateparse.parse_datetime("2020-10-01"),
                criteria__os_family="windows",
                criteria__technology_ids=["sentinel_one"],
                criteria__criticality="tier2",
                data__with_coverage=7,
                data__no_coverage=3,
            ),
            PersistentDailyCoverageGapsFactory.build(
                metadata__organization_id=organization_id,
                metadata__coverage_category=coverage_category,
                metadata__created="2023-08-01",
                criteria__last_seen=dateparse.parse_datetime("2020-10-02"),
                criteria__os_family="mac",
                criteria__technology_ids=["defender_atp"],
                criteria__criticality="tier3",
                data__with_coverage=5,
                data__no_coverage=5,
            ),
        ]

        PersistentDailyCoverageGaps.documents.upsert(coverage_gaps)

        merged_hosts = MergedHostFactory.build_batch(
            10,
            metadata__organization_id=organization_id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="sentinel_one",
                    metadata__integration__vulnerability_coverage_mode=CoverageMode.ENABLED,
                    metadata__integration__endpoint_coverage_mode=CoverageMode.ENABLED,
                    metadata__asset__last_seen=dateparse.parse_datetime("2020-10-01"),
                    attributes__os__family="windows",
                    attributes__criticality="tier2",
                )
            ],
        ) + MergedHostFactory.build_batch(
            10,
            metadata__organization_id=organization_id,
            source_assets=[
                MergedSourceHostFactory(
                    metadata__integration__technology_id="defender_atp",
                    metadata__integration__vulnerability_coverage_mode=CoverageMode.ENABLED,
                    metadata__integration__endpoint_coverage_mode=CoverageMode.ENABLED,
                    metadata__asset__last_seen=dateparse.parse_datetime("2020-10-02"),
                    attributes__os__family="mac",
                    attributes__criticality="tier3",
                )
            ],
        )

        MergedAsset.documents.create_bulk(merged_hosts)


class InventoryStatsFixtureTests(BaseInventoryStatsTests):
    def test_available(self):
        response = self.get("inventory", available=True)
        available = response.json()
        stat_ids = [a["id"] for a in available]
        self.assertIn("totals", stat_ids)

    def test_no_args(self):
        self.get(types=None, status_code=422)

    def test_invalid_type(self):
        self.get(types=["invalid"], status_code=422)

    def test_endpoint_coverage(self):
        response = self.get(types="endpoint_coverage")
        stats = response.json()["endpoint_coverage"]["totals"]
        self.assertEqual(1, stats[0]["value"], 1)

    def test_endpoint_coverage_multi(self):
        hosts = []
        defender_atp = MergedSourceHostFactory(
            metadata__integration__technology_id="defender_atp",
            metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.IGNORE,
            metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        carbon_black = MergedSourceHostFactory(
            metadata__integration__technology_id="carbon_black",
            metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.IGNORE,
            metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        hosts.extend(self.build_hosts(10, source_assets=[defender_atp, carbon_black]))
        hosts.extend(self.build_hosts(5, source_assets=[defender_atp]))
        hosts.extend(self.build_hosts(2, source_assets=[carbon_black]))
        MergedAsset.documents.create_bulk(hosts)
        response = self.get(types="endpoint_coverage")
        stats = response.json()["endpoint_coverage"]["totals"]

        both = self.total_by_key(stats, ["carbon_black", "defender_atp"], field="ids")
        self.assertEqual(10, both)

        defender_atp = self.total_by_key(stats, ["defender_atp"], field="ids")
        self.assertEqual(5, defender_atp)

        carbon_black = self.total_by_key(stats, ["carbon_black"], field="ids")
        self.assertEqual(2, carbon_black)

    def test_endpoint_coverage_excluded(self):
        hosts = []
        defender_atp = MergedSourceHostFactory(
            metadata__integration__technology_id="defender_atp",
            metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.IGNORE,
            metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
        )
        tenable_io = MergedSourceHostFactory(
            metadata__integration__technology_id="tenable_io",
            metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
            metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.IGNORE,
        )
        qualys_vmpc = MergedSourceHostFactory(
            metadata__integration__technology_id="qualys_vmpc",
            metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
            metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.IGNORE,
        )
        hosts.extend(self.build_hosts(10, source_assets=[defender_atp, tenable_io]))
        hosts.extend(
            self.build_hosts(
                5,
                source_assets=[qualys_vmpc],
                overrides={"endpoint_security_excluded": True},
            )
        )
        hosts.extend(
            self.build_hosts(
                2,
                source_assets=[tenable_io],
                overrides={"endpoint_security_excluded": True},
            )
        )
        MergedAsset.documents.create_bulk(hosts)
        response = self.get(types="endpoint_coverage")
        stats = response.json()["endpoint_coverage"]["totals"]

        both = self.total_by_key(stats, ["defender_atp"], field="ids")
        self.assertEqual(10, both)

        excluded = self.total_by_key(stats, ["excluded"], field="ids")
        self.assertEqual(7, excluded)

    def test_sources(self):
        response = self.get(types=["sources"])
        stats = response.json()["sources"]["totals"]
        self.assertEqual(1, stats[0]["value"])

    def test_totals(self):
        response = self.get(types=["totals"])
        stats = response.json()["totals"]
        self.assertEqual(3, stats["total_seen"])
        self.assertEqual(3, stats["total_unique"])

    def test_endpoint_gaps(self):
        self.setup_endpoint_gaps()
        response = self.get(types=["endpoint_gaps"])
        stats = response.json()["endpoint_gaps"]
        self.assertEqual(31, len(stats["labels"]))
        self.assertEqual(31, len(stats["data"][0]))
        self.assertEqual(5, len(stats["series"]))

        self.assertEqual("07.02", stats["labels"][0])
        self.assertEqual("08.01", stats["labels"][-1])
        self.assertEqual("Windows", stats["series"][1])

        self.assertEqual(130, stats["data"][0][14])
        self.assertEqual(52, stats["data"][1][14])
        self.assertEqual(39, stats["data"][2][14])
        self.assertEqual(26, stats["data"][3][14])
        self.assertEqual(13, stats["data"][4][14])

        # First day has no data
        for i in range(5):
            self.assertEqual(0, stats["data"][i][0])

        # Missing day has no data
        for i in range(5):
            self.assertEqual(0, stats["data"][i][MISSING_INDEX + 1])

    def test_endpoint_gaps_parent_org(self):
        self.setup_endpoint_gaps()
        self.setup_endpoint_gaps(organization_id=self.child_organization.id)
        self.setup_endpoint_gaps(organization_id="different")
        response = self.get(types=["endpoint_gaps"])
        stats = response.json()["endpoint_gaps"]
        self.assertEqual(31, len(stats["labels"]))
        self.assertEqual(31, len(stats["data"][0]))

        self.assertEqual(260, stats["data"][0][14])
        self.assertEqual(104, stats["data"][1][14])
        self.assertEqual(78, stats["data"][2][14])
        self.assertEqual(52, stats["data"][3][14])
        self.assertEqual(26, stats["data"][4][14])

    def test_endpoint_gaps_child_org(self):
        self.auth_as_child_org()
        self.setup_endpoint_gaps()
        self.setup_endpoint_gaps(organization_id=self.child_organization.id)
        response = self.get(types=["endpoint_gaps"])
        stats = response.json()["endpoint_gaps"]
        self.assertEqual(31, len(stats["labels"]))
        self.assertEqual(31, len(stats["data"][0]))

        self.assertEqual(130, stats["data"][0][14])
        self.assertEqual(52, stats["data"][1][14])
        self.assertEqual(39, stats["data"][2][14])
        self.assertEqual(26, stats["data"][3][14])
        self.assertEqual(13, stats["data"][4][14])

    def test_endpoint_gaps_missing_data(self):
        self.setup_endpoint_gaps(fake_now="2023-09-01")
        response = self.get(types=["endpoint_gaps"])
        stats = response.json()["endpoint_gaps"]
        self.assertEqual(31, len(stats["labels"]))
        self.assertEqual(31, len(stats["data"][0]))

        self.assertEqual(5, len(stats["data"]))
        self.assertEqual(0, stats["data"][0][0])
        self.assertEqual(0, stats["data"][1][0])
        self.assertEqual(0, stats["data"][2][0])
        self.assertEqual(0, stats["data"][3][0])
        self.assertEqual(0, stats["data"][4][0])

    def test_endpoint_gaps_current_day(self):
        self.setup_endpoint_gaps()
        response = self.get(types=["endpoint_gaps"])
        stats = response.json()["endpoint_gaps"]

        self.assertEqual(31, len(stats["labels"]))
        self.assertEqual("08.01", stats["labels"][-1])
        self.assertEqual(3, stats["data"][0][-1])
        self.assertEqual(0, stats["data"][1][-1])
        self.assertEqual(0, stats["data"][2][-1])
        self.assertEqual(0, stats["data"][3][-1])
        self.assertEqual(1, stats["data"][4][-1])
        tenable_host = MergedAsset.documents.search({})[2]
        # Ensure we have the host with an endpoint gap
        self.assertTrue(tenable_host.merged_data.hostname, "host3")
        self.assertSetEqual(
            set(tenable_host.source_data.technology_ids()), {"tenable_io"}
        )
        self.assertFalse(tenable_host.metadata.asset.technologies.endpoint_security)

        # Exclude it from coverage gap detection
        tenable_host.overrides.endpoint_security_excluded = True
        MergedAsset.documents.update(tenable_host)

        response = self.get(types=["endpoint_gaps"])
        stats = response.json()["endpoint_gaps"]

        self.assertEqual(31, len(stats["labels"]))
        self.assertEqual("08.01", stats["labels"][-1])
        self.assertEqual(3, stats["data"][0][-1])
        self.assertEqual(0, stats["data"][1][-1])
        self.assertEqual(0, stats["data"][2][-1])
        self.assertEqual(0, stats["data"][3][-1])
        self.assertEqual(0, stats["data"][4][-1])

    def test_endpoint_coverage_filter_criticality(self):
        response = self.get(
            types=["endpoint_coverage"], params={"criticality": ["tier0", "tier2"]}
        )

        stats = {
            item["id"]: item["value"]
            for item in response.json()["endpoint_coverage"]["totals"]
        }

        expected = {"sentinel_one": 1}
        self.assertEqual(stats, expected)

    def test_sources_filter_technology(self):
        response = self.get(
            types=["sources"], params={"technology": "crowdstrike_falcon"}
        )
        stats = {
            item["id"]: item["value"] for item in response.json()["sources"]["totals"]
        }

        expected = {"crowdstrike_falcon": 1}
        self.assertEqual(stats, expected)

    def test_totals_filter_os_family(self):
        response = self.get(types=["totals"], params={"os_family": "linux"})
        stats = response.json()["totals"]

        expected = {"total_seen": 1, "total_unique": 1}
        self.assertEqual(stats, expected)


class InventoryEmptyStatsTests(BaseInventoryStatsTests):
    # Ensure no data is loaded into ES
    es_fixtures = []

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.create_index_patchers(empty=True)

    def test_endpoint_coverage(self):
        response = self.get(types="endpoint_coverage")
        stats = response.json()["endpoint_coverage"]["totals"]
        self.assertEqual(0, len(stats))

    def test_sources(self):
        response = self.get(types=["sources"])
        stats = response.json()["sources"]["totals"]
        self.assertEqual(0, len(stats))

    def test_totals(self):
        response = self.get(types=["totals"])
        stats = response.json()["totals"]
        self.assertEqual(0, stats["total_seen"])

    def test_endpoint_gaps(self):
        response = self.get(types=["endpoint_gaps"])
        stats = response.json()["endpoint_gaps"]
        self.assertEqual(0, len(stats["labels"]))

    def test_all_available_stats(self):
        response = self.get("inventory", available=True)
        available = response.json()
        stat_ids = [a["id"] for a in available]
        for typ in stat_ids:
            self.assert_by_type(typ)

    def assert_by_type(self, typ):
        response = self.get(types=[typ])
        stats = response.json()
        self.assertIn(typ, stats, f"{typ} not in response")

    def test_insights(self):
        response = self.get(types="insights")
        stats = response.json()["insights"]
        self.assertEqual(0, stats["critical_assets_missing_endpoint"])
        self.assertEqual(0, stats["critical_assets_missing_vulnerability"])
        self.assertEqual(0, stats["new_assets"])
        self.assertEqual(0, stats["critical_assets"])


class BaseHostStatsTests(BaseStatsTestCase):
    def setUp(self):
        super().setUp()
        self.patch("apps.api.entitlements.check_entitlement")

    def get(self, types, available=False, status_code=200, params=None):
        params = params or {}
        path = "/api/v1/stats/hosts"
        if available:
            path += "/available"

        if types:
            params["types"] = types

        response = self.api_client.get(path, params=params)
        self.assertEqual(status_code, response.status_code, response.json())
        return response


class HostStatsTests(BaseHostStatsTests):
    def test_available(self):
        response = self.get("host", available=True)
        available = response.json()
        stat_ids = [a["id"] for a in available]
        self.assertIn("total", stat_ids)

    def test_no_args(self):
        self.get(types=None, status_code=422)

    def test_invalid_type(self):
        self.get(types=["invalid"], status_code=422)

    def test_total(self):
        response = self.get(types=["total"])
        stats = response.json()["total"]
        self.assertEqual(3, stats["total"])

    def test_criticality(self):
        response = self.get(types=["criticality"])
        totals = response.json()["criticality"]["totals"]
        tier2 = self.total_by_key(totals, "tier2")
        tier3 = self.total_by_key(totals, "tier3")
        unknown = self.total_by_key(totals, "unknown")
        self.assertEqual(1, tier2)
        self.assertEqual(1, tier3)
        self.assertEqual(1, unknown)

    def test_criticality_filter(self):
        response = self.get(
            types=["criticality"], params={"criticality": ["tier0", "tier3"]}
        )
        totals = response.json()["criticality"]["totals"]
        tier0 = self.total_by_key(totals, "tier0")
        tier2 = self.total_by_key(totals, "tier2")
        tier3 = self.total_by_key(totals, "tier3")
        unknown = self.total_by_key(totals, "unknown")
        self.assertEqual(0, tier0)
        self.assertIsNone(tier2)
        self.assertEqual(1, tier3)
        self.assertIsNone(unknown)

    def test_sources(self):
        response = self.get(types=["sources"])
        totals = response.json()["sources"]["totals"]
        sentinel_one = self.total_by_key(totals, "sentinel_one")
        crowdstrike = self.total_by_key(totals, "crowdstrike_falcon")
        self.assertEqual(1, sentinel_one)
        self.assertEqual(1, crowdstrike)

    def test_os_family(self):
        response = self.get(types=["os_family"])
        totals = response.json()["os_family"]["totals"]
        windows = self.total_by_key(totals, "windows")
        linux = self.total_by_key(totals, "linux")
        self.assertEqual(1, windows)
        self.assertEqual(1, linux)

    def test_os_family_filter(self):
        response = self.get(types=["os_family"], params={"os_family": "windows"})
        totals = response.json()["os_family"]["totals"]
        windows = self.total_by_key(totals, "windows")
        linux = self.total_by_key(totals, "linux")
        self.assertEqual(1, windows)
        self.assertIsNone(linux)


class HostEmptyStatsTests(BaseHostStatsTests):
    # Ensure no data is loaded into ES
    es_fixtures = []

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.create_index_patchers(empty=True)

    def test_criticality(self):
        response = self.get(types=["criticality"])
        totals = response.json()["criticality"]["totals"]
        self.assertEqual(6, len(totals))

    def test_sources(self):
        response = self.get(types=["sources"])
        totals = response.json()["sources"]["totals"]
        self.assertEqual(0, len(totals))

    def test_os_family(self):
        response = self.get(types=["os_family"])
        totals = response.json()["os_family"]["totals"]
        self.assertEqual(4, len(totals))

    def test_all_available_stats(self):
        response = self.get("host", available=True)
        available = response.json()
        stat_ids = [a["id"] for a in available]
        for typ in stat_ids:
            self.assert_by_type(typ)

    def assert_by_type(self, typ):
        response = self.get(types=[typ])
        stats = response.json()
        self.assertIn(typ, stats, f"{typ} not in response")


class InventoryCriticalityTest(BaseInventoryStatsTests):
    def test_criticality(self):
        response = self.get(types=["criticality"])
        totals = response.json()["criticality"]["totals"]
        tier2 = self.total_by_key(totals, "tier2")
        tier3 = self.total_by_key(totals, "tier3")
        unknown = self.total_by_key(totals, "unknown")
        self.assertEqual(1, tier2)
        self.assertEqual(1, tier3)
        self.assertEqual(1, unknown)

    def test_criticality_filter(self):
        response = self.get(
            types=["criticality"], params={"criticality": ["tier0", "tier3"]}
        )
        totals = response.json()["criticality"]["totals"]
        tier0 = self.total_by_key(totals, "tier0")
        tier2 = self.total_by_key(totals, "tier2")
        tier3 = self.total_by_key(totals, "tier3")
        unknown = self.total_by_key(totals, "unknown")
        self.assertEqual(0, tier0)
        self.assertIsNone(tier2)
        self.assertEqual(1, tier3)
        self.assertIsNone(unknown)


class EmptyInventoryCriticalityTest(BaseInventoryStatsTests):
    es_fixtures = []

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.create_index_patchers(empty=True)

    def test_criticality(self):
        response = self.get(types=["criticality"])
        totals = response.json()["criticality"]["totals"]
        self.assertEqual(6, len(totals))

    def test_criticality_filter(self):
        response = self.get(
            types=["criticality"], params={"criticality": ["tier0", "tier3"]}
        )
        totals = response.json()["criticality"]["totals"]
        tier0 = self.total_by_key(totals, "tier0")
        tier2 = self.total_by_key(totals, "tier2")
        tier3 = self.total_by_key(totals, "tier3")
        unknown = self.total_by_key(totals, "unknown")
        self.assertEqual(0, tier0)
        self.assertIsNone(tier2)
        self.assertEqual(0, tier3)
        self.assertIsNone(unknown)


class InventoryStatsTests(BaseInventoryStatsTests):
    es_fixtures = []

    def test_endpoint_coverage_excluded_meta_true_override_none(self):
        tenable_io = MergedSourceHostFactory(
            metadata__integration__technology_id="tenable_io",
            metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
            metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.IGNORE,
        )
        hosts = self.build_hosts(
            2,
            source_assetd=[tenable_io],
        )

        # We don't have a way to set "metadata.asset.technologies.endpoint_security_excluded"
        # from source data at this time, so we need to set it directly.
        for host in hosts:
            host.metadata.asset.technologies.endpoint_security_excluded = True

        MergedAsset.documents.create_bulk(hosts)

        response = self.get(types="endpoint_coverage")
        stats = response.json()["endpoint_coverage"]["totals"]

        excluded = self.total_by_key(stats, ["excluded"], field="ids")
        self.assertEqual(2, excluded)

    def test_endpoint_coverage_excluded_meta_true_override_false(self):
        hosts = self.build_hosts(
            2,
            technology_ids=["tenable_io"],
            overrides={"endpoint_security_excluded": False},
        )

        # We don't have a way to set "metadata.asset.technologies.endpoint_security_excluded"
        # from source data at this time, so we need to set it directly.
        for host in hosts:
            host.metadata.asset.technologies.endpoint_security_excluded = True

        MergedAsset.documents.create_bulk(hosts)

        response = self.get(types="endpoint_coverage")
        stats = response.json()["endpoint_coverage"]["totals"]

        excluded = self.total_by_key(stats, ["excluded"], field="ids")
        # None is equivalent to 0 because the key is not present
        self.assertIsNone(excluded)

    def test_endpoint_coverage_excluded_meta_false_override_false(self):
        hosts = self.build_hosts(
            2,
            technology_ids=["tenable_io"],
            overrides={"endpoint_security_excluded": False},
        )

        MergedAsset.documents.create_bulk(hosts)

        response = self.get(types="endpoint_coverage")
        stats = response.json()["endpoint_coverage"]["totals"]

        excluded = self.total_by_key(stats, ["excluded"], field="ids")
        # None is equivalent to 0 because the key is not present
        self.assertIsNone(excluded)

    def test_endpoint_coverage_excluded_meta_false_override_true(self):
        hosts = self.build_hosts(
            2,
            technology_ids=["tenable_io"],
            overrides={"endpoint_security_excluded": True},
        )

        MergedAsset.documents.create_bulk(hosts)

        response = self.get(types="endpoint_coverage")
        stats = response.json()["endpoint_coverage"]["totals"]

        excluded = self.total_by_key(stats, ["excluded"], field="ids")
        self.assertEqual(2, excluded)

    def test_endpoint_daily_coverage_gaps_by_criticality(self):
        self.assert_coverage_gaps_by_criticality(
            CoverageCategory.ENDPOINT_SECURITY,
            "endpoint_daily_coverage_gaps_by_criticality",
        )

    def test_vulnerability_daily_coverage_gaps_by_criticality(self):
        self.assert_coverage_gaps_by_criticality(
            CoverageCategory.VULNERABILITY_MANAGEMENT,
            "vulnerability_daily_coverage_gaps_by_criticality",
        )

    def assert_coverage_gaps_by_criticality(self, coverage_category, stats_key):
        self.setup_daily_coverage_gaps(coverage_category=coverage_category)
        response = self.get(types=stats_key)
        stats = response.json()[stats_key]

        total_ix = 0
        tier2_ix = 3
        tier3_ix = 4

        ix = stats["labels"].index("08.01")
        self.assertEqual(20, stats["data"][total_ix][ix])
        self.assertEqual(3, stats["data"][tier2_ix][ix])
        self.assertEqual(5, stats["data"][tier3_ix][ix])

        ix = stats["labels"].index("08.02")
        self.assertEqual(20, stats["data"][total_ix][ix])
        self.assertEqual(0, stats["data"][tier2_ix][ix])
        self.assertEqual(0, stats["data"][tier3_ix][ix])

        #######################################################################
        # try all the ways to filter out defender_atp data
        def assert_defender_atp_data_removed():
            ix = stats["labels"].index("08.01")
            self.assertEqual(10, stats["data"][total_ix][ix], filt)
            self.assertEqual(3, stats["data"][tier2_ix][ix], filt)
            self.assertEqual(0, stats["data"][tier3_ix][ix], filt)

            ix = stats["labels"].index("08.02")
            self.assertEqual(10, stats["data"][total_ix][ix], filt)
            self.assertEqual(0, stats["data"][tier2_ix][ix], filt)
            self.assertEqual(0, stats["data"][tier3_ix][ix], filt)

        filters = [
            {"criticality": "tier2"},
            {"technology": "sentinel_one"},
            {"os_family": "windows"},
            {"last_seen": "2020-09-20T00:00:00Z&2020-10-01T00:00:00Z"},
        ]
        for filt in filters:
            response = self.get(types=stats_key, params=filt)
            stats = response.json()[stats_key]
            assert_defender_atp_data_removed()


class InventoryInsightsTest(BaseInventoryStatsTests):
    es_fixtures = []

    def test_insights(self):
        for coverage in [
            CoverageCategory.ENDPOINT_SECURITY,
            CoverageCategory.VULNERABILITY_MANAGEMENT,
        ]:
            self.setup_daily_coverage_gaps(coverage_category=coverage)

        response = self.get(types="insights")

        stats = response.json()["insights"]
        self.assertEqual(0, stats["critical_assets_missing_endpoint"])
        self.assertEqual(0, stats["critical_assets_missing_vulnerability"])
        self.assertEqual(20, stats["critical_assets"])

        self.assertEqual(40, stats["new_assets"])

    def test_insights_filter(self):
        merged_hosts = (
            MergedHostFactory.build_batch(
                10,
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        metadata__integration__technology_id="sentinel_one",
                        metadata__integration__vulnerability_coverage_mode=CoverageMode.NOT_APPLICABLE,
                        metadata__integration__endpoint_coverage_mode=CoverageMode.NOT_APPLICABLE,
                        metadata__asset__last_seen=dateparse.parse_datetime(
                            "2020-10-01"
                        ),
                        attributes__os__family="windows",
                        attributes__criticality="tier2",
                    )
                ],
            )
            + MergedHostFactory.build_batch(
                10,
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        metadata__integration__technology_id="defender_atp",
                        metadata__integration__vulnerability_coverage_mode=CoverageMode.NOT_APPLICABLE,
                        metadata__integration__endpoint_coverage_mode=CoverageMode.ENABLED,
                        metadata__asset__last_seen=dateparse.parse_datetime(
                            "2020-10-02"
                        ),
                        attributes__os__family="mac",
                        attributes__criticality="tier1",
                    )
                ],
            )
            + MergedHostFactory.build_batch(
                10,
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        metadata__integration__technology_id="tenable_io",
                        metadata__integration__vulnerability_coverage_mode=CoverageMode.ENABLED,
                        metadata__integration__endpoint_coverage_mode=CoverageMode.NOT_APPLICABLE,
                        metadata__asset__last_seen=dateparse.parse_datetime(
                            "2020-10-01"
                        ),
                        attributes__os__family="windows",
                        attributes__criticality="tier2",
                    )
                ],
            )
        )

        MergedAsset.documents.create_bulk(merged_hosts)

        response = self.get(types="insights", params={"os_family": "windows"})

        stats = response.json()["insights"]
        self.assertEqual(20, stats["critical_assets_missing_endpoint"])
        self.assertEqual(10, stats["critical_assets_missing_vulnerability"])
        self.assertEqual(20, stats["critical_assets"])
        self.assertEqual(10, stats["missing_technical_security_control"])

        self.assertEqual(20, stats["new_assets"])
        self.assertEqual(20, stats["total_assets"])


class IntegrationStatsTest(BaseHostStatsTests):
    def test_integration(self):
        response = self.get(types="integration")
        stats = response.json()["integration"]
        self.assertEqual(0, stats["bc47f69a-c1d3-4031-a781-45ea5b431fb8"])
        self.assertEqual(0, stats["bc47f69a-c1d3-4031-a781-45ea5b431fc0"])
        self.assertEqual(1, stats["54f566f2-5fae-4a25-b6b7-6a9d0b3ed526"])
