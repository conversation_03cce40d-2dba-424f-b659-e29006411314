from unittest.mock import Mock

from criticalstart.auth.v2.test import factory
from fastapi.exceptions import HTTPException
from requests import HTTPError

from apps.api.entitlements import (
    AccountsApi,
    EntitlementModule,
    check_configuration_entitlements,
    check_entitlement,
    check_filter_entitlements,
    check_host_entitlements,
)
from apps.api.v1.schemas.filter import CoverageGap, QueryInventoryFilter
from apps.tests.base import BaseTestCase


class EntitlementsTests(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.m_get_entitlement = self.patch_object(AccountsApi, "get_entitlement")
        self.patch_get_entitlement([])
        self.session = factory.UserSession(
            organization=factory.Organization(
                id=self.organization.id,
                alias=self.organization.alias,
                name="Testing Org 1",
                account_id=self.organization.account_id,
                account_alias=self.organization.account.alias,
            ),
            token="auth",
        )

    def patch_get_entitlement(self, entitlement_modules, error_code=404):
        def get_entitlement(account_id, module):
            if module in entitlement_modules:
                return {
                    "id": "b9ff49fa-c917-4f8e-8545-16bed568f51e",
                    "account_id": account_id,
                    "active": True,
                    "requestable": False,
                    "requested": False,
                    "tags": [],
                    "module": module,
                }
            else:
                raise HTTPError(response=Mock(status_code=error_code))

        self.m_get_entitlement.side_effect = get_entitlement

    def check_error_raised(self, func, *args, error_code=403, error_type=None):
        with self.assertRaises(error_type or HTTPException) as e:
            func(*args)

        if error_type is HTTPError:
            self.assertEqual(e.exception.response.status_code, error_code)
        else:
            self.assertEqual(e.exception.status_code, error_code)

    def test_reraise_http_error(self):
        self.patch_get_entitlement([], error_code=500)
        self.check_error_raised(
            check_entitlement,
            self.session,
            EntitlementModule.FULL_VISIBILITY,
            error_code=500,
            error_type=HTTPError,
        )

    def test_check_empty_account(self):
        self.patch_get_entitlement([EntitlementModule.FULL_VISIBILITY])
        self.session.organization.account_id = None
        self.check_error_raised(
            check_entitlement, self.session, EntitlementModule.FULL_VISIBILITY
        )

    def test_check_no_entitlement(self):
        self.check_error_raised(
            check_entitlement, self.session, EntitlementModule.FULL_VISIBILITY
        )
        self.check_error_raised(
            check_entitlement, self.session, EntitlementModule.COVERAGE_GAPS
        )

    def test_check_entitlement(self):
        self.patch_get_entitlement([EntitlementModule.FULL_VISIBILITY])
        check_entitlement(self.session, EntitlementModule.FULL_VISIBILITY)
        self.check_error_raised(
            check_entitlement, self.session, EntitlementModule.COVERAGE_GAPS
        )

    def test_check_host_entitlements(self):
        self.patch_get_entitlement([EntitlementModule.FULL_VISIBILITY])
        check_host_entitlements(self.session)

        self.patch_get_entitlement([EntitlementModule.COVERAGE_GAPS])
        self.check_error_raised(check_host_entitlements, self.session)

    def test_check_configuration_entitlements(self):
        self.patch_get_entitlement([EntitlementModule.FULL_VISIBILITY])
        check_configuration_entitlements(self.session)

        self.patch_get_entitlement([EntitlementModule.COVERAGE_GAPS])
        self.check_error_raised(check_configuration_entitlements, self.session)

    def test_check_entitlements_no_filters(self):
        filters = QueryInventoryFilter()(
            coverage_gap=[],
            last_seen=None,
            last_seen_endpoint_security=None,
            last_seen_vulnerability_management=None,
            os_family=[],
            host_type=[],
            criticality=None,
            technology=[],
            integration=[],
        )
        self.patch_get_entitlement([EntitlementModule.COVERAGE_GAPS])
        self.check_error_raised(check_filter_entitlements, self.session, filters)
        self.patch_get_entitlement([EntitlementModule.FULL_VISIBILITY])
        check_filter_entitlements(self.session, filters)

    def test_check_entitlements_full_visibility_filters_1(self):
        filters = QueryInventoryFilter()(
            coverage_gap=[],
            last_seen=None,
            last_seen_endpoint_security=None,
            last_seen_vulnerability_management=None,
            os_family=["linux"],
            host_type=[],
            criticality=None,
            technology=[],
            integration=[],
        )
        self.patch_get_entitlement([EntitlementModule.COVERAGE_GAPS])
        self.check_error_raised(check_filter_entitlements, self.session, filters)
        self.patch_get_entitlement([EntitlementModule.FULL_VISIBILITY])
        check_filter_entitlements(self.session, filters)

    def test_check_entitlements_full_visibility_filters_2(self):
        filters = QueryInventoryFilter()(
            coverage_gap=[
                CoverageGap.ENDPOINT_SECURITY,
                CoverageGap.NO_GAPS,
            ],
            last_seen=None,
            last_seen_endpoint_security=None,
            last_seen_vulnerability_management=None,
            os_family=[],
            host_type=[],
            criticality=None,
            technology=[],
            integration=[],
        )
        self.patch_get_entitlement([EntitlementModule.COVERAGE_GAPS])
        self.check_error_raised(check_filter_entitlements, self.session, filters)
        self.patch_get_entitlement([EntitlementModule.FULL_VISIBILITY])
        check_filter_entitlements(self.session, filters)

    def test_check_entitlements_coverage_gap_filters(self):
        filters = QueryInventoryFilter()(
            coverage_gap=[CoverageGap.ENDPOINT_SECURITY],
            last_seen=None,
            last_seen_endpoint_security=None,
            last_seen_vulnerability_management=None,
            os_family=[],
            host_type=[],
            criticality=None,
            technology=[],
            integration=[],
        )
        self.patch_get_entitlement([EntitlementModule.COVERAGE_GAPS])
        check_filter_entitlements(self.session, filters)
