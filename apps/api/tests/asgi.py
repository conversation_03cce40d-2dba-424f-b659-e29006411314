from unittest import TestCase
from unittest.mock import Mock

from core.asgi import generate_operation_id


class AsgiTest(TestCase):
    def test_generate_operation_id(self):
        route = Mock()

        # if no operation_id is set, use the route name
        route.operation_id = None
        route.path = "/api/v1/mock/"
        route.name = "get_mock"
        op_id = generate_operation_id(route)
        self.assertEqual(op_id, "get_mock")

        # if operation_id is set and the route is internal,
        # use the route name prefixed with 'internal_'
        route.operation_id = None
        route.path = "/internal/api/v1/mock/"
        route.name = "get_mock"
        op_id = generate_operation_id(route)
        self.assertEqual(op_id, "internal_get_mock")

        # if operation_id is set, use the operation_id
        route.operation_id = "different_op_id"
        route.path = "/api/v1/mock/"
        route.name = "get_mock"
        op_id = generate_operation_id(route)
        self.assertEqual(op_id, "different_op_id")
