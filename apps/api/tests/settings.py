from unittest.mock import patch
from uuid import UUID

from criticalstart.audit_logs import COMMAND_AUDIT_LOG_CREATE, Action, EntityTypeId
from fastapi import status

from apps.api.permissions import AssetInventoryPermission
from apps.assets.models import OrganizationSetting
from apps.tests.base import ApiDatabaseTestCase
from core.service_bus import service_bus
from factories.organization_setting import OrganizationSettingFactory
from factories.setting import SettingFactory


class SettingsTests(ApiDatabaseTestCase):
    def setUp(self):
        super().setUp()
        self.set_org_permissions(
            self.organization, {AssetInventoryPermission.EDIT_SETTINGS}
        )

    def test_create_setting(self):
        SettingFactory.create(
            category="display",
            key="filter_last_seen_gtr_x_days",
            default_value={"enabled": True, "days": 45},
        )
        value = {
            "overridden_value": {"enabled": True, "days": 55},
        }
        setting = self._put("/display/filter_last_seen_gtr_x_days", value)

        self.assertEqual(setting["key"], "filter_last_seen_gtr_x_days")
        self.assertEqual(setting["overridden_value"], {"enabled": True, "days": 55})
        self.assertEqual(setting["default_value"], {"enabled": True, "days": 45})
        self.assertFalse(setting["is_default"])
        self.assertEqual(setting["category"], "display")

    def test_create_setting_no_permission(self):
        self.set_org_permissions(self.organization, set())
        value = {
            "overridden_value": {"enabled": True, "days": 55},
        }
        self._put(
            "/display/filter_last_seen_gtr_x_days",
            value,
            status.HTTP_403_FORBIDDEN,
        )

    def test_create_setting_validation_failure(self):
        setting = {
            "overridden_value": {"enabled": True, "days": 55},
        }
        response = self._put(
            "/nonexistcategory/nonexistkey",
            setting,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        )
        self.assertEqual(
            response["error"]["message"],
            "The received data did not pass validation.",
        )
        self.assertEqual(
            response["error"]["errors"][0]["field"],
            "category",
        )

    def test_list_settings(self):
        default_setting_1 = SettingFactory.create(
            category="display",
            key="filter_last_seen_gtr_x_days",
            default_value={"enabled": True, "days": 45},
        )
        SettingFactory.create(
            category="asset_criticality",
            key="os_mac",
            default_value={"enabled": True, "criticality": "tier0"},
        )
        OrganizationSettingFactory.create(
            organization=self.organization,
            setting=default_setting_1,
            overridden_value={"enabled": True, "days": 55},
        )

        resp = self._get()
        settings = resp["items"]
        self.assertEqual(len(settings), 2)
        for setting in settings:
            if setting["category"] == "display":
                self.assertEqual(
                    setting,
                    {
                        "category": "display",
                        "category_name": "Display",
                        "key": "filter_last_seen_gtr_x_days",
                        "default_value": {"enabled": True, "days": 45},
                        "overridden_value": {"enabled": True, "days": 55},
                        "is_default": False,
                    },
                )

            elif setting["category"] == "asset_criticality":
                self.assertEqual(
                    setting,
                    {
                        "category": "asset_criticality",
                        "category_name": "Asset Criticality",
                        "key": "os_mac",
                        "default_value": {"enabled": True, "criticality": "tier0"},
                        "overridden_value": None,
                        "is_default": True,
                    },
                )

    def test_update_settings(self):
        default_setting_1 = SettingFactory.create(
            category="display",
            key="filter_last_seen_gtr_x_days",
            default_value={"enabled": True, "days": 45},
        )
        OrganizationSettingFactory.create(
            organization=self.organization,
            setting=default_setting_1,
            overridden_value={"enabled": True, "days": 55},
        )
        setting = {
            "overridden_value": {"enabled": True, "days": 65},
        }
        setting = self._put("/display/filter_last_seen_gtr_x_days", setting)

        self.assertEqual(
            setting,
            {
                "category": "display",
                "category_name": "Display",
                "key": "filter_last_seen_gtr_x_days",
                "overridden_value": {"enabled": True, "days": 65},
                "default_value": {"enabled": True, "days": 45},
                "is_default": False,
            },
        )

    def test_update_settings_validation_error(self):
        SettingFactory.create(
            category="display",
            key="filter_last_seen_gtr_x_days",
            default_value={"enabled": True, "days": 45},
        )

        value = {"overridden_value": {"enabled": True, "days": "AA"}}

        response = self._put(
            "/display/filter_last_seen_gtr_x_days",
            value,
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        )
        self.assertEqual(
            response["error"]["message"],
            "The received data did not pass validation.",
        )
        self.assertEqual(
            response["error"]["errors"][0]["field"],
            "days",
        )

    def test_delete_one_setting(self):
        default_setting_1 = SettingFactory.create(
            category="display",
            key="filter_last_seen_gtr_x_days",
            default_value={"enabled": True, "days": 45},
        )
        OrganizationSettingFactory.create(
            organization=self.organization,
            setting=default_setting_1,
            overridden_value={"enabled": True, "days": 55},
        )

        response = self._get()
        settings = response["items"]
        self.assertEqual(len(settings), 1)
        setting = settings[0]
        self.assertEqual(
            setting,
            {
                "category": "display",
                "category_name": "Display",
                "key": "filter_last_seen_gtr_x_days",
                "default_value": {"enabled": True, "days": 45},
                "overridden_value": {"enabled": True, "days": 55},
                "is_default": False,
            },
        )

        self._delete("/display/filter_last_seen_gtr_x_days")

        response = self._get()
        settings = response["items"]
        self.assertEqual(len(settings), 1)
        setting = settings[0]
        self.assertEqual(
            setting,
            {
                "category": "display",
                "category_name": "Display",
                "key": "filter_last_seen_gtr_x_days",
                "default_value": {"enabled": True, "days": 45},
                "overridden_value": None,
                "is_default": True,
            },
        )

    def test_delete_one_setting_no_permission(self):
        self.set_org_permissions(self.organization, set())
        self._delete("/display/filter_last_seen_gtr_x_days", status.HTTP_403_FORBIDDEN)

    def test_delete_one_setting_fail(self):
        self._delete(
            "/nonexistcategory/nonexistkey", status.HTTP_422_UNPROCESSABLE_ENTITY
        )

    def test_delete_category_setting(self):
        SettingFactory.create(
            category="display",
            key="filter_last_seen_gtr_x_days",
            default_value={"enabled": True, "days": 45},
        )
        mac_setting = SettingFactory.create(
            category="asset_criticality",
            key="os_mac",
            default_value={"enabled": True, "criticality": "tier4"},
        )
        win_setting = SettingFactory.create(
            category="asset_criticality",
            key="os_windows_server",
            default_value={"enabled": True, "criticality": "tier3"},
        )
        OrganizationSettingFactory.create(
            organization=self.organization,
            setting=mac_setting,
            overridden_value={"enabled": False, "criticality": "tier0"},
        )
        OrganizationSettingFactory.create(
            organization=self.organization,
            setting=win_setting,
            overridden_value={"enabled": True, "criticality": "tier1"},
        )

        response = self._get()
        settings = response["items"]
        for setting in settings:
            if setting["category"] == "asset_criticality":
                self.assertFalse(setting["is_default"])
                self.assertTrue(setting["overridden_value"])

        self._delete("/asset_criticality")

        response = self._get()
        settings = response["items"]
        for setting in settings:
            if setting["category"] == "asset_criticality":
                self.assertTrue(setting["is_default"])
                self.assertFalse(setting["overridden_value"])

    def test_delete_category_setting_no_permission(self):
        self.set_org_permissions(self.organization, set())
        self._delete("/display", status.HTTP_403_FORBIDDEN)

    def test_delete_category_setting_fail(self):
        self._delete("/nonexistcategory", status.HTTP_422_UNPROCESSABLE_ENTITY)

    def test_create_setting_audit_log(self):
        setting = SettingFactory.create(
            category="display",
            key="filter_last_seen_gtr_x_days",
            default_value={"enabled": True, "days": 45},
        )
        value = {
            "overridden_value": {"enabled": True, "days": 55},
        }

        with patch.object(service_bus, "send_command") as m_send_command:
            self._put(
                "/display/filter_last_seen_gtr_x_days",
                value,
            )

        m_send_command.assert_called_once()
        command, audit_log = m_send_command.call_args[0]

        org_setting = OrganizationSetting.objects.get(
            organization__id=self.organization.id, setting=setting
        )
        self.assertEqual(COMMAND_AUDIT_LOG_CREATE, command)
        self.assertEqual(audit_log.entity.id, org_setting.id)
        self.assertEqual(
            audit_log.entity.type_id, EntityTypeId.ASSET_INVENTORY_ORG_SETTING
        )
        self.assertEqual(
            audit_log.entity.account_id, org_setting.organization.account_id
        )
        self.assertEqual(audit_log.entity.organization_id, org_setting.organization_id)
        self.assertEqual(audit_log.action, Action.CREATE)
        self.assertDictEqual(
            audit_log.details,
            {
                "category": "display",
                "key": "filter_last_seen_gtr_x_days",
                "overridden_value": {"enabled": True, "days": 55},
            },
        )

    def test_update_setting_audit_log(self):
        setting = SettingFactory.create(
            category="display",
            key="filter_last_seen_gtr_x_days",
            default_value={"enabled": True, "days": 45},
        )
        original_org_setting = OrganizationSettingFactory.create(
            organization=self.organization,
            setting=setting,
            overridden_value={"enabled": True, "days": 55},
        )
        value = {
            "overridden_value": {"enabled": True, "days": 65},
        }

        with patch.object(service_bus, "send_command") as m_send_command:
            self._put(
                "/display/filter_last_seen_gtr_x_days",
                value,
            )

        m_send_command.assert_called_once()
        command, audit_log = m_send_command.call_args[0]

        org_setting = OrganizationSetting.objects.get(
            organization__id=self.organization.id, setting=setting
        )
        self.assertEqual(COMMAND_AUDIT_LOG_CREATE, command)
        self.assertEqual(audit_log.entity.id, org_setting.id)
        self.assertEqual(
            audit_log.entity.type_id, EntityTypeId.ASSET_INVENTORY_ORG_SETTING
        )
        self.assertEqual(
            audit_log.entity.account_id, org_setting.organization.account_id
        )
        self.assertEqual(audit_log.entity.organization_id, org_setting.organization_id)
        self.assertEqual(audit_log.action, Action.UPDATE)
        self.assertDictEqual(
            audit_log.details,
            {
                "field_name": "overridden_value",
                "old_value_raw": original_org_setting.overridden_value,
                "value_raw": value["overridden_value"],
            },
        )

    def test_delete_setting_audit_log(self):
        setting = SettingFactory.create(
            category="display",
            key="filter_last_seen_gtr_x_days",
            default_value={"enabled": True, "days": 45},
        )
        org_setting = OrganizationSettingFactory.create(
            organization=self.organization,
            setting=setting,
            overridden_value={"enabled": True, "days": 55},
        )
        org_setting_values = {
            "id": org_setting.id,
            "org_id": org_setting.organization_id,
            "account_id": org_setting.organization.account_id,
        }

        with patch.object(service_bus, "send_command") as m_send_command:
            self._delete("/display/filter_last_seen_gtr_x_days")

        m_send_command.assert_called_once()
        command, audit_log = m_send_command.call_args[0]

        self.assertEqual(COMMAND_AUDIT_LOG_CREATE, command)
        self.assertEqual(audit_log.entity.id, UUID(org_setting_values["id"]))
        self.assertEqual(
            audit_log.entity.type_id, EntityTypeId.ASSET_INVENTORY_ORG_SETTING
        )
        self.assertEqual(audit_log.entity.account_id, org_setting_values["account_id"])
        self.assertEqual(audit_log.entity.organization_id, org_setting_values["org_id"])
        self.assertEqual(audit_log.action, Action.DELETE)
        self.assertDictEqual(
            audit_log.details,
            {
                "category": "display",
                "key": "filter_last_seen_gtr_x_days",
                "overridden_value": {"enabled": True, "days": 55},
            },
        )

    def _settings_url(self):
        return "api/v1/settings"

    def _put(self, path, data, status_code=status.HTTP_200_OK):
        response = self.api_client.put(self._settings_url() + path, json=data)
        self.assertEqual(response.status_code, status_code)
        return response.json()

    def _delete(self, path, status_code=status.HTTP_200_OK):
        response = self.api_client.delete(self._settings_url() + path)
        self.assertEqual(response.status_code, status_code)

    def _get(self, status_code=status.HTTP_200_OK):
        response = self.api_client.get(self._settings_url())
        self.assertEqual(response.status_code, status_code)
        return response.json()
