from fastapi import APIRouter, Depends

from apps.api.dependencies import session
from apps.api.v1.routers import configuration, hosts, settings, stats, version

org_routers = [
    configuration.router,
    hosts.router,
    stats.router,
    settings.router,
]

api_router = APIRouter()
for r in org_routers:
    api_router.include_router(r, prefix="/v1", dependencies=[Depends(session)])

api_router.include_router(version.router, prefix="/v1")
