from enum import StrEnum
from functools import cache

from apps.api.v1.schemas.configuration import (
    ConfigurationState,
    CoverageState,
    IntegrationState,
)
from apps.assets.models import CoverageCategory
from apps.assets.search.host import HostClient, InventoryFilter
from apps.integrations.models import Integration


class OrganizationIntegrationStateService:
    def filter_gaps(
        self,
        org_id,
        coverage_gaps: list[CoverageCategory],
    ) -> list[CoverageCategory]:
        return [
            gap for gap in coverage_gaps if self.has_coverage_configured(org_id, gap)
        ]

    def get_coverage_gap_labels(self, org_id) -> dict[StrEnum, str]:
        # circular import
        from apps.api.v1.schemas.filter import CoverageGap

        labels = CoverageGap.labels()
        return {
            gap: labels[gap]
            for gap in CoverageGap
            if self.is_category_configured(org_id, gap.to_categories())
        }

    def is_category_configured(
        self, org_id, categories: dict[CoverageCategory, str]
    ) -> bool:
        # skip cases where we have multiple categories or no categories for cases such as 'Any' or 'No Gaps'
        if len(categories) != 1:
            return True

        category = categories[0]
        if category == CoverageCategory.ENDPOINT_SECURITY:
            return self.has_endpoint_security_configured(org_id)
        elif category == CoverageCategory.VULNERABILITY_MANAGEMENT:
            return self.has_vulnerability_management_configured(org_id)
        elif category == CoverageCategory.TECHNICAL_SECURITY_CONTROL:
            return self.has_endpoint_security_configured(
                org_id
            ) and self.has_vulnerability_management_configured(org_id)

        return True

    def has_coverage_configured(
        self, org_id, coverage_category: CoverageCategory
    ) -> bool:
        if coverage_category == CoverageCategory.ENDPOINT_SECURITY:
            return self.has_endpoint_security_configured(org_id)
        elif coverage_category == CoverageCategory.VULNERABILITY_MANAGEMENT:
            return self.has_vulnerability_management_configured(org_id)
        raise ValueError(
            f"Unsupported coverage gap: {coverage_category}"
        )  # pragma: no cover

    def get_configuration_state(self, org_id):
        return ConfigurationState(
            endpoint_coverage=self.get_endpoint_coverage_state(org_id),
            vulnerability_coverage=self.get_vulnerability_coverage_state(org_id),
            service_integrations=self.get_service_integrations(org_id),
        )

    def get_endpoint_coverage_state(self, org_id):
        return self.get_coverage_state(org_id, CoverageCategory.ENDPOINT_SECURITY)

    def get_vulnerability_coverage_state(self, org_id):
        return self.get_coverage_state(
            org_id, CoverageCategory.VULNERABILITY_MANAGEMENT
        )

    def get_service_integrations(self, org_id):
        integrations = Integration.objects.filter(organization__id=org_id, enabled=True)

        if not integrations:
            return []

        integration_filters = {
            str(integration.id): {
                "term": {
                    f"source_data.{integration.technology.technology_id if integration.technology else 'unknown'}.metadata.integration.id": str(
                        integration.id
                    )
                }
            }
            for integration in integrations
        }

        client = HostClient({org_id}, InventoryFilter())

        body = {
            "size": 0,
            "query": {
                "bool": {"filter": [{"terms": {"metadata.organization_id": [org_id]}}]}
            },
            "aggs": {"integration": {"filters": {"filters": integration_filters}}},
        }

        response = client.search(body)
        totals = {}
        if aggregations := response.get("aggregations"):
            totals = {
                id: doc["doc_count"]
                for id, doc in aggregations["integration"]["buckets"].items()
            }

        service_integrations = [
            IntegrationState(
                id=str(integration.id),
                technology_id=integration.technology.technology_id if integration.technology else "unknown",
                has_data=totals.get(str(integration.id), 0) > 0,
            )
            for integration in integrations
        ]

        return service_integrations

    def get_coverage_state(self, org_id, coverage_category: CoverageCategory):
        return CoverageState(
            has_eligible_asset_sources=self.has_eligible_asset_sources(
                org_id, coverage_category
            ),
            has_designated_asset_sources=self.has_coverage_configured(
                org_id, coverage_category
            ),
            has_additional_asset_sources=self.has_additional_asset_sources(org_id),
        )

    def has_eligible_asset_sources(
        self, org_id, coverage_category: CoverageCategory
    ) -> bool:
        if coverage_category == CoverageCategory.ENDPOINT_SECURITY:
            return self.meets_endpoint_security_threshold(org_id)
        elif coverage_category == CoverageCategory.VULNERABILITY_MANAGEMENT:
            return self.meets_vulnerability_management_threshold(org_id)
        raise ValueError(
            f"Unsupported coverage gap: {coverage_category}"
        )  # pragma: no cover

    @cache
    def has_endpoint_security_configured(self, org_id) -> bool:
        return Integration.objects.filter(
            organization_id=org_id,
            endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
            enabled=True,
        ).exists()

    @cache
    def meets_endpoint_security_threshold(self, org_id) -> bool:
        return (
            Integration.objects.filter(
                organization_id=org_id,
                enabled=True,
            )
            .exclude(endpoint_coverage_mode=Integration.CoverageMode.NOT_APPLICABLE)
            .count()
            >= 1
        )

    @cache
    def has_vulnerability_management_configured(self, org_id) -> bool:
        return Integration.objects.filter(
            organization_id=org_id,
            vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
            enabled=True,
        ).exists()

    @cache
    def meets_vulnerability_management_threshold(self, org_id) -> bool:
        return (
            Integration.objects.filter(
                organization_id=org_id,
                enabled=True,
            )
            .exclude(
                vulnerability_coverage_mode=Integration.CoverageMode.NOT_APPLICABLE
            )
            .count()
            >= 1
        )

    @cache
    def has_additional_asset_sources(self, org_id) -> bool:
        return (
            Integration.objects.filter(
                organization_id=org_id,
                enabled=True,
            ).count()
            >= 2
        )
