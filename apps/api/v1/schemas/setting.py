from enum import StrEnum
from typing import Optional, Sequence

from pydantic import BaseModel, ConfigDict


class Category(StrEnum):
    DISPLAY = "display"
    ASSET_CRITICALITY = "asset_criticality"
    PURGE_AUTOMATION = "purge_automation"


class SettingUpsert(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    overridden_value: dict


class Setting(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    category: Category
    category_name: str
    key: str
    overridden_value: Optional[dict]
    default_value: dict
    is_default: bool


class SettingListResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    items: Sequence[Setting]
