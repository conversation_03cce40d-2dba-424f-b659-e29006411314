from uuid import UUID

from pydantic import BaseModel, ConfigDict


class CoverageState(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    has_eligible_asset_sources: bool
    has_designated_asset_sources: bool
    has_additional_asset_sources: bool


class IntegrationState(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    id: UUID
    technology_id: str
    has_data: bool


class ConfigurationState(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    endpoint_coverage: CoverageState
    vulnerability_coverage: CoverageState
    service_integrations: list[IntegrationState]
