from datetime import datetime
from enum import StrEnum, auto
from typing import Optional, Sequence
from uuid import UUID

from criticalstart.fastapi_utils.parameters.period import QueryPeriodType, parse_period
from fastapi import Query
from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    field_validator,
)

from apps.api.configuration import OrganizationIntegrationStateService
from apps.assets import models
from apps.assets.search.host import InventoryFilter
from apps.integrations.utils import get_external_technology_ids, get_technology_name

from .base import MetaEntry

external_tech_ids = get_external_technology_ids()
TechnologyId = StrEnum(
    "TechnologyId",
    {tech_id: tech_id for tech_id in external_tech_ids},
)
TechnologyLabels = {
    tech_id: get_technology_name(tech_id) for tech_id in external_tech_ids
}


class HostType(StrEnum):
    SERVER = "server"
    WORKSTATION = "workstation"
    OTHER = "other"

    @staticmethod
    def labels():
        return {
            HostType.SERVER: "Server",
            HostType.WORKSTATION: "Workstation",
            HostType.OTHER: "Other",
        }

    def to_model_values(self) -> set[models.HostType]:
        if self == HostType.OTHER:
            other_model_types = set(models.HostType) - set(HostType)
            return other_model_types | {models.HostType.OTHER}
        else:
            return {models.HostType(self)}


class OsAttributes(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    family: models.OsFamily
    name: Optional[str]
    host_type: HostType

    @staticmethod
    def from_dataclass(os_attributes: models.OsAttributes):
        if os_attributes.host_type in set(HostType):
            host_type = HostType(os_attributes.host_type)
        else:
            host_type = HostType.OTHER

        return OsAttributes(
            family=os_attributes.family,
            name=os_attributes.name,
            host_type=host_type,
        )


class OwnerAttributes(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: str
    email: Optional[str]

    @staticmethod
    def from_dataclass(owner_attributes: models.OwnerAttributes):
        return OwnerAttributes(
            name=owner_attributes.name,
            email=owner_attributes.email,
        )


class Technology(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    id: str
    name: str

    @staticmethod
    def from_id(technology_id: str):
        return Technology(id=technology_id, name=get_technology_name(technology_id))


class HostCriticality(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    value: models.AssetCriticality = Field(validation_alias="value")


class Integration(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    id: UUID


class HostAttributes(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    aad_id: Optional[str]
    hostname: str
    fqdns: Sequence[str]
    group_name: Sequence[str]  # TODO: Remove once unused by all clients
    group_names: Sequence[str]
    os: OsAttributes
    criticality: models.AssetCriticality
    primary_mac_address: Optional[str]
    primary_ip_address: Optional[str]
    internet_exposure: models.InternetExposure
    owner: Sequence[OwnerAttributes]  # TODO: Remove once unused by all clients
    owners: Sequence[OwnerAttributes]

    # noinspection PyMethodOverriding
    @staticmethod
    def from_dataclass(host_attrs: models.HostAssetAttributes):
        return HostAttributes(
            aad_id=host_attrs.aad_id,
            hostname=host_attrs.hostname,
            fqdns=host_attrs.fqdn,
            group_name=host_attrs.group_name,
            group_names=host_attrs.group_name,
            os=OsAttributes.from_dataclass(host_attrs.os),
            criticality=host_attrs.criticality,
            primary_mac_address=host_attrs.primary_mac_address,
            primary_ip_address=host_attrs.primary_ip_address,
            internet_exposure=host_attrs.internet_exposure,
            owner=[OwnerAttributes.from_dataclass(o) for o in host_attrs.owner],
            owners=[OwnerAttributes.from_dataclass(o) for o in host_attrs.owner],
        )


class HostSource(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    technology: Technology
    integration: Integration
    attributes: HostAttributes
    last_seen: Optional[datetime]

    # noinspection PyMethodOverriding
    @staticmethod
    def from_dataclass(source_asset: models.MergedSourceAsset):
        if isinstance(source_asset.attributes, models.HostAssetAttributes):
            return HostSource(
                attributes=HostAttributes.from_dataclass(source_asset.attributes),
                last_seen=source_asset.last_seen,
                technology=Technology.from_id(source_asset.technology_id),
                integration=Integration(id=source_asset.integration_id),
            )
        raise TypeError("Source asset is not a host.")  # pragma: no cover


class HostBase(HostAttributes):
    id: UUID
    organization_id: UUID
    last_seen: Optional[datetime]
    created: Optional[datetime]
    endpoint_security_technologies: Sequence[Technology]
    endpoint_security_excluded: bool
    vulnerability_technologies: Sequence[Technology]
    vulnerability_management_excluded: bool
    coverage_gaps: list[models.CoverageCategory]
    manually_merged: bool

    # noinspection PyMethodOverriding
    @staticmethod
    def from_dataclass(
        merged_asset: models.MergedAsset,
        config_manager: OrganizationIntegrationStateService,
    ):
        if isinstance(merged_asset.merged_data, models.HostAssetAttributes):
            # Apply overrides here when returning merged asset data.
            merged_asset = merged_asset.overridden_merged_asset
            return HostBase(
                **HostAttributes.from_dataclass(merged_asset.merged_data).model_dump(),
                id=merged_asset.id,
                organization_id=merged_asset.metadata.organization_id,
                last_seen=merged_asset.metadata.asset.last_seen,
                created=merged_asset.metadata.created,
                endpoint_security_technologies=[
                    Technology.from_id(t)
                    for t in merged_asset.metadata.asset.technologies.endpoint_security
                ],
                vulnerability_technologies=[
                    Technology.from_id(t)
                    for t in merged_asset.metadata.asset.technologies.vulnerability_management
                ],
                endpoint_security_excluded=merged_asset.metadata.asset.technologies.endpoint_security_excluded,
                vulnerability_management_excluded=merged_asset.metadata.asset.technologies.vulnerability_management_excluded,
                coverage_gaps=config_manager.filter_gaps(
                    merged_asset.metadata.organization_id, merged_asset.coverage_gaps
                ),
                manually_merged=merged_asset.metadata.manually_merged,
            )
        raise TypeError("Merged asset is not a host.")  # pragma: no cover


class HostSummary(HostBase):
    technologies: Sequence[Technology]
    filtered_last_seen: Optional[datetime]

    # noinspection PyMethodOverriding
    @staticmethod
    def from_dataclass(
        merged_asset: models.MergedAsset,
        technology_for_last_seen: str = None,
        config_manager: OrganizationIntegrationStateService = None,
    ):
        technologies = [
            Technology.from_id(t)
            for t in merged_asset.metadata.asset.technologies.all
            if t in TechnologyLabels
        ]

        return HostSummary(
            **HostBase.from_dataclass(merged_asset, config_manager).model_dump(),
            technologies=technologies,
            filtered_last_seen=merged_asset.last_seen(technology_for_last_seen),
        )


class Host(HostBase):
    sources: Sequence[HostSource]

    # noinspection PyMethodOverriding
    @staticmethod
    def from_dataclass(
        merged_asset: models.MergedAsset,
        config_manager: OrganizationIntegrationStateService,
    ):
        return Host(
            **HostBase.from_dataclass(merged_asset, config_manager).model_dump(),
            sources=[
                HostSource.from_dataclass(s)
                for s in merged_asset.source_data.all()
                if s.technology_id in TechnologyLabels
            ],
        )


class HostMeta(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    coverage_gap: MetaEntry
    internet_exposure: MetaEntry
    os_family: MetaEntry
    host_type: MetaEntry
    criticality: MetaEntry
    technology: MetaEntry
    integration: MetaEntry
    endpoint_security_combined: MetaEntry
    vulnerability_management_combined: MetaEntry
    duplicate_host_search_fieldname: MetaEntry


class CoverageGap(StrEnum):
    ANY = auto()
    ENDPOINT_SECURITY = auto()
    VULNERABILITY_MANAGEMENT = auto()
    TECHNICAL_SECURITY_CONTROL = auto()

    NO_GAPS = auto()

    @classmethod
    def labels(cls):
        return {
            cls.ANY: "Any",
            cls.ENDPOINT_SECURITY: "Endpoint Security",
            cls.VULNERABILITY_MANAGEMENT: "Vulnerability",
            cls.TECHNICAL_SECURITY_CONTROL: "Technical Security Control",
            cls.NO_GAPS: "No Gaps",
        }

    def to_categories(self):
        lookup = {
            self.ANY: [c for c in models.CoverageCategory.supported()],
            self.ENDPOINT_SECURITY: [models.CoverageCategory.ENDPOINT_SECURITY],
            self.VULNERABILITY_MANAGEMENT: [
                models.CoverageCategory.VULNERABILITY_MANAGEMENT
            ],
            self.TECHNICAL_SECURITY_CONTROL: [
                models.CoverageCategory.TECHNICAL_SECURITY_CONTROL
            ],
            self.NO_GAPS: [models.CoverageCategory.NONE],
        }
        return lookup[self]


class QueryInventoryFilter:
    def __call__(
        self,
        coverage_gap: Optional[list[str]] = Query(default_factory=list),
        last_seen: Optional[QueryPeriodType] = Query(None),
        last_seen_endpoint_security: Optional[QueryPeriodType] = Query(None),
        last_seen_vulnerability_management: Optional[QueryPeriodType] = Query(None),
        created: Optional[QueryPeriodType] = Query(None),
        os_family: Optional[list[str]] = Query(None),
        host_type: Optional[list[HostType]] = Query(None),
        criticality: Optional[list[models.AssetCriticality]] = Query(None),
        technology: Optional[list[TechnologyId]] = Query(default_factory=list),
        integration: Optional[list[str]] = Query(default_factory=list),
        endpoint_security_combined: Optional[list[str]] = Query(default_factory=list),
        endpoint_security_excluded: Optional[bool] = Query(None),
        vulnerability_management_combined: Optional[list[str]] = Query(
            default_factory=list
        ),
        vulnerability_management_excluded: Optional[bool] = Query(None),
        manually_merged: Optional[bool] = Query(None),
    ):
        all_technology_ids = list(TechnologyId)
        all_coverage_gaps = list(CoverageGap)
        missing_technology_ids = []
        missing_coverage = []
        for c in coverage_gap:
            if c in all_technology_ids:
                missing_technology_ids.append(TechnologyId(c))
            elif c in all_coverage_gaps:
                missing_coverage.extend(CoverageGap(c).to_categories())

        # Optimize search all
        all_searchable = models.CoverageCategory.supported()
        all_searchable.append(models.CoverageCategory.NONE)
        if all(c in missing_coverage for c in all_searchable):
            missing_coverage = None

        technology_ids = [str(t.value) for t in technology]
        integration_ids = [str(i) for i in integration or []]

        model_host_types = None
        if host_type:
            model_host_types = set()
            for host_type in host_type:
                model_host_types.update(host_type.to_model_values())
            model_host_types = list(model_host_types)

        last_seen = parse_period(last_seen)
        last_seen_endpoint_security = parse_period(last_seen_endpoint_security)
        last_seen_vulnerability_management = parse_period(
            last_seen_vulnerability_management
        )
        created = parse_period(created)

        return InventoryFilter(
            missing_coverage=missing_coverage,
            last_seen=last_seen,
            last_seen_endpoint_security=last_seen_endpoint_security,
            last_seen_vulnerability_management=last_seen_vulnerability_management,
            created=created,
            os_family=os_family,
            host_type=model_host_types,
            criticality=criticality,
            technology_ids=technology_ids,
            integration_ids=integration_ids,
            missing_technology_ids=missing_technology_ids,
            endpoint_security_combined=endpoint_security_combined,
            endpoint_security_excluded=endpoint_security_excluded,
            vulnerability_management_combined=vulnerability_management_combined,
            vulnerability_management_excluded=vulnerability_management_excluded,
            manually_merged=manually_merged,
        )


class HostCoverageGapExcluded(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    coverage_gap: CoverageGap
    exclude: bool

    @field_validator("coverage_gap")
    def validate_coverage_gap(cls, coverage_gap):
        if coverage_gap in (
            CoverageGap.ENDPOINT_SECURITY,
            CoverageGap.VULNERABILITY_MANAGEMENT,
        ):
            return f"{coverage_gap}_excluded"
        else:
            raise ValueError(f"Invalid coverage gap: {coverage_gap}")


class HostMerge(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    ids: list[UUID]


class HostUnmerge(BaseModel):
    model_config = ConfigDict(from_attributes=True)


class DuplicateHostSearchFieldname(StrEnum):
    HOSTNAME = auto()
    PRIMARY_IP_ADDRESS = auto()
    PRIMARY_MAC_ADDRESS = auto()

    @classmethod
    def labels(cls):
        return {
            cls.HOSTNAME: "Hostname",
            cls.PRIMARY_IP_ADDRESS: "IP Address",
            cls.PRIMARY_MAC_ADDRESS: "MAC Address",
        }


class DuplicateSummary(BaseModel):
    count: int


class DuplicateHostSearchResultDetails(BaseModel):
    duplicates: dict[str, list[HostSummary]]


class DuplicateHostSearchResult(BaseModel):
    duplicates: dict[str, DuplicateSummary]
