from typing import Annotated
from uuid import UUID

from criticalstart.auth.v2.integrations.fastapi import UserSession
from fastapi import APIRouter, Body, Depends

import apps.assets.models as models
from apps.api.dependencies import session
from apps.api.permissions import AssetInventoryPermission, check_permissions
from apps.api.v1.schemas.setting import (
    Category,
    Setting,
    SettingListResponse,
    SettingUpsert,
)
from apps.assets.models.setting import settings_values_map

router = APIRouter(
    prefix="/settings",
    tags=["settings"],
)


def translate_setting_obj(setting, overridden_value):
    return Setting(
        category=setting.category,
        category_name=setting.get_category_display(),
        key=setting.key,
        overridden_value=overridden_value if overridden_value else None,
        default_value=setting.default_value,
        is_default=False if overridden_value else True,
    )


@router.get("", response_model=SettingListResponse)
def list_settings(session: UserSession = Depends(session)):
    list_items = models.OrganizationSetting.objects.get_settings_list(
        session.organization.id
    )
    response = [
        translate_setting_obj(setting, setting.overridden_value)
        for setting in list_items
    ]
    return SettingListResponse(items=response)


# create an example without None values
example_filter_setting_value = models.setting.FilterSettingValue(
    coverage_gap=["endpoint_security"],
    last_seen="7day",
    last_seen_endpoint_security="!14day",
    last_seen_vulnerability_management="!14day",
    created="7day",
    os_family=["windows"],
    criticality=[models.AssetCriticality.TIER_2],
    technology=["defender_atp"],
    endpoint_security_combined=["defender_atp"],
    endpoint_security_excluded=False,
    vulnerability_management_combined=["tenable_io"],
    vulnerability_management_excluded=False,
    manually_merged=False,
)


def get_example(default):
    if isinstance(default, models.setting.FilterSettingValue):
        # We need a custom example for FilterSettingValue
        # because null values are remove from fastapi openapi
        # example generation.
        return example_filter_setting_value
    return default


setting_value_docs = {
    f"{category}/{key}": {
        "summary": f"{category}/{key}",
        "value": {"overridden_value": get_example(default_value)},
    }
    for category, keys in settings_values_map.items()
    for key, default_value in keys.items()
}


@router.put("/{category}/{key}", response_model=Setting)
def upsert_setting(
    category: Category,
    key: models.SettingKey,
    setting: Annotated[SettingUpsert, Body(openapi_examples=setting_value_docs)],
    session: UserSession = Depends(session),
):
    check_permissions(
        session, AssetInventoryPermission.EDIT_SETTINGS, [session.organization.id]
    )
    default_settings = models.Setting.objects.get(category=category, key=key)
    org_setting, created = models.OrganizationSetting.objects.get_or_create(
        organization_id=session.organization.id,
        setting=default_settings,
        defaults={"overridden_value": setting.overridden_value},
    )
    if created:
        models.OrganizationSetting.audit_logs.create(org_setting, session.user.id)
    else:
        original_value = org_setting.overridden_value
        org_setting.overridden_value = setting.overridden_value
        org_setting.save()
        models.OrganizationSetting.audit_logs.update(
            org_setting,
            session.user.id,
            "overridden_value",
            original_value,
            setting.overridden_value,
        )

    response = translate_setting_obj(org_setting.setting, org_setting.overridden_value)
    return response


def delete_util(
    org_id: UUID, user_id: UUID, category: Category, key: models.SettingKey = None
):
    filters = {
        "organization_id": org_id,
        "setting__category": category,
    }
    if key is not None:
        filters["setting__key"] = key
    org_settings = models.OrganizationSetting.objects.filter(**filters)
    before_deleted = list(org_settings)
    org_settings.delete()

    models.OrganizationSetting.audit_logs.delete(before_deleted, user_id)


@router.delete("/{category}/{key}", response_model=None)
def delete_setting(
    category: Category,
    key: models.SettingKey,
    session: UserSession = Depends(session),
):
    check_permissions(
        session, AssetInventoryPermission.EDIT_SETTINGS, [session.organization.id]
    )
    return delete_util(session.organization.id, session.user.id, category, key)


@router.delete("/{category}", response_model=None)
def delete_setting_category(
    category: Category, session: UserSession = Depends(session)
):
    check_permissions(
        session, AssetInventoryPermission.EDIT_SETTINGS, [session.organization.id]
    )
    return delete_util(session.organization.id, session.user.id, category)
