from collections.abc import Iterable
from functools import partial
from typing import Any, Optional
from uuid import UUID

from ata_common.chunking import chunks
from criticalstart.auth.v2.integrations.fastapi import UserSession
from criticalstart.fastapi_utils.paginate.elasticsearch import (
    ElasticSearchBasicSort,
    ElasticSearchBasicSortWithMode,
    ElasticSearchScriptSort,
    ElasticSearchSortConfig,
    Page,
    PageParams,
    make_page_params,
    paginate,
)
from fastapi import APIRouter, Depends, Query, Request, status
from fastapi.exceptions import HTTPException

from apps.accounts.models import Organization
from apps.api.configuration import OrganizationIntegrationStateService
from apps.api.dependencies import session
from apps.api.entitlements import check_filter_entitlements, check_host_entitlements
from apps.api.permissions import (
    AssetInventoryPermission,
    cached_permissions_checker,
    check_permissions,
)
from apps.api.v1.schemas.filter import (
    DuplicateHostSearchFieldname,
    DuplicateHostSearchResult,
    DuplicateHostSearchResultDetails,
    DuplicateSummary,
    Host,
    HostCoverageGapExcluded,
    HostCriticality,
    HostMerge,
    HostMeta,
    HostSummary,
    HostType,
    HostUnmerge,
    QueryInventoryFilter,
    TechnologyLabels,
)
from apps.api.v1.utils import labels_to_meta
from apps.assets.models import SourceHost
from apps.assets.models.merged_asset import MergedAsset, MergedAssetFieldGroups
from apps.assets.models.merged_source_asset import (
    AssetCriticality,
    AssetType,
    CoverageCategory,
    InternetExposure,
    OsFamily,
)
from apps.assets.search.host import HostClient, InventoryFilter
from apps.assets.services import merge_service
from apps.integrations.models import Integration
from apps.stats.base import StatCategory, stats
from core import settings

HOST_TYPE = AssetType.HOST.value

router = APIRouter(
    prefix="/hosts",
    tags=["hosts"],
)


class DynamicHostSort(ElasticSearchSortConfig):
    def __init__(self, last_seen_term):
        self.last_seen_term = last_seen_term

    @property
    def sort_fields(self):
        return {
            "hostname": ElasticSearchBasicSort(field="merged_data.hostname.keyword"),
            "last_seen": ElasticSearchBasicSortWithMode(
                field=self.last_seen_term, mode="max"
            ),
            "created": ElasticSearchBasicSort(field="metadata.created"),
            "fqdn": ElasticSearchBasicSort(field="merged_data.fqdn.keyword"),
            "device_type": ElasticSearchBasicSort(
                field="merged_data.device_type.keyword"
            ),
            "os_name": ElasticSearchBasicSort(field="merged_data.os.name.keyword"),
            "primary_mac_address": ElasticSearchBasicSort(
                field="merged_data.primary_mac_address.keyword"
            ),
            "primary_ip_address": ElasticSearchBasicSort(
                field="merged_data.primary_ip_address.keyword"
            ),
            "criticality": ElasticSearchScriptSort(
                script="""
                doc['overrides.criticality.keyword'].size() != 0 ?
                doc['overrides.criticality.keyword'].value :
                doc['merged_data.criticality.keyword'].value;
            """
            ),
        }

    default_order_by = "hostname"


class QueryHostSort:
    """Store sort information for use in the query."""

    def __call__(
        self,
        request: Request,
        filters: InventoryFilter = Depends(QueryInventoryFilter()),
    ) -> DynamicHostSort:
        sort = DynamicHostSort(filters.last_seen_term())
        request.sort = sort
        return sort


def host_sort(request: Request):
    if hasattr(request, "sort"):
        return request.sort
    return DynamicHostSort("last_seen")


def validate_host(
    host_id: UUID,
    session: UserSession = Depends(session),
):
    """Validate that the host exists."""
    check_host_entitlements(session)

    host = MergedAsset.documents.get_by_id(host_id)
    if (
        host
        and host.type == HOST_TYPE
        and UUID(host.metadata.organization_id) in session.organization_ids
    ):
        return host

    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Host not found",
    )


def update_override(hosts: Iterable[MergedAsset], attr_name, new_value, user_id: UUID):
    hosts_to_update = []
    for host in hosts:
        existing_value = getattr(host.overrides, attr_name)
        if existing_value != new_value:
            setattr(host.overrides, attr_name, new_value)
            hosts_to_update.append((host, existing_value))

    if hosts_to_update:
        updated_ids = MergedAsset.documents.update_bulk(
            [host for host, _ in hosts_to_update],
            MergedAssetFieldGroups.overrides_fields(),
        )
        updated_ids = set(updated_ids)
        for host, existing_value in hosts_to_update:
            if host.document_id in updated_ids:
                MergedAsset.audit_logs.update(
                    host, user_id, attr_name, existing_value, new_value
                )


def body_from_query(query: Optional[str] = Query(None)):
    if query:
        return MergedAsset.documents.text_query_to_body(query)
    else:
        return {}


@router.get("/meta")
def host_meta(
    session: UserSession = Depends(session),
) -> HostMeta:
    config_manager = OrganizationIntegrationStateService()
    coverage_gap = labels_to_meta(
        config_manager.get_coverage_gap_labels(session.organization.id)
    )

    os_family = labels_to_meta(OsFamily.labels())

    host_type = labels_to_meta(HostType.labels())

    criticality = labels_to_meta(AssetCriticality.labels())

    technology = labels_to_meta(TechnologyLabels)

    internet_exposure = labels_to_meta(InternetExposure.labels())

    duplicate_host_search_fieldname = labels_to_meta(
        DuplicateHostSearchFieldname.labels()
    )

    qs = Integration.objects.filter(organization__id__in=session.organization_ids)
    organization_integrations = {}
    organization_technologies = set()
    for integration in qs:
        organization_integrations[str(integration.id)] = integration.name
        organization_technologies.add(integration.technology_id)

    technology = [t for t in technology if t.id in organization_technologies]

    # Sort technologies by display name
    technology = list(sorted(technology, key=lambda t: t.name))

    integration = labels_to_meta(organization_integrations)

    # sort integrations by display name
    integration = list(sorted(integration, key=lambda i: i.name))

    # Coverage gap can be a category or technology id
    coverage_gap = list(coverage_gap)
    # FIXME: remove after official release
    if not settings.DISABLE_FEATURES:
        coverage_gap += technology

    result = stats.get_stats(
        StatCategory.INVENTORY,
        ["endpoint_coverage", "vulnerability_coverage"],
        session.organization_ids,
        filters=None,
    )

    endpoint_coverage_combined = get_coverage_combined(
        config_manager,
        session.organization.id,
        CoverageCategory.ENDPOINT_SECURITY,
        result,
        "endpoint_coverage",
    )

    vulnerability_coverage_combined = get_coverage_combined(
        config_manager,
        session.organization.id,
        CoverageCategory.VULNERABILITY_MANAGEMENT,
        result,
        "vulnerability_coverage",
    )

    return HostMeta(
        coverage_gap=coverage_gap,
        os_family=os_family,
        host_type=host_type,
        criticality=criticality,
        technology=technology,
        integration=integration,
        endpoint_security_combined=endpoint_coverage_combined,
        vulnerability_management_combined=vulnerability_coverage_combined,
        internet_exposure=internet_exposure,
        duplicate_host_search_fieldname=duplicate_host_search_fieldname,
    )


def get_coverage_combined(
    config_manager: OrganizationIntegrationStateService,
    org_id: UUID,
    coverage_category: CoverageCategory,
    stats_result: dict[str, Any],
    result_key: str,
):
    """
    Get combined coverage labels based on the coverage category and stats result. If the coverage category is not configured
    for the organization, an empty list is returned.

    Args:
        config_manager (OrganizationIntegrationStateService): The configuration manager service.
        org_id (UUID): The organization ID.
        coverage_category (CoverageCategory): The coverage category to check.
        stats_result (dict): The statistics result containing coverage data.
        result_key (str): The key to access the specific coverage data in the stats result.

    Returns:
    """
    coverage_combined = []
    if config_manager.has_coverage_configured(org_id, coverage_category):
        coverage_combined = labels_to_meta(
            {
                t["combined"]: t["name"]
                for t in stats_result[result_key]["totals"]
                if t["combined"]
            }
        )
    return coverage_combined


@router.get("/duplicates")
def get_duplicate_hosts(
    duplicate_search_fieldname: DuplicateHostSearchFieldname = Query(...),
    include_details: bool = Query(True),
    session: UserSession = Depends(session),
) -> DuplicateHostSearchResultDetails | DuplicateHostSearchResult:
    check_host_entitlements(session)

    if not include_details:
        duplicate_field_counts = MergedAsset.documents.get_duplicate_field_counts(
            organization_id=session.organization.id,
            field=duplicate_search_fieldname.value,
        )
        results = {
            key: DuplicateSummary(count=count)
            for key, count in duplicate_field_counts.items()
        }
        return DuplicateHostSearchResult(duplicates=results)

    # TODO: Remove rest of this method once the Front End adopts the new parameter: include_details

    # get the duplicate merged assets based on the search parameter
    duplicate_assets = MergedAsset.documents.get_duplicates_by_field(
        organization_id=session.organization.id,
        field=duplicate_search_fieldname.value,
    )

    config_manager = OrganizationIntegrationStateService()

    def convert_to_host_summary(assets):
        return [
            HostSummary.from_dataclass(asset, config_manager=config_manager)
            for asset in assets
        ]

    # convert the duplicate merged assets to HostSummary objects
    duplicate_hosts = {
        key: convert_to_host_summary(assets) for key, assets in duplicate_assets.items()
    }

    return DuplicateHostSearchResultDetails(duplicates=duplicate_hosts)


# Must be routed after meta
@router.get("/{host_id}", response_model=Host)
def get_host(
    merged_asset: MergedAsset = Depends(validate_host),
) -> Host:
    config_manager = OrganizationIntegrationStateService()
    return Host.from_dataclass(merged_asset, config_manager=config_manager)


@router.get("")
def list_hosts(
    session: UserSession = Depends(session),
    body: dict = Depends(body_from_query),
    filters: InventoryFilter = Depends(QueryInventoryFilter()),
    # Save the last seen sort column to the request
    sort: DynamicHostSort = Depends(QueryHostSort()),
    paging: PageParams = Depends(make_page_params(host_sort)),
) -> Page[HostSummary]:
    check_filter_entitlements(session, filters)
    client = HostClient(organization_ids=session.organization_ids, filters=filters)

    config_manager = OrganizationIntegrationStateService()

    def host_converter(merged_asset: MergedAsset) -> HostSummary:
        # For now, filtered_last_seen is only set to a source asset's last_seen
        # if one and only one technology was requested.  Otherwise, it is set to
        # the latest last seen for all source assets; the merged last seen.
        # In the future, we may return the latest last seen for the requested
        # technologies, if we can make it work efficiently with sorted pagination.
        tech_for_last_seen = None
        if filters.technology_ids and len(filters.technology_ids) == 1:
            tech_for_last_seen = filters.technology_ids[0]
        return HostSummary.from_dataclass(
            merged_asset,
            technology_for_last_seen=tech_for_last_seen,
            config_manager=config_manager,
        )

    return paginate(body, client, paging, host_converter)


@router.put("/coverage_gap_excluded")
def list_set_coverage_gap_excluded(
    coverage_exclude: HostCoverageGapExcluded,
    session: UserSession = Depends(session),
    body: dict = Depends(body_from_query),
    filters: InventoryFilter = Depends(QueryInventoryFilter()),
):
    check_host_entitlements(session)

    client = HostClient(organization_ids=session.organization_ids, filters=filters)

    update_override(
        client.scan(body),
        coverage_exclude.coverage_gap,
        coverage_exclude.exclude,
        session.user.id,
    )


@router.put("/{host_id}/coverage_gap_excluded")
def set_coverage_gap_excluded(
    coverage_exclude: HostCoverageGapExcluded,
    session: UserSession = Depends(session),
    host: MergedAsset = Depends(validate_host),
):
    check_host_entitlements(session)

    update_override(
        [host],
        coverage_exclude.coverage_gap,
        coverage_exclude.exclude,
        session.user.id,
    )


@router.put("/criticality")
def list_set_criticality(
    criticality: HostCriticality,
    session: UserSession = Depends(session),
    body: dict = Depends(body_from_query),
    filters: InventoryFilter = Depends(QueryInventoryFilter()),
) -> HostCriticality:
    check_host_entitlements(session)

    client = HostClient(organization_ids=session.organization_ids, filters=filters)

    update_override(
        client.scan(body),
        "criticality",
        criticality.value or None,
        session.user.id,
    )

    return criticality


@router.put("/{host_id}/criticality")
def set_criticality(
    criticality: HostCriticality,
    session: UserSession = Depends(session),
    host: MergedAsset = Depends(validate_host),
) -> HostCriticality:
    check_host_entitlements(session)

    update_override(
        [host],
        "criticality",
        criticality.value or None,
        session.user.id,
    )

    return criticality


@router.delete("/{host_id}")
def delete_host(
    session: UserSession = Depends(session),
    host: MergedAsset = Depends(validate_host),
) -> None:
    check_host_entitlements(session)
    check_permissions(
        session,
        AssetInventoryPermission.DELETE_ASSET,
        [host.metadata.organization_id],
    )

    deleted_ids = MergedAsset.documents.delete_bulk([host])
    SourceHost.documents.delete_merged_assets(deleted_ids)
    if host.document_id in deleted_ids:
        MergedAsset.audit_logs.delete([host], session.user.id)


@router.delete("")
def delete_hosts(
    session: UserSession = Depends(session),
    body: dict = Depends(body_from_query),
    filters: InventoryFilter = Depends(QueryInventoryFilter()),
) -> None:
    check_host_entitlements(session)

    client = HostClient(organization_ids=session.organization_ids, filters=filters)
    hosts = client.scan(body)
    check_org = cached_permissions_checker(
        session, AssetInventoryPermission.DELETE_ASSET
    )

    # We need to load the hosts into memory before deleting them to be able to create audit logs
    try:
        for chunk in chunks(hosts, 1000):
            to_delete = [h for h in chunk if check_org(h.metadata.organization_id)]
            deleted_ids = MergedAsset.documents.delete_bulk(to_delete)
            SourceHost.documents.delete_merged_assets(deleted_ids)
            deleted_hosts = (h for h in to_delete if h.document_id in deleted_ids)
            MergedAsset.audit_logs.delete(deleted_hosts, session.user.id)
    except Exception:
        # If we fail to delete some hosts, we need to close the generator (delete the es scroll)
        hosts.close()
        raise


def validate_for_merge(
    host_merge: HostMerge,
    session: UserSession = Depends(session),
):
    """Validate that the hosts exist."""
    check_host_entitlements(session)
    hosts = MergedAsset.documents.get_by_ids(host_merge.ids)

    if len(hosts) != len(set(host_merge.ids)):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Hosts not found"
        )

    for host in hosts:
        if host.metadata.organization_id != str(session.organization.id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Merging across organizations is not permitted",
            )
    return hosts


@router.post("/merge/review")
def merge_review(
    session: UserSession = Depends(session),
    merged_assets: list[MergedAsset] = Depends(validate_for_merge),
) -> Host:
    org = Organization.objects.get(id=session.organization.id)
    merged_asset_ids = [m.id for m in merged_assets]
    merged_asset = merge_service.manually_merge_preview(org, merged_asset_ids)
    config_manager = OrganizationIntegrationStateService()
    return Host.from_dataclass(merged_asset, config_manager=config_manager)


@router.post("/merge")
def merge(
    session: UserSession = Depends(session),
    merged_assets: list[MergedAsset] = Depends(validate_for_merge),
) -> Host:
    org = Organization.objects.get(id=session.organization.id)
    merged_asset_ids = [m.id for m in merged_assets]
    merged_asset = merge_service.manually_merge(org, merged_asset_ids)
    MergedAsset.audit_logs.update(
        merged_asset, session.user.id, "manually_merged", False, True
    )
    config_manager = OrganizationIntegrationStateService()
    return Host.from_dataclass(merged_asset, config_manager=config_manager)


def validate_for_unmerge(
    unmerged_asset: HostUnmerge,
    merged_asset: MergedAsset = Depends(validate_host),
):
    if not merged_asset.metadata.manually_merged:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Unmerging a non-manually merged asset is not permitted",
        )
    return merged_asset


@router.post("/{host_id}/unmerge/review")
def unmerge_review(
    session: UserSession = Depends(session),
    merged_asset: MergedAsset = Depends(validate_for_unmerge),
) -> Page[Host]:
    org = Organization.objects.get(id=session.organization.id)
    merged_assets = merge_service.manually_unmerge_preview(org, merged_asset.id)

    config_manager = OrganizationIntegrationStateService()
    converter = partial(Host.from_dataclass, config_manager=config_manager)
    return Page(
        items=[converter(asset) for asset in merged_assets],
        total=len(merged_assets),
        next_cursor=None,
        prev_cursor=None,
        limit=100,
    )


@router.post("/{host_id}/unmerge")
def unmerge(
    session: UserSession = Depends(session),
    merged_asset: MergedAsset = Depends(validate_for_unmerge),
) -> Page[Host]:
    org = Organization.objects.get(id=session.organization.id)
    merged_assets = merge_service.manually_unmerge(org, merged_asset.id)

    config_manager = OrganizationIntegrationStateService()
    converter = partial(Host.from_dataclass, config_manager=config_manager)
    return Page(
        items=[converter(asset) for asset in merged_assets],
        total=len(merged_assets),
        next_cursor=None,
        prev_cursor=None,
        limit=100,
    )
