from criticalstart.auth.v2.integrations.fastapi import UserSession
from fastapi import APIRouter, Depends

from apps.api.configuration import OrganizationIntegrationStateService
from apps.api.dependencies import session
from apps.api.entitlements import check_configuration_entitlements
from apps.api.v1.schemas.configuration import ConfigurationState

router = APIRouter(
    prefix="/configuration",
    tags=["configuration"],
)


@router.get("/state", response_model=ConfigurationState)
def get_configuration_state(session: UserSession = Depends(session)):
    check_configuration_entitlements(session)

    config_service = OrganizationIntegrationStateService()
    return config_service.get_configuration_state(session.organization.id)
