from typing import Sequence

from criticalstart.auth.v2.integrations.fastapi import UserSession
from fastapi import APIRouter, Depends, Query
from fastapi.exceptions import HTTPException

from apps.api.dependencies import session
from apps.api.entitlements import check_filter_entitlements
from apps.api.v1.schemas.filter import QueryInventoryFilter
from apps.api.v1.schemas.stat import AvailableStats, Stats
from apps.assets.models.merged_source_asset import (
    AssetType,
)
from apps.assets.search.host import InventoryFilter
from apps.stats.base import StatCategory, stats

HOST_TYPE = AssetType.HOST.value

router = APIRouter(
    prefix="/stats",
    tags=["stats"],
)


def validate_types(category: StatCategory, types: Sequence[str]):
    for typ in types:
        if not stats.get_stat(category, typ):
            raise HTTPException(422, "Invalid type")


@router.get("/inventory/available")
def available_inventory_stats() -> AvailableStats:
    return stats.available(StatCategory.INVENTORY)


@router.get("/hosts/available")
def available_host_stats() -> AvailableStats:
    return stats.available(StatCategory.HOST)


@router.get("/inventory")
def get_inventory_stats(
    session: UserSession = Depends(session),
    types: Sequence[str] = Query(default=[], min_length=1),
    filters: InventoryFilter = Depends(QueryInventoryFilter()),
) -> Stats:
    category = StatCategory.INVENTORY
    validate_types(category, types)
    return stats.get_stats(category, types, session.organization_ids, filters)


@router.get("/hosts")
def get_hosts_stats(
    session: UserSession = Depends(session),
    types: Sequence[str] = Query(default=[], min_length=1),
    filters: InventoryFilter = Depends(QueryInventoryFilter()),
) -> Stats:
    check_filter_entitlements(session, filters)
    category = StatCategory.HOST
    validate_types(category, types)
    return stats.get_stats(category, types, session.organization_ids, filters)
