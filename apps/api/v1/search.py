from typing import Callable, Dict, Sequence
from typing import Optional as OptionalTyp

from pyparsing import (
    Group,
    Keyword,
    OneOrMore,
    Optional,
    ParseException,
    QuotedString,
    Word,
    ZeroOrMore,
    alphas,
    printables,
)


def compile_simple_kql(
    query,
    identifier_transformer: OptionalTyp[Callable] = None,
    in_clause_transformer: OptionalTyp[Callable] = None,
) -> Dict | None:
    parsed = _parse_simple_kql(query)
    if parsed is None:
        return None

    identity = lambda x: x
    transform = identifier_transformer or identity

    must = []
    must_not = []
    for expression in parsed:
        invert = expression.get("not") == "not"
        identifier = expression.get("identifier")
        operator = expression.get("operator")
        value = expression.get("value")
        values = expression.get("values", [])

        if operator == ":":
            if value == "*":
                clause = {"exists": {"field": _drop_keyword(transform(identifier))}}
            else:
                clause = {"match": {transform(identifier): value}}
            if invert:
                must_not.append(clause)
            else:
                must.append(clause)

        elif operator == "<":
            es_operator = "lt" if not invert else "gte"
            must.append({"range": {transform(identifier): {es_operator: value}}})
        elif operator == ">":
            es_operator = "gt" if not invert else "lte"
            must.append({"range": {transform(identifier): {es_operator: value}}})
        elif operator == "in":
            should_queries = []
            for value in values:
                value_identifier, value = (
                    in_clause_transformer(identifier, value)
                    if in_clause_transformer
                    else (identifier, value)
                )
                should_queries.append({"match": {transform(value_identifier): value}})

            clause = {"bool": {"minimum_should_match": 1, "should": should_queries}}
            if invert:
                must_not.append(clause)
            else:
                must.append(clause)

    body = {"query": {"bool": {}}}
    if must:
        body["query"]["bool"]["filter"] = must

    if must_not:
        body["query"]["bool"]["must_not"] = must_not

    return body


def simple_query(query: str, fields: Sequence[str]):
    return {
        "query": {
            "bool": {
                "filter": [
                    {
                        "query_string": {
                            "query": query,
                            "fields": fields,
                            "default_operator": "and",
                            "analyze_wildcard": True,
                            "allow_leading_wildcard": True,
                        }
                    },
                ],
            }
        }
    }


def add_filter_clause_for_condition(body, clause, condition):
    query = body.setdefault("query", {})
    query.setdefault("bool", {})
    query["bool"].setdefault("filter", [])
    required_index = -1
    for i, filter in enumerate(query["bool"]["filter"]):
        if "bool" in filter:
            if condition in filter["bool"]:
                required_index = i
                break
    if required_index == -1:
        query["bool"]["filter"].append({"bool": {condition: []}})
        required_index = len(query["bool"]["filter"]) - 1
    query["bool"]["filter"][required_index]["bool"][condition].append(clause)
    return body


def add_must_clause(body, clause):
    return add_filter_clause_for_condition(body, clause, "must")


def add_must_not_clause(body, clause):
    return add_filter_clause_for_condition(body, clause, "must_not")


def _parse_simple_kql(query):
    """
    This syntax is derived from KQL.
    https://www.elastic.co/guide/en/kibana/current/kuery-query.html
    For simplicity it does not support:
    * parentheses
    * escaped characters outside quotes
    * multiple unquoted search words
    * and/or
    More support can be added later if necessary.
    """
    reserved = '":<>*()'
    invert = Keyword("not").set_results_name("not")
    match_operator = Word(":").set_results_name("operator")
    compare_operator = (Keyword("<") | Keyword(">")).set_results_name("operator")
    in_operator = Keyword("in").set_results_name("operator")
    identifier = Word(alphas + "._").set_results_name("identifier")
    value = QuotedString(quoteChar='"', escChar="\\") | Word(
        printables, excludeChars=reserved
    )
    match_value = (value | Keyword("*")).set_results_name("value")
    compare_value = value.set_results_name("value")
    match_values = (
        Word("(")
        + ZeroOrMore(
            value.set_results_name("values", list_all_matches=True) + Keyword(",")
        )
        + value.set_results_name("values", list_all_matches=True)
        + Word(")")
    )
    match_expression = identifier + match_operator + match_value
    in_expression = identifier + in_operator + match_values
    compare_expression = identifier + compare_operator + compare_value
    expression = Optional(invert) + (
        match_expression | compare_expression | in_expression
    )
    parser = OneOrMore(Group(expression))
    try:
        parsed = parser.parse_string(query)
    except ParseException:
        parsed = None

    return parsed


def _drop_keyword(identifier):
    """
    Remove the keyword suffix from an identifier.
    """
    if identifier.endswith(".keyword"):
        return identifier[: -len(".keyword")]
    return identifier  # pragma: no cover
