import json
from unittest.mock import patch

import responses
from criticalstart.service_bus import Message, notifications
from django.conf import settings
from django.core.management import call_command
from django.test import TransactionTestCase, override_settings

from apps.assets.models import IdentificationRule, MergedAsset, SyncTask
from apps.assets.services import integration_response_staging
from apps.assets.tasks import invoke_fetch_assets
from apps.assets.tests.es_base import ESCaseMixin
from apps.integrations.models import Integration
from apps.service_bus.v1.schemas import (
    IntegrationInvokeActionCommand,
    IntegrationInvokeCommandResponse,
)
from apps.tests.base import BaseCaseMixin
from factories.integration import IntegrationFactory


@override_settings(CELERY_TASK_ALWAYS_EAGER=True, CELERY_TASK_EAGER_PROPAGATES=False)
class IntegrationTests(ESCaseMixin, BaseCaseMixin, TransactionTestCase):
    def setUp(self) -> None:
        super().setUp()

        # Use test exchange to avoid conflicts with local environment
        self.add_patch(
            patch("criticalstart.service_bus.service_bus.EXCHANGE", "service_bus_test")
        )

        # Use test client ids to avoid conflicts with local environment
        av_sb_client_id = "asset_inventory_test"
        dc_sb_client_id = "data_connectors_test"
        self.command = f"{dc_sb_client_id}.integration_invoke"
        self.add_patch(
            patch(
                "apps.service_bus.v1.handlers.COMMAND_INTEGRATION_INVOKE_ACTION",
                self.command,
            )
        )

        from criticalstart.service_bus import ServiceBus

        from apps.service_bus.v1.handlers import init_service_bus

        self.data_connectors_service_bus = ServiceBus(client_id=dc_sb_client_id)
        self.asset_inventory_service_bus = init_service_bus(client_id=av_sb_client_id)

        # patch the service bus to use the test service bus
        self.add_patch(
            patch("core.service_bus.service_bus", self.asset_inventory_service_bus)
        )

        # patch the refresh period so the merge task completes quickly
        # Note: Not using the ESCaseMixin because it's not needed for this test
        self.add_patch(
            patch(
                "apps.assets.models.merged_asset.MergedAssetElasticSearchManager.REFRESH",
                "true",
            )
        )

        patcher = patch(
            "apps.assets.services.merging.merge_service.MergeService.missing_rules_from_db"
        )
        mock = patcher.start()
        mock.return_value = [], []
        self.addCleanup(patcher.stop)

        patcher_code = patch(
            "apps.assets.services.merging.merge_service.MergeService.missing_rules_from_code"
        )
        mock_code = patcher_code.start()
        mock_code.return_value = [], []
        self.addCleanup(patcher_code.stop)

    def tearDown(self) -> None:
        from criticalstart.service_bus import ServiceBus

        for instance in ServiceBus._instances.values():
            instance.reset()
            instance.connection.release()
            instance.producer_connection.release()

        ServiceBus._instances = {}

        # Call super() last to ensure resources needed by
        # subclass tearDown are not prematurely cleaned up.
        super().tearDown()

    def add_patch(self, patcher):
        patcher.start()
        self.addCleanup(patcher.stop)

    @responses.activate
    @patch("apps.assets.tasks.get_result_content")
    def test_service_bus_invoke_return_type_success(self, mock_get_result_content):
        enabled_integration_id = "cdff870c-4551-4cb5-99f4-979d597d2bfd"
        responses.get(
            f"{settings.CONNECTORS_SERVICE_URL}/internal/api/v1/integrations",
            json={
                "items": [
                    {
                        "id": enabled_integration_id,
                        "technology_id": "defender_atp",
                        "version_id": "v1",
                        "organization_id": "50ec5e1d-061d-442d-aa8d-68c8b04929db",
                        "name": "",
                        "category_id": "endpoint_security",
                        "category_name": "Endpoint Security",
                        "technology_name": "Microsoft Defender for Endpoint",
                        "enabled_actions": ["host_sync"],
                        "enabled": True,
                        "vulnerability_coverage_mode": "n/a",
                        "endpoint_coverage_mode": "n/a",
                    },
                    {
                        "id": "abff870c-4551-4cb5-99f4-979d597d2ea3",
                        "technology_id": "defender_atp",
                        "version_id": "v1",
                        "organization_id": "50ec5e1d-061d-442d-aa8d-68c8b04929db",
                        "name": "",
                        "category_id": "endpoint_security",
                        "category_name": "Endpoint Security",
                        "technology_name": "Microsoft Defender for Endpoint",
                        "enabled_actions": [],  # sync_hosts is not in enabled_actions
                        "enabled": True,
                        "vulnerability_coverage_mode": "n/a",
                        "endpoint_coverage_mode": "n/a",
                    },
                ]
            },
        )

        asset = {
            "source_id": "microsoft_mde_1",
            "hostname": "WIN-123456",
            "group_names": [],
            "fqdns": [],
            "os": {
                "host_type": "server",
                "family": "windows",
                "name": "Windows Server 2019 Datacenter",
            },
            "ip_addresses": ["************"],
            "mac_addresses": [],
            "internet_exposure": "unknown",
            "aad_id": None,
            "criticality": None,
            "owners": [{"name": "test", "email": "<EMAIL>"}],
            "last_seen": "2021-08-05T18:00:00Z",
            "extra_unexpected_fields": {"test": "ensure extra fields are ignored"},
        }

        asset_2 = asset.copy()
        asset_2["source_id"] = "microsoft_mde_2"
        asset_2["hostname"] = "WIN-123457"

        # Mock the return value of get_result_content
        mock_get_result_content.return_value = b"\n".join(
            [
                json.dumps(asset).encode("utf-8"),
                json.dumps(asset_2).encode("utf-8"),
            ]
        )

        IdentificationRule.objects.create(asset_type="host")

        correlation_id = None

        def integration_invoke_command_handler(message: Message) -> None:
            response = IntegrationInvokeCommandResponse(
                status="SUCCESS",
                artifact_keys=["test_result_id_1", "test_result_id_2"],
                return_type="Host",
            )
            self.data_connectors_service_bus.send_command_response(
                command=self.command,
                command_response_body=response,
                correlation_id=message.correlation_id,
                sender_id=message.sender_id,
            )

            nonlocal correlation_id
            correlation_id = message.correlation_id

            message.acknowledge()

        self.invoke_and_consume(integration_invoke_command_handler)

        key = integration_response_staging.build_key(
            organization_id="50ec5e1d-061d-442d-aa8d-68c8b04929db",
            technology_id="defender_atp",
            integration_id=enabled_integration_id,
            correlation_id=correlation_id,
        )
        raw_assets = integration_response_staging.read_raw(key)
        self.assertEqual(len(raw_assets), 2)

        task = SyncTask.objects.get(correlation_id=correlation_id)
        self.assertEqual(task.status, SyncTask.Status.COMPLETED)

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 2)
        self.assertEqual(
            sorted([a.merged_data.hostname for a in merged_assets]),
            ["win-123456", "win-123457"],
        )

    @responses.activate
    @patch("apps.assets.tasks.get_result_content")
    def test_service_bus_invoke_return_type_success_convert_failure(
        self, mock_get_result_content
    ):
        enabled_integration_id = "cdff870c-4551-4cb5-99f4-979d597d2bfd"
        responses.get(
            f"{settings.CONNECTORS_SERVICE_URL}/internal/api/v1/integrations",
            json={
                "items": [
                    {
                        "id": enabled_integration_id,
                        "technology_id": "defender_atp",
                        "version_id": "v1",
                        "organization_id": "50ec5e1d-061d-442d-aa8d-68c8b04929db",
                        "name": "",
                        "category_id": "endpoint_security",
                        "category_name": "Endpoint Security",
                        "technology_name": "Microsoft Defender for Endpoint",
                        "enabled_actions": ["host_sync"],
                        "enabled": True,
                        "vulnerability_coverage_mode": "n/a",
                        "endpoint_coverage_mode": "ignore",
                    },
                    {
                        "id": "abff870c-4551-4cb5-99f4-979d597d2ea3",
                        "technology_id": "defender_atp",
                        "version_id": "v1",
                        "organization_id": "50ec5e1d-061d-442d-aa8d-68c8b04929db",
                        "name": "",
                        "category_id": "endpoint_security",
                        "category_name": "Endpoint Security",
                        "technology_name": "Microsoft Defender for Endpoint",
                        "enabled_actions": [],  # sync_hosts is not in enabled_actions
                        "enabled": True,
                        "vulnerability_coverage_mode": "n/a",
                        "endpoint_coverage_mode": "ignore",
                    },
                ]
            },
        )

        asset = {
            "source_id": "microsoft_mde_1",
            "hostname": "WIN-123456",
            "group_names": [],
            "fqdns": [],
            "os": {
                "host_type": "server",
                "family": "windows",
                "name": "Windows Server 2019 Datacenter",
            },
            "ip_addresses": ["************"],
            "mac_addresses": [],
            "internet_exposure": "unknown",
            "aad_id": None,
            "criticality": None,
            "owners": [],
            "last_seen": "2021-08-05T18:00:00Z",
        }

        asset_2 = asset.copy()
        asset_2["source_id"] = "microsoft_mde_2"
        asset_2["hostname"] = "WIN-123457"
        asset_2.pop("os")  # Cause a failure by removing the os field.

        # Mock the return value of get_result_content
        mock_get_result_content.return_value = b"\n".join(
            [
                json.dumps(asset).encode("utf-8"),
                json.dumps(asset_2).encode("utf-8"),
            ]
        )

        IdentificationRule.objects.create(asset_type="host")

        correlation_id = None

        def integration_invoke_command_handler(message: Message) -> None:
            response = IntegrationInvokeCommandResponse(
                status="SUCCESS",
                artifact_keys=["test_result_id_1", "test_result_id_2"],
                return_type="Host",
            )
            self.data_connectors_service_bus.send_command_response(
                command=self.command,
                command_response_body=response,
                correlation_id=message.correlation_id,
                sender_id=message.sender_id,
            )

            nonlocal correlation_id
            correlation_id = message.correlation_id

            message.acknowledge()

        self.invoke_and_consume(integration_invoke_command_handler)

        key = integration_response_staging.build_key(
            organization_id="50ec5e1d-061d-442d-aa8d-68c8b04929db",
            technology_id="defender_atp",
            integration_id=enabled_integration_id,
            correlation_id=correlation_id,
        )
        raw_assets = integration_response_staging.read_raw(key)
        self.assertEqual(len(raw_assets), 2)

        task = SyncTask.objects.get(correlation_id=correlation_id)
        self.assertEqual(task.status, SyncTask.Status.COMPLETED)

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertEqual(
            [a.merged_data.hostname for a in merged_assets],
            ["win-123456"],
        )

    @responses.activate
    def test_service_bus_invoke_return_type_success_disabled(self):
        responses.get(
            f"{settings.CONNECTORS_SERVICE_URL}/internal/api/v1/integrations",
            json={
                "items": [
                    {
                        "id": "abff870c-4551-4cb5-99f4-979d597d2ea3",
                        "technology_id": "defender_atp",
                        "version_id": "v1",
                        "organization_id": "50ec5e1d-061d-442d-aa8d-68c8b04929db",
                        "name": "Test Integration Name",
                        "category_id": "endpoint_security",
                        "category_name": "Endpoint Security",
                        "technology_name": "Microsoft Defender for Endpoint",
                        "enabled_actions": ["host_sync"],
                        "enabled": False,
                        "vulnerability_coverage_mode": "n/a",
                        "endpoint_coverage_mode": "ignore",
                    },
                ]
            },
        )
        invoke_fetch_assets.delay()
        self.assertFalse(SyncTask.objects.exists())

    @responses.activate
    def test_service_bus_invoke_response_failed(self):
        enabled_integration_id = "cdff870c-4551-4cb5-99f4-979d597d2bfd"
        responses.get(
            f"{settings.CONNECTORS_SERVICE_URL}/internal/api/v1/integrations",
            json={
                "items": [
                    {
                        "id": enabled_integration_id,
                        "technology_id": "defender_atp",
                        "version_id": "v1",
                        "organization_id": "50ec5e1d-061d-442d-aa8d-68c8b04929db",
                        "name": "",
                        "category_id": "endpoint_security",
                        "category_name": "Endpoint Security",
                        "technology_name": "Microsoft Defender for Endpoint",
                        "enabled_actions": ["host_sync"],
                        "enabled": True,
                        "vulnerability_coverage_mode": "n/a",
                        "endpoint_coverage_mode": "ignore",
                    },
                ]
            },
        )

        call_command("sync_merging_rules")

        correlation_id = None

        def integration_invoke_command_handler(message: Message) -> None:
            response = IntegrationInvokeCommandResponse(
                status="FAILURE",
                artifact_keys=None,
            )
            self.data_connectors_service_bus.send_command_response(
                command=self.command,
                command_response_body=response,
                correlation_id=message.correlation_id,
                sender_id=message.sender_id,
            )

            nonlocal correlation_id
            correlation_id = message.correlation_id

            message.acknowledge()

        with self.assertLogs(logger="core.celery", level="ERROR"):
            self.invoke_and_consume(integration_invoke_command_handler)

        task = SyncTask.objects.get(correlation_id=correlation_id)
        self.assertEqual(task.status, SyncTask.Status.FAILED)

    @responses.activate
    def test_service_bus_invoke_response_unhealthy(self):
        enabled_integration_id = "cdff870c-4551-4cb5-99f4-979d597d2bfd"
        responses.get(
            f"{settings.CONNECTORS_SERVICE_URL}/internal/api/v1/integrations",
            json={
                "items": [
                    {
                        "id": enabled_integration_id,
                        "technology_id": "defender_atp",
                        "version_id": "v1",
                        "organization_id": "50ec5e1d-061d-442d-aa8d-68c8b04929db",
                        "name": "",
                        "category_id": "endpoint_security",
                        "category_name": "Endpoint Security",
                        "technology_name": "Microsoft Defender for Endpoint",
                        "enabled_actions": ["host_sync"],
                        "enabled": True,
                        "vulnerability_coverage_mode": "n/a",
                        "endpoint_coverage_mode": "ignore",
                    },
                ]
            },
        )

        call_command("sync_merging_rules")

        correlation_id = None

        def integration_invoke_command_handler(message: Message) -> None:
            response = IntegrationInvokeCommandResponse(
                status="UNHEALTHY",
                artifact_keys=None,
            )
            self.data_connectors_service_bus.send_command_response(
                command=self.command,
                command_response_body=response,
                correlation_id=message.correlation_id,
                sender_id=message.sender_id,
            )

            nonlocal correlation_id
            correlation_id = message.correlation_id

            message.acknowledge()

        with (
            self.assertNoLogs(logger="core.celery", level="ERROR"),
            self.assertLogs(logger="apps.assets.tasks", level="WARNING"),
        ):
            self.invoke_and_consume(integration_invoke_command_handler)

        task = SyncTask.objects.get(correlation_id=correlation_id)
        self.assertEqual(task.status, SyncTask.Status.FAILED)

    def invoke_and_consume(self, integration_invoke_command_handler):
        self.data_connectors_service_bus.register_command(
            self.command,
            integration_invoke_command_handler,
            IntegrationInvokeActionCommand,
        )

        invoke_fetch_assets.delay()

        # consume the message
        for _ in self.data_connectors_service_bus.consume(limit=1):
            break

        # now consume the response message sent to asset inventory
        for _ in self.asset_inventory_service_bus.consume(limit=1):
            break

    def test_integration_deleted_notification_handler(self):
        call_command("sync_merging_rules")
        instance = IntegrationFactory.create()

        notifications.IntegrationDeleted(
            object=notifications.Integration(
                id=instance.id,
                organization_id=instance.organization_id,
                account_id=instance.organization.account_id,
            )
        ).publish(key="test_key", bus=self.data_connectors_service_bus)

        # now consume the response message sent to asset inventory
        for _ in self.asset_inventory_service_bus.consume(limit=1):
            break

        with self.assertRaises(Integration.DoesNotExist):
            Integration.objects.get(id=instance.id)

    @responses.activate
    def test_integration_created_notification_handler(self):
        instance: Integration = IntegrationFactory.build()
        responses.get(
            f"{settings.CONNECTORS_SERVICE_URL}/internal/api/v1/integrations/{instance.id}",
            json={
                "id": str(instance.id),
                "technology_id": instance.technology_id,
                "version_id": "v1",
                "name": instance.name,
                "organization_id": str(instance.organization_id),
                "category_id": instance.category_id,
                "category_name": instance.category_id,
                "technology_name": instance.technology_id,
                "enabled_actions": ["host_sync"],
                "enabled": False,
                "vulnerability_coverage_mode": "n/a",
                "endpoint_coverage_mode": "ignore",
            },
        )

        notifications.IntegrationCreated(
            object=notifications.Integration(
                id=instance.id,
                organization_id=instance.organization_id,
                account_id=instance.organization.account_id,
            ),
        ).publish(key="test_key", bus=self.data_connectors_service_bus)

        # now consume the response message sent to asset inventory
        for _ in self.asset_inventory_service_bus.consume(limit=1):
            break

        integration = Integration.objects.get(id=instance.id)
        self.assertEqual(integration.enabled, False)
        self.assertEqual(integration.endpoint_coverage_mode, "ignore")
        self.assertEqual(integration.vulnerability_coverage_mode, "n/a")

    @responses.activate
    def test_integration_updated_notification_handler(self):
        instance: Integration = IntegrationFactory.create(
            vulnerability_coverage_mode="n/a",
            endpoint_coverage_mode="ignore",
        )
        responses.get(
            f"{settings.CONNECTORS_SERVICE_URL}/internal/api/v1/integrations/{instance.id}",
            json={
                "id": str(instance.id),
                "technology_id": instance.technology_id,
                "version_id": "v1",
                "name": instance.name,
                "organization_id": str(instance.organization_id),
                "category_id": instance.category_id,
                "category_name": instance.category_id,
                "technology_name": instance.technology_id,
                "enabled_actions": ["host_sync"],
                "enabled": False,
                "vulnerability_coverage_mode": "n/a",
                "endpoint_coverage_mode": "enabled",
            },
        )

        notifications.IntegrationUpdated(
            object=notifications.Integration(
                id=instance.id,
                organization_id=instance.organization_id,
                account_id=instance.organization.account_id,
            ),
        ).publish(key="test_key", bus=self.data_connectors_service_bus)

        # now consume the response message sent to asset inventory
        for _ in self.asset_inventory_service_bus.consume(limit=1):
            break

        integration = Integration.objects.get(id=instance.id)
        self.assertEqual(integration.enabled, False)
        self.assertEqual(integration.endpoint_coverage_mode, "enabled")

    @responses.activate
    @patch("apps.assets.tasks._sync_integration")
    def test_integration_updated_notification_handler_no_update(
        self, m__sync_integration
    ):
        instance: Integration = IntegrationFactory.create(
            vulnerability_coverage_mode="n/a",
            endpoint_coverage_mode="ignore",
        )
        responses.get(
            f"{settings.CONNECTORS_SERVICE_URL}/internal/api/v1/integrations/{instance.id}",
            json={
                "id": str(instance.id),
                "technology_id": instance.technology_id,
                "version_id": "v1",
                "name": instance.name,
                "organization_id": str(instance.organization_id),
                "category_id": instance.category_id,
                "category_name": instance.category_id,
                "technology_name": instance.technology_id,
                "enabled_actions": ["host_sync"],
                "enabled": instance.enabled,
                "vulnerability_coverage_mode": instance.vulnerability_coverage_mode,
                "endpoint_coverage_mode": instance.endpoint_coverage_mode,
            },
        )

        notifications.IntegrationUpdated(
            object=notifications.Integration(
                id=instance.id,
                organization_id=instance.organization_id,
                account_id=instance.organization.account_id,
            ),
        ).publish(key="test_key", bus=self.data_connectors_service_bus)

        # now consume the response message sent to asset inventory
        for _ in self.asset_inventory_service_bus.consume(limit=1):
            break

        m__sync_integration.assert_not_called()

    @responses.activate
    @patch("apps.assets.tasks.get_result_content")
    def test_integration_action_complete_notification_handler(
        self, mock_get_result_content
    ):
        call_command("sync_merging_rules")
        asset = {
            "source_id": "microsoft_mde_1",
            "hostname": "WIN-123456",
            "group_names": [],
            "fqdns": [],
            "os": {
                "host_type": "server",
                "family": "windows",
                "name": "Windows Server 2019 Datacenter",
            },
            "ip_addresses": ["************"],
            "mac_addresses": [],
            "internet_exposure": "unknown",
            "aad_id": None,
            "criticality": None,
            "owners": [],
            "last_seen": "2021-08-05T18:00:00Z",
        }

        mock_get_result_content.return_value = b"\n".join(
            [
                json.dumps(asset).encode("utf-8"),
            ]
        )

        instance: Integration = IntegrationFactory.create()
        responses.get(
            f"{settings.CONNECTORS_SERVICE_URL}/internal/api/v1/integrations/{instance.id}",
            json={
                "id": str(instance.id),
                "technology_id": instance.technology_id,
                "version_id": "v1",
                "name": instance.name,
                "organization_id": str(instance.organization_id),
                "category_id": instance.category_id,
                "category_name": instance.category_id,
                "technology_name": instance.technology_id,
                "enabled_actions": ["host_sync"],
                "enabled": True,
                "vulnerability_coverage_mode": "n/a",
                "endpoint_coverage_mode": "ignore",
            },
        )

        notifications.IntegrationActionCompleted(
            object=notifications.Integration(
                id=instance.id,
                organization_id=instance.organization_id,
                account_id=instance.organization.account_id,
            ),
            action="host_sync",
            result_keys=["test_result_id"],
            result_type="Host",
        ).publish(key="test_key", bus=self.data_connectors_service_bus)

        # now consume the response message sent to asset inventory
        for _ in self.asset_inventory_service_bus.consume(limit=1):
            break

        task = SyncTask.objects.first()
        self.assertEqual(task.status, SyncTask.Status.COMPLETED)

        merged_assets = MergedAsset.documents.search({})
        self.assertEqual(len(merged_assets), 1)
        self.assertEqual(
            [a.merged_data.hostname for a in merged_assets],
            ["win-123456"],
        )

    def test_integration_action_complete_notification_handler_invalid_action(self):
        instance: Integration = IntegrationFactory.create()

        notifications.IntegrationActionCompleted(
            object=notifications.Integration(
                id=instance.id,
                organization_id=instance.organization_id,
                account_id=instance.organization.account_id,
            ),
            action="invalid_action",
            result_keys=["test_result_id"],
            result_type="Host",
        ).publish(key="test_key", bus=self.data_connectors_service_bus)

        # now consume the response message sent to asset inventory
        for _ in self.asset_inventory_service_bus.consume(limit=1):
            break

        self.assertFalse(SyncTask.objects.exists())
