from enum import StrEnum, auto
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field


class IntegrationAction(StrEnum):
    HOST_SYNC = auto()


class IntegrationInvokeActionCommand(BaseModel):
    integration_id: UUID
    action: IntegrationAction
    action_args: dict = Field(default_factory=dict)


class IntegrationInvokeStatus(StrEnum):
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    UNHEALTHY = "UNHEALTHY"


class IntegrationInvokeCommandResponse(BaseModel):
    artifact_ids: Optional[list[str]] = None
    artifact_keys: Optional[list[str]] = None
    artifact_filesystem_url: Optional[str] = None
    status: IntegrationInvokeStatus
    return_type: Optional[str] = None
