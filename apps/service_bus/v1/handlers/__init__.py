from criticalstart.service_bus import ServiceBus, notifications

from .integration import (
    integration_action_complete_handler,
    integration_command_response_handler,
    integration_created_handler,
    integration_deleted_handler,
    integration_updated_handler,
)

COMMAND_INTEGRATION_INVOKE_ACTION = "data_connectors.integration_invoke_action"


def default_commands():
    """
    Returns a dictionary of commands and their handlers.
    NOTE: This is intentionally a function so tests can override
          the command keys and still use the default handlers.
    """

    return {
        COMMAND_INTEGRATION_INVOKE_ACTION: integration_command_response_handler,
    }


def default_notification_classes():
    return {
        notifications.IntegrationDeleted: integration_deleted_handler,
        notifications.IntegrationCreated: integration_created_handler,
        notifications.IntegrationUpdated: integration_updated_handler,
        notifications.IntegrationActionCompleted: integration_action_complete_handler,
    }


def init_service_bus(client_id=None, commands=None):
    commands = commands or default_commands()
    notification_classes = default_notification_classes()
    sb = ServiceBus(client_id=client_id)

    for command, handler in commands.items():
        sb.register_command_response(command, handler)

    for notification_class, handler in notification_classes.items():
        notification_class.subscribe(handler, bus=sb)

    return sb
