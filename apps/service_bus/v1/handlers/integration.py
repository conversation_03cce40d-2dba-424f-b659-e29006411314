from uuid import uuid4

from criticalstart.service_bus import Message, notifications

from apps.assets.tasks import (
    delete_assets_for_integration,
    stage_integration_action_complete_response,
    stage_integration_response,
    sync_integration,
)
from apps.service_bus.v1.schemas import IntegrationAction


def integration_command_response_handler(message: Message) -> None:
    """
    This function is called when a command response is received.
    """

    stage_integration_response.delay(
        correlation_id=message.correlation_id,
        response=message.body,
    )

    message.acknowledge()


def integration_deleted_handler(notification: notifications.IntegrationDeleted):
    """
    This function is called when an integration deleted notification is received.
    """
    delete_assets_for_integration.delay(integration_id=notification.object.id)


def integration_created_handler(notification: notifications.IntegrationCreated):
    sync_integration.delay(integration_id=notification.object.id)


def integration_updated_handler(notification: notifications.IntegrationUpdated):
    sync_integration.delay(integration_id=notification.object.id)


def integration_action_complete_handler(
    notification: notifications.IntegrationActionCompleted,
):
    """
    This function is called when an integration action completed notification is received.
    """
    if notification.action != IntegrationAction.HOST_SYNC:
        return

    correlation_id = f"notification-{uuid4()}"
    stage_integration_action_complete_response.delay(
        correlation_id=correlation_id,
        integration_id=notification.object.id,
        result_keys=notification.result_keys,
        result_ids=notification.result_ids,
        result_type=notification.result_type,
        result_filesystem_url=notification.result_filesystem_url,
    )
