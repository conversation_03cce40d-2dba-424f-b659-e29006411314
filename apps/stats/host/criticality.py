from apps.assets.models import AssetCriticality
from apps.assets.search.host import Host<PERSON>lient, InventoryFilter
from apps.stats.base import BaseStat, StatCategory


class HostCriticality(BaseStat):
    id = "criticality"
    label = "Asset Criticality"
    description = "Hosts by Asset Criticality."
    category = StatCategory.HOST

    def get_data(self, organization_ids, filters: InventoryFilter):
        client = HostClient(organization_ids, filters)
        body = {
            "size": 0,
            "aggs": {
                "source": {
                    "multi_terms": {
                        "terms": [
                            {
                                "field": "merged_data.criticality.keyword",
                                "missing": AssetCriticality.UNKNOWN.value,
                            },
                            {
                                "field": "overrides.criticality.keyword",
                                "missing": "missing",
                            },
                        ],
                        "size": 100,
                    }
                }
            },
        }
        response = client.search(body)

        if aggregations := response.get("aggregations"):
            sources = aggregations["source"]["buckets"]
        else:
            sources = []

        counts = {}
        for s in sources:
            merged_criticality, override = [k.lower() for k in s["key"]]
            key = override if override != "missing" else merged_criticality
            count = s["doc_count"]
            counts[key] = counts.get(key, 0) + count

        totals = []
        for criticality in AssetCriticality:
            if filters.criticality:
                if criticality not in filters.criticality:
                    continue

            count = counts.get(criticality, 0)
            totals.append(
                {
                    "id": criticality,
                    "value": count,
                    "name": AssetCriticality.label(criticality),
                }
            )

        return {"totals": totals}
