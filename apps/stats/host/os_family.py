from apps.assets.models.merged_source_asset import OsFamily
from apps.assets.search.host import HostClient, InventoryFilter
from apps.stats.base import BaseStat, StatCategory


class HostOsFamily(BaseStat):
    id = "os_family"
    label = "OS Distribution"
    description = "Hosts by operating system family."
    category = StatCategory.HOST

    def get_data(self, organization_ids, filters: InventoryFilter):
        client = HostClient(organization_ids, filters)
        body = {
            "size": 0,
            "aggs": {
                "source": {
                    "terms": {
                        "field": "merged_data.os.family.keyword",
                        "missing": "unknown",
                    }
                }
            },
        }
        response = client.search(body)
        if aggregations := response.get("aggregations"):
            sources = aggregations["source"]["buckets"]
        else:
            sources = []
        totals = []

        counts = {}
        for s in sources:
            key = s["key"].lower()
            count = s["doc_count"]
            counts[key] = count

        for os, label in OsFamily.labels().items():
            key = os.value

            if filters.os_family:
                if os.value not in filters.os_family:
                    continue

            count = counts.get(key, 0)
            totals.append(
                {
                    "id": key,
                    "value": count,
                    "name": label,
                }
            )

        return {"totals": totals}
