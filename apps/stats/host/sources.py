from apps.assets.search.host import HostClient
from apps.integrations.utils import get_external_technology_ids, get_technology_name
from apps.stats.base import BaseStat, StatCategory


class HostSources(BaseStat):
    id = "sources"
    label = "Asset Sources"
    description = "Hosts by source discovery method."
    category = StatCategory.HOST

    def get_data(self, organization_ids, filters):
        client = HostClient(organization_ids, filters)
        body = {
            "size": 0,
            "aggs": {
                technology_id: {
                    "value_count": {
                        "field": f"source_data.{technology_id}.metadata.asset.source_id.keyword"
                    }
                }
                for technology_id in get_external_technology_ids()
            },
        }
        response = client.search(body)

        totals = [
            {
                "id": technology_id,
                "value": value,
                "name": get_technology_name(technology_id),
            }
            for technology_id, s in response.get("aggregations", {}).items()
            if (value := s["value"])
        ]

        return {"totals": totals}
