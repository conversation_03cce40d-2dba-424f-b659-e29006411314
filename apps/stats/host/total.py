from apps.assets.search.host import HostClient
from apps.stats.base import BaseStat, StatCategory


class HostTotal(BaseStat):
    id = "total"
    label = "Total Unique Hosts"
    description = "Hosts count."
    category = StatCategory.HOST

    def get_data(self, organization_ids, filters):
        client = HostClient(organization_ids, filters)
        body = {
            "size": 0,
            "track_total_hits": True,
        }
        response = client.search(body)
        total = response["hits"]["total"]["value"]
        return {"total": total}
