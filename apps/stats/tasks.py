import logging

from celery import shared_task

from apps.accounts.models import Organization
from apps.assets.models import CoverageCategory
from apps.assets.search.host import InventoryFilter
from apps.stats.inventory.endpoint_gaps import InventoryEndpointGaps
from apps.stats.models import PersistentDailyCoverageGaps
from apps.stats.models.persistent_stats import PersistentStat

logger = logging.getLogger(__name__)


@shared_task
def record_endpoint_gaps():
    for organization in Organization.objects.all():
        record_endpoint_gaps_for.delay(organization.id)


@shared_task
def record_endpoint_gaps_for(organization_id: str):
    # Confirm organization is valid
    Organization.objects.get(id=organization_id)
    stat = InventoryEndpointGaps.calculate_persistent_stat(
        [organization_id], filters=None
    )
    PersistentStat.documents.create(stat)


@shared_task
def record_daily_coverage_gaps():
    for organization in Organization.objects.all():
        record_daily_coverage_gaps_for.delay(organization.id)


@shared_task
def record_daily_coverage_gaps_for(organization_id: str):
    # Confirm organization is valid
    Organization.objects.get(id=organization_id)

    categories_to_record = [
        CoverageCategory.ENDPOINT_SECURITY,
        CoverageCategory.VULNERABILITY_MANAGEMENT,
    ]
    for coverage_category in categories_to_record:
        stats = PersistentDailyCoverageGaps.documents.calculate_persistent_stats(
            organization_id, InventoryFilter(), coverage_category
        )
        PersistentDailyCoverageGaps.documents.upsert(stats)
