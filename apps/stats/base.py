import logging
from abc import ABCMeta, abstractmethod
from enum import Enum
from typing import Any, Sequence, Union
from uuid import UUID

logger = logging.getLogger(__name__)


class StatMeta(ABCMeta):
    def __new__(cls, clsname, bases, attrs):
        newclass = super().__new__(cls, clsname, bases, attrs)
        if clsname != "BaseStat":
            cls.register_stat(newclass)
        return newclass

    @staticmethod
    def register_stat(cls):
        """
        Automatically register the stat type.
        """
        if not hasattr(cls, "id") or not cls.id:
            raise SyntaxError("Stat class must define a unique id")  # pragma: no cover
        if not hasattr(cls, "description") or not cls.description:
            raise SyntaxError(
                "Stat class must define a description"
            )  # pragma: no cover
        if not hasattr(cls, "label") or not cls.label:
            raise SyntaxError("Stat class must define a label")  # pragma: no cover
        if not hasattr(cls, "category") or not cls.category in StatCategory:
            raise SyntaxError("Stat class must define a category")  # pragma: no cover

        if cls.id in Stats._stat_registry:
            raise SyntaxError(
                f'Aggregator "{cls.id}" is already registered'
            )  # pragma: no cover

        category = Stats._stat_registry.setdefault(cls.category.value, {})
        category[cls.id] = cls()


class StatCategory(Enum):
    """Stats are grouped by input arguments"""

    HOST = "host"
    INVENTORY = "inventory"


class BaseStat(metaclass=StatMeta):
    """
    BaseStat is an entry point to fetch any type of stat.
    """

    id = ""
    description = ""
    label = ""
    category = None

    @abstractmethod
    def get_data(self, organization_ids, filters: Any):
        pass


class Stats:
    _stat_registry = {}

    def available(self, category: StatCategory):
        stat_types = self._stat_registry[category.value].values()
        return list(s for s in stat_types if s.category == category)

    def get_stat(self, category: StatCategory, name: str):
        return self._stat_registry[category.value].get(name)

    def get_stats(
        self,
        category: StatCategory,
        types: Sequence[str],
        organization_ids: Sequence[Union[str, UUID]],
        filters: Any,
    ):
        result = {}
        stat_types = (
            s for s in self._stat_registry[category.value].values() if s.id in types
        )
        for stat in stat_types:
            try:
                organization_ids = [str(org_id) for org_id in organization_ids]
                result[stat.id] = stat.get_data(organization_ids, filters)
            except Exception as err:
                logger.error(
                    "Failed to run stats",
                    exc_info=err,
                    extra={"stat_id": stat.id},
                )
                continue
        return result


stats = Stats()
