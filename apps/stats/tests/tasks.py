from unittest.mock import patch

from django.test import override_settings
from isodate import parse_date, parse_datetime

from apps.accounts.models import Organization
from apps.api.v1.search import add_must_clause
from apps.assets.models import AssetCriticality, CoverageCategory, MergedAsset, OsFamily
from apps.assets.tests.es_base import ESCaseMixin
from apps.stats import record_endpoint_gaps
from apps.stats.models import PersistentDailyCoverageGaps
from apps.stats.models.persistent_stats import PersistentStat
from apps.stats.tasks import record_daily_coverage_gaps
from apps.tests.base import BaseTestCase
from factories.merged_host import MergedHostFactory


class ReconciliationEntry:
    pass


@override_settings(CELERY_TASK_ALWAYS_EAGER=True)
class RecordEndpointCoverageTestCase(ESCaseMixin, BaseTestCase):
    es_fixtures = ["merged_assets.json"]

    def test_record_endpoint_gaps(self):
        record_endpoint_gaps()

        org_id_1 = str(self.organization.id)
        org_id_2 = str(Organization.objects.get(alias="test_organization2").id)

        body = {}
        add_must_clause(body, {"term": {"metadata.stat.type": "endpoint_gaps"}})
        response = PersistentStat.documents.search_raw(body)
        stats = [s["_source"] for s in response["hits"]["hits"]]
        org_stats_1 = [s for s in stats if s["metadata"]["organization_id"] == org_id_1]
        org_stats_2 = [s for s in stats if s["metadata"]["organization_id"] == org_id_2]
        self.assertEqual(1, len(org_stats_1))
        self.assertEqual(1, len(org_stats_2))

        data = org_stats_1[0]["data"]
        self.assertDictEqual(
            {
                "total": 3,
                "windows": {"total": 1, "with_coverage": 1, "no_coverage": 0},
                "linux": {"total": 1, "with_coverage": 1, "no_coverage": 0},
                "mac": {"total": 0, "with_coverage": 0, "no_coverage": 0},
                "unknown": {"total": 1, "with_coverage": 0, "no_coverage": 1},
            },
            data,
        )


@override_settings(CELERY_TASK_ALWAYS_EAGER=True)
class RecordEndpointCoverageNoAssetsTestCase(ESCaseMixin, BaseTestCase):
    es_fixtures = []

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.create_index_patchers(
            empty=True,
            target_only="apps.assets.models.merged_asset.MergedAssetElasticSearchManager.INDEX_READ",
        )

    def test_record_endpoint_gaps_no_assets(self):
        record_endpoint_gaps()

        org_id = str(self.organization.id)

        body = {}
        add_must_clause(body, {"term": {"metadata.stat.type": "endpoint_gaps"}})
        response = PersistentStat.documents.search_raw(body)
        stats = [s["_source"] for s in response["hits"]["hits"]]
        org_stats = [s for s in stats if s["metadata"]["organization_id"] == org_id]
        self.assertEqual(1, len(org_stats))

        data = org_stats[0]["data"]
        self.assertDictEqual(
            {
                "total": 0,
                "windows": {"total": 0, "with_coverage": 0, "no_coverage": 0},
                "linux": {"total": 0, "with_coverage": 0, "no_coverage": 0},
                "mac": {"total": 0, "with_coverage": 0, "no_coverage": 0},
                "unknown": {"total": 0, "with_coverage": 0, "no_coverage": 0},
            },
            data,
        )


@override_settings(CELERY_TASK_ALWAYS_EAGER=True)
class RecordDailyCoverageGapsTestCase(ESCaseMixin, BaseTestCase):
    def create_merged_hosts_with_criteria(self):
        last_seen_values = [
            parse_datetime("2021-01-01T00:00:00Z"),
            parse_datetime("2021-01-02T00:00:00Z"),
            parse_datetime("2021-01-03T00:00:00Z"),
        ]
        os_family_values = [OsFamily.WINDOWS, OsFamily.LINUX, OsFamily.MAC]
        criticality_values = [
            AssetCriticality.TIER_1,
            AssetCriticality.TIER_2,
            AssetCriticality.TIER_3,
        ]
        technology_id_values = [
            ["defender_atp"],
            ["qualys_vmpc"],
            ["defender_atp", "qualys_vmpc"],
        ]
        values = zip(last_seen_values, os_family_values, criticality_values)
        merged_assets = []
        for last_seen, os_family, criticality in values:
            for technology_ids in technology_id_values:
                merged_asset = MergedHostFactory.build(
                    metadata__organization_id=self.organization.id,
                    technology_ids=technology_ids,
                )
                merged_asset.metadata.asset.last_seen = last_seen
                merged_asset.merged_data.os.family = os_family
                merged_asset.merged_data.criticality = criticality
                merged_assets.append(merged_asset)
        MergedAsset.documents.create_bulk(merged_assets)
        return merged_assets

    def test_record_daily_coverage_gaps(self):
        self.create_merged_hosts_with_criteria()

        record_daily_coverage_gaps()

        coverage_categories = [
            CoverageCategory.ENDPOINT_SECURITY,
            CoverageCategory.VULNERABILITY_MANAGEMENT,
        ]
        for coverage_category in coverage_categories:
            body = {}
            add_must_clause(
                body,
                {"term": {"metadata.coverage_category.keyword": coverage_category}},
            )
            stats = PersistentDailyCoverageGaps.documents.search_raw(
                body=body, size=100
            )
            self.assertEqual(9, stats["hits"]["total"]["value"])

    def test_record_daily_coverage_gaps_twice_same_day(self):
        self.create_merged_hosts_with_criteria()

        now_to_path = "apps.stats.models.daily_coverage_gaps.timezone.now"
        with patch(now_to_path) as mock_now:

            def assert_stats(created, updated):
                coverage_categories = [
                    CoverageCategory.ENDPOINT_SECURITY,
                    CoverageCategory.VULNERABILITY_MANAGEMENT,
                ]
                for coverage_category in coverage_categories:
                    body = {}
                    add_must_clause(
                        body,
                        {
                            "term": {
                                "metadata.coverage_category.keyword": coverage_category
                            }
                        },
                    )
                    stats = PersistentDailyCoverageGaps.documents.search(
                        body=body, size=100
                    )
                    stats = list(stats)

                    self.assertEqual(9, len(stats))
                    self.assertEqual(
                        [parse_date(created)] * 9,
                        [stat.metadata.created for stat in stats],
                    )
                    self.assertEqual(
                        [parse_datetime(updated)] * 9,
                        [stat.metadata.updated for stat in stats],
                    )

            mock_now.return_value = parse_datetime("2021-01-01T11:22:33Z")
            record_daily_coverage_gaps()
            assert_stats("2021-01-01", "2021-01-01T11:22:33Z")

            mock_now.return_value = parse_datetime("2021-01-01T23:59:59Z")
            record_daily_coverage_gaps()
            assert_stats("2021-01-01", "2021-01-01T23:59:59Z")
