from unittest.mock import Mock

from requests import RequestException

from apps.stats.base import StatCategory, Stats
from apps.tests.base import BaseTestCase


class StatsTestCase(BaseTestCase):
    def test_get_stats_no_except(self):
        stats = Stats()

        category = StatCategory.INVENTORY

        mock_bad_stat = Mock()
        mock_bad_stat.id = "bad_stat"
        mock_bad_stat.description = "bad_stat"
        mock_bad_stat.label = "bad_stat"
        mock_bad_stat.category = category
        mock_bad_stat.get_data.side_effect = RequestException("Bad ES request")

        mock_good_stat = Mock()
        mock_good_stat.id = "good_stat"
        mock_good_stat.description = "good_stat"
        mock_good_stat.label = "good_stat"
        mock_good_stat.category = category
        mock_good_stat.get_data.return_value = {"total": 1}

        def remove_bad_stat():
            stats._stat_registry[category.value].pop("bad_stat")

        def remove_good_stat():
            stats._stat_registry[category.value].pop("good_stat")

        stats._stat_registry[category.value]["bad_stat"] = mock_bad_stat
        self.addCleanup(remove_bad_stat)

        stats._stat_registry[category.value]["good_stat"] = mock_good_stat
        self.addCleanup(remove_good_stat)

        # Should not raise
        result = stats.get_stats(
            category, ["bad_stat", "good_stat"], "org_id", filters=None
        )
        self.assertNotIn("bad_stat", result)
        self.assertIn("good_stat", result)
