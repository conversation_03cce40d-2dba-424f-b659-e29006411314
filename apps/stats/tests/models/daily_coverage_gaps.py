from itertools import product

from criticalstart.fastapi_utils.parameters.period import Period
from django.utils import timezone
from django.utils.dateparse import parse_datetime

from apps.assets.models import AssetCriticality, CoverageCategory, MergedAsset, OsFamily
from apps.assets.search.host import InventoryFilter
from apps.assets.tests.es_base import ESCaseMixin
from apps.integrations.models.integration import Integration
from apps.stats.models.daily_coverage_gaps import DailyCoverageGapsManager
from apps.tests.base import BaseTestCase
from factories.merged_host import MergedHostFactory, MergedSourceHostFactory


class DailyCoverageGapManagerTestCase(ESCaseMixin, BaseTestCase):
    def setUp(self):
        super().setUp()

    def test_calculate_persistent_stats(self):
        merged_assets = (
            MergedHostFactory.build_batch(
                10,
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        metadata__integration__technology_id="defender_atp",
                        metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.IGNORE,
                        metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                    )
                ],
            )
            + MergedHostFactory.build_batch(
                10,
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        metadata__integration__technology_id="qualys_vmpc",
                        metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                        metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.IGNORE,
                    )
                ],
            )
            + MergedHostFactory.build_batch(
                10,
                metadata__organization_id=self.organization.id,
                source_assets=[
                    MergedSourceHostFactory(
                        metadata__integration__technology_id="defender_atp",
                        metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.IGNORE,
                        metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.ENABLED,
                    ),
                    MergedSourceHostFactory(
                        metadata__integration__technology_id="qualys_vmpc",
                        metadata__integration__vulnerability_coverage_mode=Integration.CoverageMode.ENABLED,
                        metadata__integration__endpoint_coverage_mode=Integration.CoverageMode.IGNORE,
                    ),
                ],
            )
        )

        criteria_tuples = [
            (
                parse_datetime("2021-01-01T00:00:00Z"),
                OsFamily.WINDOWS,
                AssetCriticality.TIER_1,
            ),  # 0, 3, 6, 9, 12, 15, 18, 21, 24, 27
            (
                parse_datetime("2021-01-02T00:00:00Z"),
                OsFamily.LINUX,
                AssetCriticality.TIER_2,
            ),  # 1, 4, 7, 10, 13, 16, 19, 22, 25, 28
            (
                parse_datetime("2021-01-03T00:00:00Z"),
                OsFamily.MAC,
                AssetCriticality.TIER_3,
            ),  # 2, 5, 8, 11, 14, 17, 20, 23, 26, 29
        ]
        for i, merged_asset in enumerate(merged_assets):
            merged_asset.metadata.asset.last_seen = criteria_tuples[i % 3][0]
            merged_asset.merged_data.os.family = criteria_tuples[i % 3][1]
            merged_asset.merged_data.criticality = criteria_tuples[i % 3][2]

        MergedAsset.documents.create_bulk(merged_assets)

        manager = DailyCoverageGapsManager()
        stats = manager.calculate_persistent_stats(
            self.organization.id, InventoryFilter(), CoverageCategory.ENDPOINT_SECURITY
        )
        self.assertEqual(9, len(stats))
        for i, stat in enumerate(stats):
            stat.metadata.created = stat.criteria.last_seen.date()

        # Assert there are three stats for each criteria tuple
        for criteria_tuple in criteria_tuples:
            matching_stats = [
                s
                for s in stats
                if (
                    s.criteria.last_seen == criteria_tuple[0]
                    and s.criteria.os_family == criteria_tuple[1]
                    and s.criteria.criticality == criteria_tuple[2]
                )
            ]
            self.assertEqual(3, len(matching_stats))

        manager.upsert(stats)

        stats_by_day = manager.get_stats_by_day(
            [self.organization.id],
            InventoryFilter(),
            CoverageCategory.ENDPOINT_SECURITY,
            Period(None, timezone.now()),
        )

        empty = {
            "tier0": {"total": 0, "no_coverage": 0, "with_coverage": 0},
            "tier1": {"total": 0, "no_coverage": 0, "with_coverage": 0},
            "tier2": {"total": 0, "no_coverage": 0, "with_coverage": 0},
            "tier3": {"total": 0, "no_coverage": 0, "with_coverage": 0},
            "tier4": {"total": 0, "no_coverage": 0, "with_coverage": 0},
            "unknown": {"total": 0, "no_coverage": 0, "with_coverage": 0},
        }
        total = 0
        for _, stat in stats_by_day.items():
            for _, crit_stat in stat.items():
                total += crit_stat["total"]
        self.assertEqual(30, total)
        self.assertEqual(
            stats_by_day,
            {
                "2021-01-01": {
                    **empty,
                    "tier1": {"total": 10, "no_coverage": 3, "with_coverage": 7},
                },
                "2021-01-02": {
                    **empty,
                    "tier2": {"total": 10, "no_coverage": 4, "with_coverage": 6},
                },
                "2021-01-03": {
                    **empty,
                    "tier3": {"total": 10, "no_coverage": 3, "with_coverage": 7},
                },
            },
        )

    def test_calculate_persistent_stats_no_assets(self):
        manager = DailyCoverageGapsManager()
        stats = manager.calculate_persistent_stats(
            self.organization.id, InventoryFilter(), CoverageCategory.ENDPOINT_SECURITY
        )
        self.assertEqual(0, len(stats))
        manager.upsert(stats)

        stats_by_day = manager.get_stats_by_day(
            [self.organization.id],
            InventoryFilter(),
            CoverageCategory.ENDPOINT_SECURITY,
            Period(None, timezone.now()),
        )
        self.assertEqual({}, stats_by_day)

    def test_calculate_persistent_stats_all_possible_criticality_combinations(self):
        merged_assets = []
        possible_overrides = list(AssetCriticality) + [None]
        for criticality, override in product(AssetCriticality, possible_overrides):
            merged_asset = MergedHostFactory.build(
                metadata__organization_id=self.organization.id,
                technology_ids=["defender_atp"],
            )

            last_seen = parse_datetime("2021-01-01T00:00:00Z")
            merged_asset.metadata.asset.last_seen = last_seen
            merged_asset.merged_data.os.family = OsFamily.WINDOWS
            merged_asset.merged_data.criticality = criticality
            merged_asset.overrides.criticality = override

            merged_assets.append(merged_asset)

        MergedAsset.documents.create_bulk(merged_assets)

        manager = DailyCoverageGapsManager()
        stats = manager.calculate_persistent_stats(
            self.organization.id, InventoryFilter(), CoverageCategory.ENDPOINT_SECURITY
        )
        self.assertEqual(
            len(AssetCriticality),
            len(stats),
            "Expected a stat for each criticality value because other criteria are the same",
        )
        self.assertEqual(
            [len(AssetCriticality) + 1] * len(AssetCriticality),
            [stat.data.total for stat in stats],
            "Expected one host for each criticality value and one for no override.",
        )

    def test_get_stats_for_today_criticality_filter_with_override(self):
        merged_asset = MergedHostFactory.build(
            metadata__organization_id=self.organization.id,
            technology_ids=["defender_atp"],
        )

        last_seen = parse_datetime("2021-01-01T00:00:00Z")
        merged_asset.metadata.asset.last_seen = last_seen
        merged_asset.merged_data.os.family = OsFamily.WINDOWS
        merged_asset.merged_data.criticality = AssetCriticality.TIER_1
        merged_asset.overrides.criticality = AssetCriticality.TIER_2

        MergedAsset.documents.create_bulk([merged_asset])

        manager = DailyCoverageGapsManager()

        #############################################################################
        # Test with criticality filter that should not match the asset because of the override
        filter_tier1 = InventoryFilter(criticality=[AssetCriticality.TIER_1])
        stats = manager.get_stats_for_today(
            [self.organization.id], filter_tier1, CoverageCategory.ENDPOINT_SECURITY
        )

        for criticality, stat in stats.items():
            self.assertEqual(0, stat["total"], f"Unexpected stat for {criticality}")

        #############################################################################
        # Test with criticality filter that should match the asset because of the override
        filter_tier2 = InventoryFilter(criticality=[AssetCriticality.TIER_2])
        stats = manager.get_stats_for_today(
            [self.organization.id], filter_tier2, CoverageCategory.ENDPOINT_SECURITY
        )

        for criticality, stat in stats.items():
            if criticality == AssetCriticality.TIER_2:
                self.assertEqual(1, stat["total"])
            else:
                self.assertEqual(0, stat["total"], f"Unexpected stat for {criticality}")
