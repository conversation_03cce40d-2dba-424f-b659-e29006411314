from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional

from django.utils.dateparse import parse_datetime
from django.utils.timezone import now

from apps.api.v1.search import add_must_clause
from apps.es import es_service


class StatType(Enum):
    ENDPOINT_GAPS = "endpoint_gaps"


class PersistentStatManager:
    client = es_service.es
    INDEX_WRITE = "persistent_stats-current"
    INDEX_READ = "persistent_stats-*"

    REFRESH = None

    @classmethod
    def date_histogram_latest(
        cls, body: Dict, stat_type: StatType, timerange: dict, interval: str
    ) -> Dict:
        """
        Fetch results by interval.
        e.g. if interval is 1 day then select the most recent result from
        each day matching the given time range.
        """

        body["size"] = 0
        add_must_clause(
            body,
            {
                "range": {
                    "metadata.created": timerange,
                }
            },
        )
        add_must_clause(body, {"term": {"metadata.stat.type": stat_type.value}})

        body.setdefault("aggs", {})

        min_date = timerange["gte"]
        max_date = timerange.get("lte") or "now"

        body["aggs"]["histogram"] = {
            "date_histogram": {
                "field": "metadata.created",
                "calendar_interval": interval,
                "extended_bounds": {
                    "min": min_date,
                    "max": max_date,
                },
            },
            "aggs": {
                "organizations": {
                    "terms": {
                        "field": "metadata.organization_id",
                        "size": 100,
                    },
                    "aggs": {
                        "latest": {
                            "top_hits": {
                                "size": 1,
                                "sort": {
                                    "metadata.created": "desc",
                                },
                            }
                        }
                    },
                }
            },
        }
        response = cls.search_raw(body=body)
        if aggregations := response.get("aggregations"):
            buckets = aggregations["histogram"]["buckets"]
        else:
            buckets = []

        created_to_stats = {}
        for interval in buckets:
            created = interval["key_as_string"]
            created_to_stats[created] = []
            current = created_to_stats[created]

            organizations = interval["organizations"]["buckets"]
            for organization in organizations:
                hits = organization["latest"]["hits"]["hits"]
                stat = None
                if hits:
                    stat = cls.from_doc(hits[0]["_source"])
                current.append(stat)

        return created_to_stats

    @classmethod
    def search_raw(cls, body: Dict, index: str = None, **kwargs) -> Dict:
        """
        Search for stats and return the raw OpenSearch.
        """
        return cls.client.search(
            index=index or cls.INDEX_READ,
            body=body,
            **kwargs,
        )

    @classmethod
    def create(cls, asset: "PersistentStat"):
        """
        Record a new stat.
        """
        _index = cls.INDEX_WRITE
        result = cls.client.index(
            _index,
            body=cls.to_doc(asset, created=now()),
            refresh=cls.REFRESH,
        )
        return result

    @staticmethod
    def to_doc(
        stat: "PersistentStat", created: datetime, updated: datetime = None
    ) -> Dict:
        _source = {
            "data": {
                **stat.data,
            },
            "metadata": {
                "organization_id": stat.organization_id,
                "stat": {
                    "type": stat.type.value,
                },
                "created": stat.created or created,
            },
        }

        return _source

    @staticmethod
    def from_doc(document: Dict[str, Any]) -> "PersistentStat":
        organization_id = document["metadata"]["organization_id"]
        stat_type = document["metadata"]["stat"]["type"]
        created = parse_datetime(document["metadata"]["created"])
        data = document["data"]

        return PersistentStat(
            organization_id=organization_id,
            type=StatType(stat_type),
            data=data,
            created=created,
        )


@dataclass(frozen=True)
class PersistentStat:
    organization_id: str

    type: StatType

    data: Dict[str, Any]

    created: Optional[datetime] = None

    documents = PersistentStatManager()
