import hashlib
from collections import defaultdict
from collections.abc import Iterable
from datetime import date, datetime
from typing import ClassV<PERSON>, Generator
from uuid import UUID

from criticalstart.fastapi_utils.parameters.period import Period
from django.utils import timezone
from opensearchpy import Q, Search
from opensearchpy.helpers import streaming_bulk
from pydantic import BaseModel

from apps.assets.models import AssetCriticality, CoverageCategory, MergedAsset, OsFamily
from apps.assets.search.host import InventoryFilter
from apps.es import es_service


class DailyCoverageGapsManager:
    client = es_service.es
    INDEX_WRITE = "daily_coverage_gaps-current"
    INDEX_READ = "daily_coverage_gaps-*"

    REFRESH = None

    def calculate_persistent_stats(
        self,
        organization_id,
        filters: InventoryFilter,
        coverage_category: CoverageCategory,
    ) -> list["PersistentDailyCoverageGaps"]:
        return self.__calculate_persistent_stats(
            coverage_category,
            filters,
            organization_id,
        )

    def __calculate_persistent_stats(
        self, coverage_category, filters, organization_id, organization_ids=None
    ):
        merged_asset_index = MergedAsset.documents.INDEX_READ
        search = Search(using=self.client, index=merged_asset_index)
        search = search.extra(size=0)
        # For cases when we are calculating the stats for update-to-date results, we will have
        # a list of orgs to filter by.
        if organization_ids:
            search = search.filter("terms", metadata__organization_id=organization_ids)
        else:
            search = search.filter("term", metadata__organization_id=organization_id)
        search = self._filter_for_daily_coverage_gaps(
            search, self._merged_asset_index_field_map(), filters
        )
        search.aggs.bucket(
            "last_seen",
            "date_histogram",
            field="metadata.asset.last_seen",
            calendar_interval="day",
            min_doc_count=1,
        ).bucket(
            "os_family",
            "terms",
            field="merged_data.os.family.keyword",
            size=len(OsFamily),
            min_doc_count=1,
        ).bucket(
            "all_combined",
            "terms",
            field="metadata.asset.technologies.all_combined",
            size=100,
        ).bucket(
            "criticality",
            "multi_terms",
            terms=[
                {
                    "field": "merged_data.criticality.keyword",
                    "missing": AssetCriticality.UNKNOWN.value,
                },
                {
                    "field": "overrides.criticality.keyword",
                    "missing": "missing",
                },
            ],
            size=100,
        ).bucket(
            "with_coverage",
            "filter",
            self.get_with_coverage_filter(coverage_category),
        )
        result = search.execute()

        if not result.aggregations:
            return []

        def get_data(criticality_bucket):
            with_coverage = criticality_bucket.with_coverage.doc_count
            total = criticality_bucket.doc_count
            no_coverage = total - with_coverage
            return DailyCoverageGapsData(
                with_coverage=with_coverage,
                no_coverage=no_coverage,
                total=total,
            )

        def get_criticality(criticality_bucket):
            return (
                criticality_bucket["key"][0]
                if criticality_bucket["key"][1] == "missing"
                else criticality_bucket["key"][1]
            )

        stats = defaultdict(DailyCoverageGapsData)
        for last_seen_bucket in result.aggregations.last_seen.buckets:
            for os_family_bucket in last_seen_bucket.os_family.buckets:
                for all_combined_bucket in os_family_bucket.all_combined.buckets:
                    for criticality_bucket in all_combined_bucket.criticality.buckets:
                        criteria = DailyCoverageGapsCriteria(
                            last_seen=last_seen_bucket.key_as_string,
                            os_family=os_family_bucket.key,
                            technology_ids=all_combined_bucket.key.split(","),
                            criticality=get_criticality(criticality_bucket),
                        )
                        criteria_key = criteria.model_dump_json()
                        stats[criteria_key] += get_data(criticality_bucket)

        stats = [
            PersistentDailyCoverageGaps(
                data=data,
                criteria=DailyCoverageGapsCriteria.model_validate_json(criteria_key),
                metadata=DailyCoverageGapsMetadata(
                    organization_id=organization_id,
                    coverage_category=coverage_category,
                ),
            )
            for criteria_key, data in stats.items()
        ]
        return stats

    @staticmethod
    def get_with_coverage_filter(coverage_category: CoverageCategory) -> Q:
        if coverage_category == CoverageCategory.TECHNICAL_SECURITY_CONTROL:
            categories = CoverageCategory.technical_security_controls()
        else:
            categories = [coverage_category]

        queries = [
            Q(
                "bool",
                should=[
                    Q(
                        "exists",
                        field=f"metadata.asset.technologies.{category}",
                    ),
                    InventoryFilter.gap_excluded_clause(category),
                ],
                minimum_should_match=1,
            )
            for category in categories
        ]
        if len(queries) == 1:
            return queries[0]

        # Construct a boolean query for multiple coverage categories to be used in the
        # "with_coverage" filter aggregation. This aggregation calculates:
        # - "with_coverage": The number of assets that have **any** of the specified coverage categories.
        # - "no_coverage": The number of assets that have **none** of the specified coverage categories.
        #
        # By setting `minimum_should_match=1`, the query ensures that an asset matches if it meets at least
        # one of the conditions for any of the specified categories.
        return Q("bool", should=queries, minimum_should_match=1)

    @classmethod
    def get_stats_by_day(
        cls,
        organization_ids: list[UUID],
        filters: InventoryFilter,
        coverage_category: CoverageCategory,
        period: Period,
    ) -> dict:
        """
        Fetch results by interval.
        e.g. if interval is 1 day then select the most recent result from
        each day matching the given time range.
        """
        search = Search(using=cls.client, index=cls.INDEX_READ)
        search = search.extra(size=0)
        search = search.filter(
            "terms",
            metadata__organization_id__keyword=[str(o) for o in organization_ids],
        )
        search = search.filter(
            "term", metadata__coverage_category__keyword=coverage_category
        )
        search = cls._filter_for_daily_coverage_gaps(
            search, cls._daily_coverage_gap_index_field_map(), filters
        )
        search = cls._filter_range(search, "metadata.created", period)

        search.aggs.bucket(
            "coverage_gaps_by_day",
            "date_histogram",
            field="metadata.created",
            interval="day",
            format="yyyy-MM-dd",
        ).bucket(
            "criticality",
            "terms",
            field="criteria.criticality.keyword",
        ).metric(
            "total",
            "sum",
            field="data.total",
        ).metric(
            "with_coverage",
            "sum",
            field="data.with_coverage",
        ).metric(
            "no_coverage", "sum", field="data.no_coverage"
        )

        result = search.execute()

        def empty_stats():
            return {
                str(crit): {"total": 0, "with_coverage": 0, "no_coverage": 0}
                for crit in AssetCriticality
            }

        if not result.aggregations:
            return {}

        stats_by_day = {}
        for bucket in result.aggregations.coverage_gaps_by_day.buckets:
            stats_by_day[bucket.key_as_string] = empty_stats()
            for crit_bucket in bucket.criticality.buckets:
                stats_by_day[bucket.key_as_string][crit_bucket.key] = {
                    "total": int(crit_bucket.total.value),
                    "with_coverage": int(crit_bucket.with_coverage.value),
                    "no_coverage": int(crit_bucket.no_coverage.value),
                }

        return stats_by_day

    def get_stats_for_today(
        self,
        organization_ids: list[UUID],
        filters: InventoryFilter,
        coverage_category: CoverageCategory,
    ):
        stats = self.__calculate_persistent_stats(
            coverage_category,
            filters,
            organization_ids[0],
            organization_ids=organization_ids,
        )
        stats_for_today = {
            str(crit): {"total": 0, "with_coverage": 0, "no_coverage": 0}
            for crit in AssetCriticality
        }
        for stat in stats:
            values = stats_for_today[stat.criteria.criticality]
            values["total"] += stat.data.total
            values["with_coverage"] += stat.data.with_coverage
            values["no_coverage"] += stat.data.no_coverage

        return stats_for_today

    @classmethod
    def search_raw(cls, body: dict = None, index: str = None, **kwargs) -> dict:
        """
        Search for stats and return the raw OpenSearch.
        """
        return cls.client.search(
            index=index or cls.INDEX_READ,
            body=body or {},
            **kwargs,
        )

    @classmethod
    def search(
        cls, body: dict = None, index: str = None, **kwargs
    ) -> Iterable["PersistentDailyCoverageGaps"]:
        """
        Search for stats and return the parsed results.
        """
        response = cls.search_raw(body, index=index, **kwargs)
        for hit in response["hits"]["hits"]:
            yield cls.from_doc(hit["_source"])

    @classmethod
    def upsert(cls, stats: list["PersistentDailyCoverageGaps"]):
        """
        Record a new stat.
        """
        actions = cls._construct_update_actions(stats)
        results = cls._stream_actions(actions)
        return [
            action["update"]["_id"]
            for ok, action in results
            if action["update"]["result"] == "updated"
        ]

    @staticmethod
    def to_doc(stat: "PersistentDailyCoverageGaps") -> dict:
        return stat.model_dump(mode="json")

    @staticmethod
    def from_doc(document: dict) -> "PersistentDailyCoverageGaps":
        return PersistentDailyCoverageGaps.model_validate(document)

    @staticmethod
    def _generate_document_id(stat):
        unique_string = "_".join(
            [
                str(stat.metadata.created),
                stat.criteria.model_dump_json(),
                str(stat.metadata.organization_id),
                str(stat.metadata.coverage_category),
            ]
        )
        # Generate a hash of the unique string
        unique_id = hashlib.md5(unique_string.encode()).hexdigest()
        return unique_id

    @classmethod
    def _construct_update_actions(
        cls,
        stats: list["PersistentDailyCoverageGaps"],
    ) -> Generator[dict, None, None]:
        """
        Construct update actions for existing merged assets.
        """
        now = timezone.now()
        for stat in stats:
            if not stat.metadata.created:
                stat.metadata.created = now.date()
            if not stat.metadata.updated:
                stat.metadata.updated = now

            yield {
                "_op_type": "update",
                "_index": cls.INDEX_WRITE,
                "_id": cls._generate_document_id(stat),
                "doc_as_upsert": True,
                "doc": cls.to_doc(stat),
            }

    @classmethod
    def _stream_actions(
        cls,
        actions,
        batch_size: int = 10000,
        max_retries: int = 2,
    ):
        """
        Stream a series of actions to OpenSearch.
        """
        return streaming_bulk(
            cls.client,
            actions,
            max_retries=max_retries,
            chunk_size=batch_size,
            refresh=cls.REFRESH,
        )

    @classmethod
    def _daily_coverage_gap_index_field_map(cls):
        return {
            "last_seen": lambda search, period: cls._filter_range(
                search, "criteria.last_seen", period
            ),
            "os_family": lambda search, terms: cls._filter_terms(
                search, "criteria.os_family.keyword", terms
            ),
            "criticality": lambda search, terms: cls._filter_terms(
                search, "criteria.criticality.keyword", terms
            ),
            "technology_ids": lambda search, terms: cls._filter_terms(
                search, "criteria.technology_ids.keyword", terms
            ),
        }

    @classmethod
    def _merged_asset_index_field_map(cls):
        return {
            "last_seen": lambda search, period: cls._filter_range(
                search, "metadata.asset.last_seen", period
            ),
            "os_family": lambda search, terms: cls._filter_terms(
                search, "merged_data.os.family.keyword", terms
            ),
            "criticality": lambda search, terms: cls._filter_merged_asset_criticality(
                search, terms
            ),
            "technology_ids": lambda search, terms: cls._filter_terms(
                search, "metadata.asset.technologies.all", terms
            ),
        }

    @staticmethod
    def _filter_terms(search: Search, field: str, terms: Iterable[str]):
        return search.filter("terms", **{field: terms})

    @staticmethod
    def _filter_range(search: Search, field: str, period: Period):
        return search.filter(
            "range", **{field: {"gte": period.start, "lte": period.end}}
        )

    @staticmethod
    def _filter_merged_asset_criticality(search: Search, criticality: AssetCriticality):
        return search.filter(
            Q("terms", overrides__criticality__keyword=criticality)
            | (
                Q("terms", merged_data__criticality__keyword=criticality)
                & ~Q("exists", field="overrides.criticality")
            )
        )

    @staticmethod
    def _filter_for_daily_coverage_gaps(
        search: Search,
        field_map: dict,
        filters: InventoryFilter,
    ):
        """
        Create a filter for daily coverage gaps.
        """

        if filters.last_seen:
            search = field_map["last_seen"](search, filters.last_seen)

        if filters.os_family:
            search = field_map["os_family"](search, filters.os_family)

        if filters.technology_ids:
            search = field_map["technology_ids"](search, filters.technology_ids)

        if filters.criticality:
            search = field_map["criticality"](search, filters.criticality)

        return search


class DailyCoverageGapsData(BaseModel):
    total: int = 0
    with_coverage: int = 0
    no_coverage: int = 0

    def __add__(self, other: "DailyCoverageGapsData") -> "DailyCoverageGapsData":
        return DailyCoverageGapsData(
            total=self.total + other.total,
            with_coverage=self.with_coverage + other.with_coverage,
            no_coverage=self.no_coverage + other.no_coverage,
        )


class DailyCoverageGapsCriteria(BaseModel):
    last_seen: datetime | None
    os_family: OsFamily
    technology_ids: list[str]
    criticality: AssetCriticality


class DailyCoverageGapsMetadata(BaseModel):
    organization_id: UUID
    coverage_category: CoverageCategory
    created: date | None = None
    updated: datetime | None = None


class PersistentDailyCoverageGaps(BaseModel):
    data: DailyCoverageGapsData
    criteria: DailyCoverageGapsCriteria
    metadata: DailyCoverageGapsMetadata

    documents: ClassVar[DailyCoverageGapsManager] = DailyCoverageGapsManager()
