from datetime import timedelta

from criticalstart.fastapi_utils.parameters.period import Period
from django.utils import dateparse
from django.utils.timezone import now
from opensearchpy.helpers.aggs import Bucket

from apps.assets.models import AssetCriticality, CoverageCategory
from apps.assets.search.host import InventoryFilter
from apps.stats.base import BaseStat, StatCategory
from apps.stats.models.daily_coverage_gaps import PersistentDailyCoverageGaps

DAYS = 30
DATE_FORMAT = "%m.%d"
OPENSEARCH_CONNECTION_TIMEOUT = 300


class MultiTerms(Bucket):
    name = "multi_terms"


class EndpointDailyCoverageGapsByCriticality(BaseStat):
    id = "endpoint_daily_coverage_gaps_by_criticality"
    label = "Daily Endpoint Security Gaps"
    description = "Historical hosts by endpoint security gaps."
    category = StatCategory.INVENTORY

    def get_data(self, organization_ids, filters: InventoryFilter):
        dt = now()
        stats_by_day = PersistentDailyCoverageGaps.documents.get_stats_by_day(
            organization_ids,
            filters,
            CoverageCategory.ENDPOINT_SECURITY,
            Period(dt - timedelta(days=DAYS), dt),
        )

        # Calculate an on-the-fly stat for the current merged assets.
        stats_for_today = PersistentDailyCoverageGaps.documents.get_stats_for_today(
            organization_ids,
            filters,
            CoverageCategory.ENDPOINT_SECURITY,
        )
        today_key = str(dt.date())
        stats_by_day[today_key] = stats_for_today

        # This data structure is useful for plotting or visualizing trends over time.
        #
        # Example breakdown:
        # "labels": ['09.01', '09.02', ...] → List of date strings.
        # "series": ['Total', 'Tier 0', 'Tier 1', ...] → Labels for the different data series.
        # "data": [[100, 150, 120, ...], [40, 50, 30, ...], [30, 40, 50, ...], [30, 60, 40, ...]] → Values per day.
        #
        # - "labels" contains date strings corresponding to the days being plotted.
        # - "series" contains labels like "Total" and criticality levels.
        # - "data" contains lists: the first for total counts per day, followed by counts for each criticality.
        #
        # If you were to plot this data in a graph, the X-axis would represent the dates (labels),
        # and the Y-axis would represent the values in each series (data).
        # Each series ("Total", "Tier 0", "Tier 1", ...) would have its own line or bar on the graph.

        labels = []
        criticality_values = {k: [] for k in AssetCriticality}
        total = []

        for day, stat in stats_by_day.items():
            label = dateparse.parse_datetime(day).strftime(DATE_FORMAT)
            labels.append(label)

            day_total = 0
            for criticality, values in criticality_values.items():
                values.append(stat[criticality]["no_coverage"])
                day_total += stat[criticality]["total"]
            total.append(day_total)

        return {
            "labels": labels,
            "series": ["Total", *AssetCriticality.labels().values()],
            "data": [total, *criticality_values.values()],
        }
