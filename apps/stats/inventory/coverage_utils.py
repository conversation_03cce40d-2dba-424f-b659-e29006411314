from apps.assets.models import CoverageCategory
from apps.assets.search.host import Host<PERSON>lient, InventoryFilter
from apps.integrations.utils import get_technology_name


def get_coverage_by_category(
    organization_ids, filters: InventoryFilter, coverage_category: CoverageCategory
):
    client = HostClient(organization_ids, filters)
    excluded_clause = InventoryFilter.gap_excluded_clause(coverage_category)
    body = {
        "size": 0,
        "aggs": {
            "coverage": {
                "filter": {"bool": {"must_not": [excluded_clause]}},
                "aggs": {
                    "coverage": {
                        "terms": {
                            "field": InventoryFilter.coverage_combined_field(
                                coverage_category
                            ),
                            "missing": "",
                            "min_doc_count": 1,
                            "size": 100,
                        }
                    }
                },
            },
            "excluded": {
                "filter": excluded_clause,
            },
        },
    }
    response = client.search(body)

    if aggregations := response.get("aggregations"):
        sources = aggregations["coverage"]["coverage"]["buckets"]
        excluded_count = aggregations["excluded"]["doc_count"]
    else:
        sources = []
        excluded_count = 0

    totals = []
    for s in sources:
        combined = s["key"]
        if not combined:
            combined = "missing"

        keys = combined.split(",")
        count = s["doc_count"]
        labels = []

        for key in keys:
            if key == "missing":
                labels.append("Missing")
            else:
                labels.append(get_technology_name(key))

        totals.append(
            {
                "id": keys[0],
                "ids": keys,
                "value": count,
                "name": "/".join(labels),
                "names": labels,
                "combined": None if combined == "missing" else combined,
            }
        )

    if excluded_count:
        totals.append(
            {
                "id": "excluded",
                "ids": ["excluded"],
                "value": excluded_count,
                "name": "Excluded",
                "names": ["Excluded"],
                "combined": None,
            }
        )

    return {"totals": totals}
