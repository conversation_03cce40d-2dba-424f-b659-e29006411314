from apps.assets.search.host import HostClient
from apps.stats.base import BaseStat, StatCategory


class InventoryTotals(BaseStat):
    id = "totals"
    label = "Host Reconciliation"
    description = "Hosts totals seen/unique across all asset sources."
    category = StatCategory.INVENTORY

    def get_data(self, organization_ids, filters):
        client = HostClient(organization_ids, filters)
        body = {
            "size": 0,
            "track_total_hits": True,
            "aggs": {"seen": {"sum": {"field": "metadata.asset.technologies.count"}}},
        }
        response = client.search(body)
        if aggregations := response.get("aggregations"):
            total_seen = aggregations["seen"]["value"]
        else:
            total_seen = 0

        total_unique = response["hits"]["total"]["value"]
        return {"total_seen": int(total_seen), "total_unique": int(total_unique)}
