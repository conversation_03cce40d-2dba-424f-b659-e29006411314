from apps.assets.models import CoverageCategory
from apps.assets.search.host import InventoryFilter
from apps.stats.base import BaseStat, StatCategory
from apps.stats.inventory.coverage_utils import get_coverage_by_category


class InventoryEndpointCoverage(BaseStat):
    id = "endpoint_coverage"
    label = "Endpoint Security Coverage"
    description = "Hosts by endpoint security coverage."
    category = StatCategory.INVENTORY

    def get_data(self, organization_ids, filters: InventoryFilter):
        return get_coverage_by_category(
            organization_ids, filters, CoverageCategory.ENDPOINT_SECURITY
        )
