from apps.assets.models import AssetCriticality, CoverageCategory
from apps.assets.search.host import HostClient, InventoryFilter
from apps.stats.base import BaseStat, StatCategory
from apps.stats.models.daily_coverage_gaps import PersistentDailyCoverageGaps


class InventoryInsights(BaseStat):
    id = "insights"
    label = "Insights"
    description = "Asset Inventory Insights."
    category = StatCategory.INVENTORY

    def get_data(self, organization_ids, filters: InventoryFilter):
        critical_values = {
            AssetCriticality.TIER_0,
            AssetCriticality.TIER_1,
            AssetCriticality.TIER_2,
        }

        endpoint_security_stats_for_today = (
            PersistentDailyCoverageGaps.documents.get_stats_for_today(
                organization_ids,
                filters,
                CoverageCategory.ENDPOINT_SECURITY,
            )
        )
        missing_endpoint, total_critical_assets = self.calculate_assets(
            critical_values, endpoint_security_stats_for_today
        )

        vulnerability_management_stats_for_today = (
            PersistentDailyCoverageGaps.documents.get_stats_for_today(
                organization_ids,
                filters,
                CoverageCategory.VULNERABILITY_MANAGEMENT,
            )
        )
        missing_vulnerability, _ = self.calculate_assets(
            critical_values, vulnerability_management_stats_for_today
        )

        technical_security_control_stats_for_today = (
            PersistentDailyCoverageGaps.documents.get_stats_for_today(
                organization_ids,
                filters,
                CoverageCategory.TECHNICAL_SECURITY_CONTROL,
            )
        )
        missing_technical_security_control, total_assets = self.calculate_assets(
            list(AssetCriticality), technical_security_control_stats_for_today
        )

        new_assets = self.get_new_assets(organization_ids, filters)

        return {
            "critical_assets_missing_endpoint": missing_endpoint,
            "critical_assets_missing_vulnerability": missing_vulnerability,
            "critical_assets": total_critical_assets,
            "missing_technical_security_control": missing_technical_security_control,
            "total_assets": total_assets,
            "new_assets": new_assets,
        }

    @staticmethod
    def calculate_assets(eligible_criticality, stats_for_today):
        stats_for_today = {
            key: value
            for key, value in stats_for_today.items()
            if key in eligible_criticality
        }
        critical_assets = sum(stat["total"] for stat in stats_for_today.values())
        missing_security = sum(stat["no_coverage"] for stat in stats_for_today.values())

        return missing_security, critical_assets

    @staticmethod
    def get_new_assets(organization_ids, filters: InventoryFilter):
        # New Asset is an asset that has been added to the inventory in the last 7 days.

        client = HostClient(organization_ids, filters)
        body = {
            "size": 0,
            "query": {
                "bool": {
                    "filter": [
                        {"range": {"metadata.created": {"gte": "now-7d"}}},
                    ]
                }
            },
        }
        response = client.search(body)
        new_assets = response.get("hits").get("total").get("value")

        return new_assets
