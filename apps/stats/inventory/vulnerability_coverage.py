from apps.assets.models import CoverageCategory
from apps.assets.search.host import InventoryFilter
from apps.stats.base import BaseStat, StatCategory
from apps.stats.inventory.coverage_utils import get_coverage_by_category


class InventoryVulnerabilityCoverage(BaseStat):
    id = "vulnerability_coverage"
    label = "Vulnerability Management Coverage"
    description = "Hosts by vulnerability management coverage."
    category = StatCategory.INVENTORY

    def get_data(self, organization_ids, filters: InventoryFilter):
        return get_coverage_by_category(
            organization_ids, filters, CoverageCategory.VULNERABILITY_MANAGEMENT
        )
