from datetime import timedelta

from django.utils import dateparse
from django.utils.timezone import now

from apps.api.v1.search import add_must_clause
from apps.assets.models import CoverageCategory, MergedAsset
from apps.assets.models.merged_source_asset import OsFamily
from apps.assets.search.host import InventoryFilter
from apps.stats.base import BaseStat, StatCategory
from apps.stats.models.persistent_stats import PersistentStat, StatType

HOST_TYPE_KEYWORD = "host"

DATE_FORMAT = "%m.%d"
DAYS = 31
INTERVAL = "1d"


class InventoryEndpointGaps(BaseStat):
    id = "endpoint_gaps"
    label = "Endpoint Security Gaps"
    description = "Historical hosts by endpoint security gaps."
    category = StatCategory.INVENTORY

    def get_data(self, organization_ids, filters: InventoryFilter):
        body = {}
        add_must_clause(body, {"terms": {"metadata.organization_id": organization_ids}})
        start_of_day = now().replace(hour=0, minute=0, second=0, microsecond=0)
        # Calculate exact timestamp because relative bounds are calculated
        # at query execution time and do not properly overlap
        time_range = {"gte": start_of_day - timedelta(days=DAYS - 1), "lte": now()}
        created_to_stats = PersistentStat.documents.date_histogram_latest(
            body, StatType.ENDPOINT_GAPS, time_range, INTERVAL
        )

        # Dates for histogram
        labels = [
            dateparse.parse_datetime(created).strftime(DATE_FORMAT)
            for created in created_to_stats.keys()
        ]

        # Use up-to-date data for today
        today_key = now().strftime(DATE_FORMAT)
        # These are in ISO format, so already sorted
        if created_to_stats:
            last_created = list(created_to_stats.keys())[-1]
            created_to_stats[last_created] = [
                self.calculate_persistent_stat(organization_ids, filters)
            ]
        else:
            created_to_stats[today_key] = [
                self.calculate_persistent_stat(organization_ids, filters)
            ]

        # Names of series in the chart
        series = [
            "Total",
            "Windows",
            "Linux",
            "macOS",
            "Unknown",
        ]

        data = [
            self.values_for(created_to_stats, "total"),
            self.values_for(created_to_stats, "windows.no_coverage"),
            self.values_for(created_to_stats, "linux.no_coverage"),
            self.values_for(created_to_stats, "mac.no_coverage"),
            self.values_for(created_to_stats, "unknown.no_coverage"),
        ]

        return {"labels": labels, "series": series, "data": data}

    @staticmethod
    def calculate_persistent_stat(organization_ids, filters: InventoryFilter):
        """
        Calculate the stat for current moment. Either to
        1. Record historical stats periodically
        2. Return the most recent version when querying historical stats for today.
        """
        excluded_clause = InventoryFilter.gap_excluded_clause(
            CoverageCategory.ENDPOINT_SECURITY
        )
        body = {
            "size": 0,
            "track_total_hits": True,
            "aggs": {
                "os_family": {
                    "terms": {
                        "field": "merged_data.os.family.keyword",
                        "missing": "unknown",
                    },
                    "aggs": {
                        "with_coverage": {
                            "filter": {
                                "bool": {
                                    "should": [
                                        {
                                            "exists": {
                                                "field": "metadata.asset.technologies.endpoint_security"
                                            }
                                        },
                                        excluded_clause,
                                    ],
                                    "minimum_should_match": 1,
                                },
                            },
                        }
                    },
                }
            },
        }
        add_must_clause(body, {"terms": {"metadata.organization_id": organization_ids}})
        response = MergedAsset.documents.search_raw(body)

        data = {
            "total": response["hits"]["total"]["value"],
        }

        counts = {}

        if aggregations := response.get("aggregations"):
            buckets = aggregations["os_family"]["buckets"]
        else:
            buckets = []

        for bucket in buckets:
            os_family = bucket["key"]
            total = bucket["doc_count"]
            with_coverage = bucket["with_coverage"]["doc_count"]
            no_coverage = total - with_coverage
            counts[os_family] = {
                "total": total,
                "with_coverage": with_coverage,
                "no_coverage": no_coverage,
            }

        for os_family in OsFamily.external_families():
            data[os_family] = counts.get(
                os_family,
                {
                    "total": 0,
                    "with_coverage": 0,
                    "no_coverage": 0,
                },
            )

        organization_id = ""
        if len(organization_ids) == 1:
            organization_id = organization_ids[0]
        return PersistentStat(
            organization_id=organization_id, type=StatType.ENDPOINT_GAPS, data=data
        )

    @staticmethod
    def values_for(created_to_stats, nested_key):
        values = []
        for stats in created_to_stats.values():
            value = 0
            for stat in stats:
                lookup = stat.data
                for key in nested_key.split("."):
                    lookup = lookup.get(key)
                    if lookup is None:
                        lookup = 0
                        break
                value += lookup
            values.append(int(value))
        return values
