from functools import partial
from uuid import UUID

from criticalstart.fastapi_utils.paginate.elasticsearch import (
    Page,
    PageParams,
    make_page_params,
    paginate,
)
from fastapi import APIRouter, Depends

from apps.api.configuration import OrganizationIntegrationStateService
from apps.api.v1.routers.hosts import body_from_query, host_sort
from apps.api.v1.schemas.filter import (
    HostSummary,
    QueryInventoryFilter,
)
from apps.assets.search.host import HostClient, InventoryFilter
from apps.internal_api.v1.schemas.host import InternalHost

router = APIRouter(
    prefix="/hosts",
    tags=["hosts"],
)


@router.get("")
def list_hosts(
    organization_id: UUID,
    body: dict = Depends(body_from_query),
    filters: InventoryFilter = Depends(QueryInventoryFilter()),
    paging: PageParams = Depends(make_page_params(host_sort)),
) -> Page[HostSummary]:
    client = HostClient(organization_ids={organization_id}, filters=filters)

    config_manager = OrganizationIntegrationStateService()
    from_dataclass = partial(HostSummary.from_dataclass, config_manager=config_manager)
    return paginate(body, client, paging, from_dataclass)


@router.get("/details")
def list_hosts_details(
    organization_id: UUID,
    body: dict = Depends(body_from_query),
    filters: InventoryFilter = Depends(QueryInventoryFilter()),
    paging: PageParams = Depends(make_page_params(host_sort)),
) -> Page[InternalHost]:
    client = HostClient(
        organization_ids={organization_id}, filters=filters, include_all_fields=True
    )

    config_manager = OrganizationIntegrationStateService()
    from_dataclass = partial(InternalHost.from_dataclass, config_manager=config_manager)
    return paginate(body, client, paging, from_dataclass)
