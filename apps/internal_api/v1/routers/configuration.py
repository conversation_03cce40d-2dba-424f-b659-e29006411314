from uuid import UUID

from fastapi import APIRouter

from apps.api.configuration import OrganizationIntegrationStateService
from apps.internal_api.v1.schemas.configuration import InternalCoverageState

router = APIRouter(
    prefix="/configuration",
    tags=["configuration"],
)


@router.get("/coverage_state", response_model=InternalCoverageState)
def get_coverage_state(organization_id: UUID):
    config_service = OrganizationIntegrationStateService()
    return InternalCoverageState(
        endpoint_coverage=config_service.get_endpoint_coverage_state(organization_id),
        vulnerability_coverage=config_service.get_vulnerability_coverage_state(
            organization_id
        ),
    )
