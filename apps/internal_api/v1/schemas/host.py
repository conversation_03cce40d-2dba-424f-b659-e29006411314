from datetime import datetime
from typing import Optional, Sequence
from uuid import UUID

from pydantic import BaseModel, ConfigDict

from apps.api.configuration import OrganizationIntegrationStateService
from apps.api.v1.schemas.filter import (
    HostAttributes,
    Integration,
    Technology,
    TechnologyLabels,
)
from apps.assets import models


class InternalHostSource(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    source_id: Optional[str]
    technology: Technology
    integration: Integration
    attributes: HostAttributes
    last_seen: Optional[datetime]

    @staticmethod
    def from_dataclass(source_asset: models.MergedSourceAsset):
        if isinstance(source_asset.attributes, models.HostAssetAttributes):
            return InternalHostSource(
                source_id=source_asset.metadata.asset.source_id,
                attributes=HostAttributes.from_dataclass(source_asset.attributes),
                last_seen=source_asset.last_seen,
                technology=Technology.from_id(source_asset.technology_id),
                integration=Integration(id=source_asset.integration_id),
            )
        raise TypeError("Source asset is not a host.")  # pragma: no cover


class InternalHost(HostAttributes):
    id: UUID
    organization_id: UUID
    account_id: UUID
    sources: Sequence[InternalHostSource]
    last_seen: Optional[datetime]
    coverage_gaps: list[models.CoverageCategory]

    # noinspection PyMethodOverriding
    @staticmethod
    def from_dataclass(
        merged_asset: models.MergedAsset,
        config_manager: OrganizationIntegrationStateService = None,
    ):
        if isinstance(merged_asset.merged_data, models.HostAssetAttributes):
            # Apply overrides here when returning merged asset data.
            merged_asset = merged_asset.overridden_merged_asset
            return InternalHost(
                **HostAttributes.from_dataclass(merged_asset.merged_data).model_dump(),
                id=merged_asset.id,
                organization_id=merged_asset.metadata.organization_id,
                account_id=merged_asset.metadata.account_id,
                sources=[
                    InternalHostSource.from_dataclass(s)
                    for s in merged_asset.source_data.all()
                    if s.technology_id in TechnologyLabels
                ],
                last_seen=merged_asset.metadata.asset.last_seen,
                coverage_gaps=config_manager.filter_gaps(
                    merged_asset.metadata.organization_id, merged_asset.coverage_gaps
                ),
            )
        raise TypeError("Merged asset is not a host.")  # pragma: no cover
