from apps.assets.models import AssetCriticality
from apps.assets.tests.es_base import <PERSON>SCase<PERSON>ixin
from apps.tests.base import BaseTestCase
from factories.merged_host import MergedHostFactory
from factories.merged_source_host import MergedSourceHostFactory


class InternalHostTests(ESCaseMixin, BaseTestCase):
    def test_missing_organization_id(self):
        self.get(status_code=422, organization_id=None)

    def test_search_merged_data(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
            ],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_3)
            ],
        )
        response = self.get(
            organization_id=self.organization.id, query="criticality: tier2"
        )
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual("tier2", objects[0]["criticality"])

    def test_search_any(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__hostname="host1")],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__hostname="host2")],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[MergedSourceHostFactory(attributes__hostname="host3")],
        )
        response = self.get(organization_id=self.organization.id, query="hostname: *")
        objects = response.json()["items"]
        self.assertEqual(3, len(objects))

    def test_filter_merged_data(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
            ],
        )
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_3)
            ],
        )
        params = {"criticality": "tier2"}
        response = self.get(organization_id=self.organization.id, params=params)
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual("tier2", objects[0]["criticality"])

    def test_filter_and_query_merged_data(self):
        host_1 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
            ],
        )
        host_2 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__criticality=AssetCriticality.TIER_2)
            ],
        )
        params = {"criticality": "tier2"}
        query = f'not id in ("{host_2.id}")'
        response = self.get(
            organization_id=self.organization.id, params=params, query=query
        )
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(host_1.id, objects[0]["id"])

    def test_list_details(self):
        MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(attributes__hostname="host1"),
                MergedSourceHostFactory(attributes__hostname="host2"),
            ],
        )
        response = self.get(organization_id=self.organization.id, route="details")
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(2, len(objects[0]["sources"]))

    def test_filter_and_query_details(self):
        host_1 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    attributes__hostname="host1",
                    attributes__criticality=AssetCriticality.TIER_2,
                )
            ],
        )
        host_2 = MergedHostFactory.create(
            metadata__organization_id=self.organization.id,
            source_assets=[
                MergedSourceHostFactory(
                    attributes__hostname="host2",
                    attributes__criticality=AssetCriticality.TIER_2,
                )
            ],
        )
        params = {"criticality": "tier2"}
        query = f'not id in ("{host_2.id}")'
        response = self.get(
            organization_id=self.organization.id,
            params=params,
            query=query,
            route="details",
        )
        objects = response.json()["items"]
        self.assertEqual(1, len(objects))
        self.assertEqual(host_1.id, objects[0]["id"])

    def get(
        self, status_code=200, organization_id=None, query=None, route=None, params=None
    ):
        path = "/internal/api/v1/hosts"
        if route:
            path += f"/{route}"

        params = params or {}
        if organization_id:
            params["organization_id"] = organization_id
        if query:
            params["query"] = query

        response = self.api_client.get(path, params=params)
        self.assertEqual(status_code, response.status_code, response.json())
        return response
