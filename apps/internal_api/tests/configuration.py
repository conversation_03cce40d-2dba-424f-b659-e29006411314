from apps.integrations.api.data_connectors import CoverageMode
from apps.integrations.models.integration import Integration
from apps.integrations.models import Technology
from apps.tests.base import ApiDatabaseTestCase
from factories.integration import IntegrationFactory, TechnologyFactory


class InternalConfigurationStateTests(ApiDatabaseTestCase):
    fixtures = ["accounts.json"]

    def setUp(self):
        super().setUp()
        self.patch("apps.api.entitlements.check_entitlement")

    def create_integration(
        self,
        technology_id,
        category_id="vulnerability_management",
        coverage_mode=CoverageMode.NOT_APPLICABLE,
        enabled=True,
        endpoint_coverage_mode=CoverageMode.NOT_APPLICABLE,
    ):
        # Create or get the technology
        technology = TechnologyFactory.create(technology_id=technology_id)
        return IntegrationFactory.create(
            organization=self.organization,
            technology=technology,
            category_id=category_id,
            vulnerability_coverage_mode=coverage_mode,
            enabled=enabled,
            endpoint_coverage_mode=endpoint_coverage_mode,
        )

    def test_endpoint_security_coverage_has_eligible_sources_false(self):
        self.assert_coverage("endpoint_coverage", "has_eligible_asset_sources", False)

    def test_endpoint_security_coverage_has_eligible_sources_true(self):
        self.create_integration(
            "defender_atp",
            "endpoint_security",
            CoverageMode.ENABLED,
            endpoint_coverage_mode=CoverageMode.ENABLED,
        )
        self.assert_coverage("endpoint_coverage", "has_eligible_asset_sources", True)

    def test_vulnerability_management_coverage_has_eligible_sources_false(self):
        self.assert_coverage(
            "vulnerability_coverage", "has_eligible_asset_sources", False
        )

    def test_vulnerability_management_coverage_has_eligible_sources_true(self):
        self.create_integration(
            "tenable_io", coverage_mode=Integration.CoverageMode.ENABLED
        )
        self.assert_coverage(
            "vulnerability_coverage", "has_eligible_asset_sources", True
        )

    def test_endpoint_security_coverage_has_designated_sources_false(self):
        self.assert_coverage("endpoint_coverage", "has_designated_asset_sources", False)

    def test_endpoint_security_coverage_has_designated_sources_true(self):
        self.create_integration(
            "defender_atp",
            "endpoint_security",
            CoverageMode.ENABLED,
            endpoint_coverage_mode=CoverageMode.ENABLED,
        )
        self.assert_coverage("endpoint_coverage", "has_designated_asset_sources", True)

    def test_vulnerability_management_coverage_has_designated_sources_false(self):
        self.assert_coverage(
            "vulnerability_coverage", "has_designated_asset_sources", False
        )

    def test_vulnerability_management_coverage_has_designated_sources_true(self):
        self.create_integration(
            "tenable_io", coverage_mode=Integration.CoverageMode.ENABLED
        )
        self.assert_coverage(
            "vulnerability_coverage", "has_designated_asset_sources", True
        )

    def test_endpoint_security_coverage_has_additional_sources_false(self):
        self.create_integration("defender_atp", "endpoint_security")
        self.assert_coverage("endpoint_coverage", "has_additional_asset_sources", False)

    def test_endpoint_security_coverage_has_additional_sources_true(self):
        self.create_integration("defender_atp", "endpoint_security")
        self.create_integration("user_input", "endpoint_security")
        self.assert_coverage("endpoint_coverage", "has_additional_asset_sources", True)

    def test_vulnerability_management_coverage_has_additional_sources_false(self):
        self.assert_coverage(
            "vulnerability_coverage", "has_additional_asset_sources", False
        )

    def test_vulnerability_management_coverage_has_additional_sources_true(self):
        self.create_integration(
            "tenable_io", coverage_mode=Integration.CoverageMode.ENABLED
        )
        self.create_integration(
            "user_input", coverage_mode=Integration.CoverageMode.ENABLED
        )
        self.assert_coverage(
            "vulnerability_coverage", "has_additional_asset_sources", True
        )

    def assert_coverage(self, coverage_type, coverage_state_key, expected_value):
        response = self.get()
        self.assertEqual(200, response.status_code)
        state = response.json()
        self.assertIn(coverage_type, state)
        self.assertEqual(expected_value, state[coverage_type][coverage_state_key])

    def get(self, status_code=200, query=None, params=None):
        params = params or {}
        path = "/internal/api/v1/configuration/coverage_state"

        params = {}
        params["organization_id"] = self.organization.id

        response = self.api_client.get(path, params=params)
        self.assertEqual(status_code, response.status_code, response.json())
        return response
