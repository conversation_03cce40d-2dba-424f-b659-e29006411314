#!/bin/bash -e
#
## Send this data to NewRelic so that we have metrics for the current branch

# Split on '/', take the 3rd element onwards
# Example: refs/heads/feature/BET123 -> feature/BET123
BRANCH=$(echo $CODEBUILD_WEBHOOK_HEAD_REF | cut -d/ -f 3)
COMMIT_ID=$(git rev-parse HEAD)

# This is redundant, but being pedantic for humans reading this
NEW_RELIC_INSERT_KEY="$NEW_RELIC_INSERT_KEY"
NEW_RELIC_ACCOUNT_ID="$NEW_RELIC_ACCOUNT_ID"

# We only care about metrics for the main branches
if [[ $BRANCH != "develop" && $BRANCH != "master"  && $BRANCH != "main" ]]; then
 exit 0
fi

COVERAGE_DATA=""
COVERAGE_FILE=$(find . -name "coverage.json" -print -quit)

# If the variable is not empty and the file exists
if [[ -n "$COVERAGE_FILE" && -e "$COVERAGE_FILE" ]]; then
  echo "coverage.json found at - $COVERAGE_FILE"
  COVERAGE_DATA=$(cat <<EOF
    "coverage.totals.covered_lines": $(cat "$COVERAGE_FILE" | jq .totals.covered_lines),
    "coverage.totals.num_statements": $(cat "$COVERAGE_FILE" | jq .totals.num_statements),
    "coverage.totals.percent_covered": $(cat "$COVERAGE_FILE" | jq .totals.percent_covered),
    "coverage.totals.missing_lines": $(cat "$COVERAGE_FILE" | jq .totals.missing_lines),
    "coverage.meta.branch_coverage": $(cat "$COVERAGE_FILE" | jq .meta.branch_coverage)
EOF
  )
fi


# Otherwise, we want to post some data to NR. This code will only run when there is
# a new merge commit to `master` or `develop`, and thus, only looks at the most recent
# commit.
#
# We only look at diffs inside subdirs, so if top level files change we don't care.
COMMIT_DATA=$(cat <<EOF
{
   "eventType":           "GithubEvent",
   "branch":              "$BRANCH",
   "repository":          "$(basename $(git rev-parse --show-toplevel))",
   "commit_id":           "$COMMIT_ID",
   "commit_short_id":     "$(git rev-parse --short HEAD)",
   "files_added_count":    $(git show --stat --diff-filter=A | grep " | " | wc -l),
   "files_deleted_count":  $(git show --stat --diff-filter=D | grep " | " | wc -l),
   "files_modified_count": $(git show --stat --diff-filter=M | grep " | " | wc -l),
   $COVERAGE_DATA
}
EOF
)
# Print to console (colorized) and also to data.json
echo "Sending commit Event to NewRelic"
echo $COMMIT_DATA  | jq .
echo $COMMIT_DATA  | jq . > data.json

gzip -c data.json | curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-Insert-Key: $NEW_RELIC_INSERT_KEY" \
  -H "Content-Encoding: gzip" \
  https://insights-collector.newrelic.com/v1/accounts/$NEW_RELIC_ACCOUNT_ID/events --data-binary @-
