version: 0.2

env:
  variables:
    DOCKER_BUILDKIT: 1

phases:
  pre_build:
    commands:
      # REPOSITORY_URL and $REGISTRY_URI are set by the build environment via terraform
      - echo Logging in to Amazon ECR...
      - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $REGISTRY_URL
      - echo Logging in to Amazon CodeArtifact...
      - export CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain criticalstart --domain-owner 818476207984 --query authorizationToken --duration-seconds 1800 --output text)
      - export PIP_INDEX_URL="https://aws:${CODEARTIFACT_AUTH_TOKEN}@criticalstart-818476207984.d.codeartifact.us-west-2.amazonaws.com/pypi/criticalstart_global/simple/"
      - TAG="$(git rev-parse --short HEAD)"
      - PIP_TAG="$(sha256sum requirements.txt | cut -c 1-7)"

      # Pre-commit checks
      - pip install pre-commit
      - pre-commit run --all-files

      # Start/build test services
      - export COMPOSE_FILE=docker-compose.codebuild.yml
      - docker-compose up -d rabbitmq postgres opensearch27
      # Because $PIP_INDEX_URL contains a token that changes every build, Docker will try to rebuild any container
      # dependent on that ARG. So, that container is split into its own build/deploy cycle, which is only built if the
      # requirements file actually changes. All other builds are based on the containers pushed to ECR.
      - PUSH_BUILD_IMAGE=0
      - aws ecr describe-images --repository-name=advancedthreatanalytics/asset_inventory --image-ids imageTag=pip-$PIP_TAG 2>&1 > /dev/null; EXITCODE=$?
      - |
        if [ $EXITCODE != 0 ]; then
          echo "requirements have changed, need to build and deploy new build image"
          docker build --target build \
            --build-arg BUILDKIT_INLINE_CACHE=1 \
            --build-arg PIP_INDEX_URL \
            --build-arg BUILD_CONTAINER_HASH=pip-$PIP_TAG \
            --cache-from=$REPOSITORY_URI:pip-$PIP_TAG \
            --tag $REPOSITORY_URI:pip-$PIP_TAG .
          PUSH_BUILD_IMAGE=1
        else
          echo "build image with current requirements exists, no need to rebuild"
        fi
      - |
        docker build --target deploy \
          --build-arg BUILDKIT_INLINE_CACHE=1 \
          --build-arg BUILD_CONTAINER_HASH=pip-$PIP_TAG \
          --cache-from=$REPOSITORY_URI:pip-$PIP_TAG \
          --tag $REPOSITORY_URI:$TAG .
      - |
        docker build --target test \
          --build-arg BUILDKIT_INLINE_CACHE=1 \
          --build-arg BUILD_CONTAINER_HASH=pip-$PIP_TAG \
          --cache-from=$REPOSITORY_URI:pip-$PIP_TAG \
          --cache-from=$REPOSITORY_URI:$TAG \
          --tag test .

      - docker-compose up -d --no-recreate --wait

  build:
    commands:
      - docker-compose exec -T app python manage.py makemigrations --check --dry-run
      - docker-compose exec -T app pytest
      - docker-compose exec -T app pytest -c pytest_demo.ini

  post_build:
    commands:
      # This was added to debug build issues with opensearch failing during a test.
      # Commenting out for now because the issue was fixed, but keeping around in case we need to debug in the future.
      #      - |
      #        if [ "$CODEBUILD_BUILD_SUCCEEDING" -eq "0" ]; then
      #          docker-compose logs opensearch27
      #        fi
      - |
        if [ "$PUSH_BUILD_IMAGE" -eq "1" ]; then
          docker push $REPOSITORY_URI:pip-$PIP_TAG
        fi
      - |
        if [ "$CODEBUILD_BUILD_SUCCEEDING" -eq "1" ]; then
          docker push $REPOSITORY_URI:$TAG
        fi
      - docker-compose exec -T app mkdir coverage
      - docker-compose exec -T app coverage html --directory=coverage
      - docker-compose exec -T app coverage json -o coverage/coverage.json
      - docker-compose cp app:/opt/app/coverage coverage
      - ./scripts/write_build_info_to_newrelic.sh

artifacts:
  name: coverage_report
  files:
    - coverage/**
