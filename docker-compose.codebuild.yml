version: "3.9"

name: codebuild

services:
  app:
    image: test
    build:
      context: .
      target: test
    command: ["gunicorn", "--bind=0.0.0.0:8000", "core.wsgi"]
    environment:
      - DEBUG=True
      - PYTHONUNBUFFERED=true
      - DOCKER_IP=docker
      - INTERNAL_IPS=localhost
      - ALLOWED_HOSTS=localhost,docker,0.0.0.0,app
      - DJANGO_SETTINGS_MODULE=core.settings
      - DJANGO_SECRET_KEY=12345
      - AWS_DEFAULT_REGION=us-west-2
      - BACKEND_URL=http://localhost:8000
      - AUTH_JWT_SECRET_KEYS=HS512:zgH3eMmRy227MAoBUif+k0mHn5PaXPJE
      - EXTERNAL_DOMAIN=ataplatform.io
      # Local Infrastructure
      - DATABASE_URL=**************************************/docker
      - CELERY_BROKER_URL=amqp://guest:guest@rabbitmq:5672//
      - ELASTICSEARCH_HOSTS=opensearch27
      # Application config
      - CONNECTOR_SERVICE_TOKEN=dummy
    networks:
      - codebuild

    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      opensearch27:
        condition: service_healthy
  postgres:
    image: 818476207984.dkr.ecr.us-west-2.amazonaws.com/advancedthreatanalytics/docker-postgres-dev:15
    environment:
      POSTGRES_USER: docker
      POSTGRES_PASSWORD: docker
      POSTGRES_DB: docker
    networks:
      - codebuild
  rabbitmq:
    image: 818476207984.dkr.ecr.us-west-2.amazonaws.com/advancedthreatanalytics/ata_mq:3.9-d31a5a9
    networks:
      - codebuild
  opensearch27:
    image: public.ecr.aws/opensearchproject/opensearch:2.16.0
    environment:
      - bootstrap.memory_lock=true
      - cluster.name=opensearch-cluster
      - discovery.type=single-node
      - node.name=opensearch27
      - "OPENSEARCH_JAVA_OPTS=-Xms1g -Xmx2g"
      - OPENSEARCH_INITIAL_ADMIN_PASSWORD=yourStrongPassword123!
      - "DISABLE_INSTALL_DEMO_CONFIG=true"
      - "DISABLE_SECURITY_PLUGIN=true"
    tmpfs:
      - /usr/share/opensearch/data:mode=770,uid=1000,gid=1000,size=256m
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    healthcheck:
      test:
        [
          "CMD",
          "curl",
          "--fail",
          "http://localhost:9200/_cluster/health?wait_for_status=yellow&timeout=5s",
        ]
      interval: 10s # Time between each check
      timeout: 5s # Time to wait for a response before failing
      start_period: 15s # Time to wait before starting the health checks
      retries: 15 # Number of retries before marking the service as unhealthy
    networks:
      - codebuild

networks:
  codebuild:
    driver: bridge
