[tool.pytest.ini_options]
addopts = "--reuse-db --ignore=scripts --ignore=tmp --durations=15 --tb=short"
python_files = "tests.py tests/*.py"
filterwarnings = [
    "ignore:::custom_user",
    "ignore:::onelogin",
    "ignore:the imp module is deprecated in favour of importlib",
    "ignore:.*defines default_app_config",
    "ignore:.*is deprecated in favor of django.urls.re_path.*",
    "ignore:`formatargspec` is deprecated since Python 3.5.*",
]

[tool.ruff]
fix = true
line-length = 88
select = [
    "I",    # isort
    "N",    # pep8 naming
    "TID",  # flake8-tidy-imports
    "T201", # print found
    "F401", # imported but unused
    "F841", # local variable is assigned to but never used
]
ignore=["E501"]
target-version = "py38"

[tool.ruff.isort]
known-third-party = ["newrelic"]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"**/management/commands/*.py" = ["T201"]
"scripts/*.*" = ["T201"]
"**/schemas/*.py" = ["N805"]
"apps/assets/models/asset.py" = ["N805"]
"apps/assets/models/merged_asset.py" = ["N805"]
