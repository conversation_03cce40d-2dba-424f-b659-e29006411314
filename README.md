`develop`: ![Codebuild Status Develop](https://codebuild.us-west-2.amazonaws.com/badges?uuid=eyJlbmNyeXB0ZWREYXRhIjoiKzdmQzRadHJsN25jOEMycjdMOXN2bEEraXdwYzRyQWk1QlVZSmxUOFY1TXJsMjlkU29EZjlBd1RtVktIN1NHS3NtV1hxYWVJSGhyMjI3OG5NYnl3ekdVPSIsIml2UGFyYW1ldGVyU3BlYyI6IkhueEtRd1drQlBkaU40dloiLCJtYXRlcmlhbFNldFNlcmlhbCI6MX0%3D&branch=develop)

`main`: ![Codebuild Status Main](https://codebuild.us-west-2.amazonaws.com/badges?uuid=eyJlbmNyeXB0ZWREYXRhIjoiKzdmQzRadHJsN25jOEMycjdMOXN2bEEraXdwYzRyQWk1QlVZSmxUOFY1TXJsMjlkU29EZjlBd1RtVktIN1NHS3NtV1hxYWVJSGhyMjI3OG5NYnl3ekdVPSIsIml2UGFyYW1ldGVyU3BlYyI6IkhueEtRd1drQlBkaU40dloiLCJtYXRlcmlhbFNldFNlcmlhbCI6MX0%3D&branch=main)

---

## Asset Inventory Service

The Asset Inventory Service is responsible for merging assets from various source systems into a unified, consolidated form. It manages different types of assets and applies rules to ensure that assets from different systems are properly matched and merged.

### Types of Assets

- **Staged Asset**: This represents the form in which the asset is received from the integration sync/fetch task.
  A Staged Asset is not an actual class but a dictionary form of the response.
  It acts as the initial stage in the asset merging pipeline.

- **Source Asset**: The dictionary staged asset is converted to a model form called Source Asset.
  It serves as the input data used to create a Merged Asset.
  Multiple Source Assets may contribute to the creation of a single Merged Asset.

- **Merged Asset**: A Merged Asset is the final, consolidated form of an asset.
  It contains a list of Source Assets from which it was derived and reflects the unified view of an asset across different systems.

### Merged Asset

A **Merged Asset** plays a crucial role in the Asset Inventory Service,
representing the consolidated view of an asset across all source systems.
Below are two ways to understand the concept of a Merged Asset: descriptively and constructively.

#### Descriptive Definition

A **MergedAsset** is a unified entity representing all **SourceAssets** from different source systems
that are considered equivalent based on a set of **IdentificationRules**. Two SourceAssets are part of
the same MergedAsset if and only if there exists a chain of matches between them, where each match is
determined by an **IdentificationRule** that links their attributes. Therefore, a MergedAsset consists of
all SourceAssets that are transitively connected through these matches. This creates a consolidated
and reconciled view of the asset data from all relevant sources.

#### Constructive Definition

A **MergedAsset** is constructed as a unified representation of an asset, created from one or more
SourceAssets across different source systems. The process begins with a single **SourceAsset** when no
match is found in the Identification step. During this step, **IdentificationRules** are used to
determine uniqueness. When a match is identified between SourceAssets, those SourceAssets are added
to the **MergedAsset** that the matched **SourceAsset** belongs to.

Through transitive relationships, if a **SourceAsset** matches multiple SourceAssets that
belong to different MergedAssets, those MergedAssets are merged into a single **MergedAsset**.
This dynamic process results in the evolution of the **MergedAsset**, providing a continuously
updated and unified view of asset data from all relevant sources.

---

## First Time Setup

1. Run make init
   - `make init`

## Running the API Server

`make serve` or `uvicorn core.asgi:app --reload`

## Integrating with ZTAP

This service has dependencies on

- [Auth](https://github.com/AdvancedThreatAnalytics/ata_portal_auth) (port 8002)

## Code Formatting

See [Black](https://black.readthedocs.io/en/stable/)

##
