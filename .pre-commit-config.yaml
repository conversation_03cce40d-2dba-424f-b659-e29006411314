---
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: check-added-large-files
      - id: check-ast
      - id: check-builtin-literals
      - id: check-case-conflict
      - id: check-executables-have-shebangs
      - id: check-json
      - id: check-merge-conflict
      - id: check-shebang-scripts-are-executable
      - id: check-symlinks
      - id: check-toml
      - id: check-vcs-permalinks
      - id: check-xml
      - id: check-yaml
      - id: debug-statements
      - id: destroyed-symlinks
      - id: detect-aws-credentials
        args: ["--allow-missing-credentials"]
      - id: detect-private-key
      - id: end-of-file-fixer
      - id: file-contents-sorter
      - id: fix-byte-order-marker
      - id: fix-encoding-pragma
        args: ["--remove"]
      - id: forbid-submodules
      - id: mixed-line-ending
        args: ["--fix=lf"]
      - id: no-commit-to-branch
      - id: requirements-txt-fixer
      - id: sort-simple-yaml
      - id: trailing-whitespace

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: "v3.0.0-alpha.4"
    hooks:
      - id: prettier
        # general html formatters aren't compatible with Django html templates
        exclude_types: ["html"]

  - repo: https://github.com/psf/black
    rev: 23.1.0
    hooks:
      - id: black

  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.0.253
    hooks:
      - id: ruff
        # see pyproject.toml for additional config
        args: ["--exit-non-zero-on-fix"]
