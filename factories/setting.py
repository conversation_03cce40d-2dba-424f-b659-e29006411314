import factory
from factory import CREATE_STRATEGY
from factory.django import DjangoModelFactory

from apps.assets.models import Setting, SettingKey
from apps.assets.models.setting import DaysSettingValue


class SettingFactory(DjangoModelFactory):
    class Meta:
        model = Setting
        strategy = CREATE_STRATEGY

    id = factory.Faker("uuid4")
    key = SettingKey.FILTER_LAST_SEEN_GTR_X_DAYS
    category = Setting.Category.DISPLAY
    default_value = DaysSettingValue(enabled=True, days=30).model_dump()
