import factory
from factory import CREATE_STRATEGY
from factory.django import DjangoModelFactory

from apps.assets.models import OrganizationSetting
from apps.assets.models.setting import DaysSettingValue
from factories.organization import OrganizationFactory
from factories.setting import SettingFactory


class OrganizationSettingFactory(DjangoModelFactory):
    class Meta:
        model = OrganizationSetting
        strategy = CREATE_STRATEGY

    id = factory.Faker("uuid4")
    organization = factory.SubFactory(OrganizationFactory)
    setting = factory.SubFactory(SettingFactory)
    overridden_value = DaysSettingValue(enabled=True, days=45).model_dump()
