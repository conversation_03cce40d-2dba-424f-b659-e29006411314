import uuid

import factory
from factory import BUILD_STRATEGY
from factory.django import DjangoModelFactory

from apps.accounts.models import Organization
from factories.account import AccountFactory


class OrganizationFactory(DjangoModelFactory):
    class Meta:
        model = Organization
        strategy = BUILD_STRATEGY

    id = uuid.uuid4()
    alias = factory.Faker("name")
    account = factory.SubFactory(AccountFactory)
