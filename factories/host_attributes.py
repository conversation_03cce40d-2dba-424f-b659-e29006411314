import factory
from factory import BUILD_STRATEGY

from apps.assets.models.merged_source_asset import (
    AssetCriticality,
    HostAssetAttributes,
    InternetExposure,
)
from factories.os_attributes import OsAttributesFactory
from factories.owner_attributes import OwnerAttributesFactory


class HostAttributesFactory(factory.Factory):
    class Meta:
        strategy = BUILD_STRATEGY
        model = HostAssetAttributes
        exclude = ("domain",)

    hostname = factory.Faker(provider="hostname", levels=0)

    domain = factory.Faker(provider="domain_name", levels=2)

    fqdn = factory.LazyAttribute(lambda o: [f"{o.hostname}.{o.domain}"])

    public_ip = factory.List([factory.Faker(provider="ipv4_public")])
    private_ip = factory.List(
        [factory.Faker(provider="ipv4_private"), factory.Faker(provider="ipv4_private")]
    )
    # TODO remove after migration
    mac_address = factory.List(
        [factory.Faker(provider="mac_address"), factory.Faker(provider="mac_address")]
    )
    universal_mac_address = factory.List(
        [factory.Faker(provider="mac_address"), factory.Faker(provider="mac_address")]
    )
    local_mac_address = factory.List(
        [factory.Faker(provider="mac_address"), factory.Faker(provider="mac_address")]
    )
    internet_exposure = factory.Faker(
        provider="random_element", elements=list(InternetExposure)
    )

    group_name = factory.List(
        [
            factory.Faker(provider="word", part_of_speech="noun"),
            factory.Faker(provider="word", part_of_speech="noun"),
        ]
    )

    criticality = factory.Faker(
        provider="random_element", elements=list(AssetCriticality)
    )
    os = factory.SubFactory(OsAttributesFactory)
    aad_id = factory.Faker(provider="uuid4")

    owner = factory.List([factory.SubFactory(OwnerAttributesFactory)])

    @classmethod
    def _build(cls, model_class, *args, **kwargs):
        if "primary_ip_address" not in kwargs:
            primary = HostAssetAttributes.default_primary_ip_addr(
                preferred_ips=kwargs.get("private_ip"),
                secondary_ips=kwargs.get("public_ip"),
            )
            kwargs["primary_ip_address"] = primary
        if "primary_mac_address" not in kwargs:
            primary = HostAssetAttributes.default_primary_mac_addr(
                universal_addresses=kwargs.get("universal_mac_address"),
                local_addresses=kwargs.get("local_mac_address"),
            )
            kwargs["primary_mac_address"] = primary
        return super()._build(model_class, *args, **kwargs)
