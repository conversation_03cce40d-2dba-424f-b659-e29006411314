import factory
from criticalstart.data_connectors.sdk.models import (
    AssetCriticality,
    HostType,
    OsFamily,
)

from apps.assets.models import InternetExposure


class OsAttributesFactory(factory.DictFactory):
    class Meta:
        strategy = factory.BUILD_STRATEGY

    host_type = HostType.WORKSTATION
    family = OsFamily.WINDOWS
    name = "Windows 10"


class OwnerAttributesFactory(factory.DictFactory):
    class Meta:
        strategy = factory.BUILD_STRATEGY

    name = factory.Faker(provider="name")
    email = factory.Faker(provider="email")


class StagedHostFactory(factory.DictFactory):
    class Meta:
        strategy = factory.BUILD_STRATEGY
        exclude = ("domain",)

    source_id = factory.Faker(provider="uuid4")
    hostname = factory.Faker(provider="hostname", levels=0)
    domain = factory.Faker(provider="domain_name", levels=2)
    fqdns = factory.LazyAttribute(lambda o: [f"{o.hostname}.{o.domain}"])

    group_names = factory.List(
        [
            factory.Faker(provider="word", part_of_speech="noun"),
            factory.Faker(provider="word", part_of_speech="noun"),
        ]
    )

    ip_addresses = factory.List(
        [
            factory.Faker(provider="ipv4_private"),
            factory.Faker(provider="ipv4_private"),
            factory.Faker(provider="ipv4_public"),
        ]
    )
    mac_addresses = factory.List(
        [factory.Faker(provider="mac_address"), factory.Faker(provider="mac_address")]
    )
    internet_exposure = factory.Faker(
        provider="random_element", elements=list(InternetExposure)
    )

    os = factory.SubFactory(OsAttributesFactory)
    owners = factory.List([factory.SubFactory(OwnerAttributesFactory)])
    aad_id = factory.Faker(provider="uuid4")
    criticality = factory.Faker(
        provider="random_element", elements=list(AssetCriticality)
    )

    last_seen = factory.Faker(
        provider="date_time_between", start_date="-1M", end_date="now"
    )
