import uuid

import factory.django
from django.utils import timezone
from factory import BUILD_STRATEGY

from apps.assets.models import AssetType, SyncTask

from .integration import IntegrationFactory


class SyncTaskFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = SyncTask
        strategy = BUILD_STRATEGY

    correlation_id = factory.LazyFunction(uuid.uuid4)
    status = SyncTask.Status.COMPLETED

    started_at = factory.LazyFunction(timezone.now)
    staged_at = factory.LazyFunction(timezone.now)
    finished_at = factory.LazyFunction(timezone.now)

    integration = factory.SubFactory(IntegrationFactory)
    asset_type = AssetType.HOST
