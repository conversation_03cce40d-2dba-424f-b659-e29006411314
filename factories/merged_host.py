from collections import defaultdict
from copy import deepcopy
from typing import Sequence

import factory
from factory import BUILD_STRATEGY

from apps.assets.models import MergedAsset, MergedSourceAsset, SourceHost
from apps.assets.models.convert import convert_to_source_host
from apps.assets.models.merged_asset import (
    MergedAssetMetadata,
    MergedAssetOverrides,
)
from apps.assets.services.merged_asset_service import merged_asset_service
from apps.integrations.utils import get_all_technology_ids
from factories.merged_source_host import HostMetadataFactory, MergedSourceHostFactory


class MergedHostMetadataFactory(factory.Factory):
    class Meta:
        model = MergedAssetMetadata
        strategy = BUILD_STRATEGY

    account_id = factory.Faker(provider="uuid4")
    account_alias = factory.Faker("company")
    organization_id = factory.Faker(provider="uuid4")
    organization_alias = factory.Faker("company")
    asset = None
    created = None
    manually_merged = False

    @factory.lazy_attribute
    def updated(self):
        return self.created


class MergedHostOverridesFactory(factory.Factory):
    class Meta:
        model = MergedAssetOverrides


class MergedHostFactory(factory.Factory):
    class Meta:
        model = MergedAsset
        strategy = BUILD_STRATEGY

    class Params:
        technology_ids: Sequence[str] = None
        source_assets: Sequence[MergedSourceAsset] = None

    locator = None
    metadata = factory.SubFactory(MergedHostMetadataFactory)
    overrides = factory.SubFactory(MergedHostOverridesFactory)

    @factory.lazy_attribute
    def source_data(self):
        if self.source_assets:
            ret = defaultdict(list)
            for asset in self.source_assets:
                ret[asset.technology_id].append(asset)
            return dict(ret)

        tech_ids = self.technology_ids or get_all_technology_ids()

        return {
            tech_id: [
                MergedSourceHostFactory.build(
                    metadata=HostMetadataFactory(integration__technology_id=tech_id)
                )
            ]
            for tech_id in tech_ids
        }

    @factory.lazy_attribute
    def merged_data(self):
        # TODO: For now we just copy the attributes from the first source asset.
        #  In the future we should merge all sources using reconciliation rules.
        return deepcopy(next(iter(self.source_data.values()))[0].attributes)

    @classmethod
    def _build(cls, model_class, *args, **kwargs):
        merged_asset = super()._build(model_class, *args, **kwargs)
        merged_asset_service.rebuild_sources_metadata([merged_asset])
        return merged_asset

    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        merged_asset = cls._build(model_class, *args, **kwargs)
        MergedAsset.documents.create(merged_asset)
        source_hosts = [
            convert_to_source_host(merged_asset, source_host)
            for source_host in merged_asset.source_data.all()
        ]
        SourceHost.documents.create_bulk(source_hosts)

        return merged_asset
