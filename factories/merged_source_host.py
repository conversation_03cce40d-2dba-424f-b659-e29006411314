import factory
from factory import BUILD_STRATEGY

from apps.assets.models import MergedSourceAsset
from apps.assets.models.merged_source_asset import (
    AssetType,
    SourceAssetDetailMetadata,
    SourceAssetIntegrationMetadata,
    SourceAssetMetadata,
)
from apps.integrations.models import Integration
from apps.integrations.utils import get_external_technology_ids, get_technology_category
from factories.host_attributes import HostAttributesFactory


class HostIntegrationMetadataFactory(factory.Factory):
    class Meta:
        strategy = BUILD_STRATEGY
        model = SourceAssetIntegrationMetadata

    id = factory.Faker(provider="uuid4")
    technology_id: str = factory.LazyFunction(
        lambda: get_external_technology_ids()[0]
        if get_external_technology_ids()
        else "test_tech"
    )
    vulnerability_coverage_mode = Integration.CoverageMode.NOT_APPLICABLE
    endpoint_coverage_mode = Integration.CoverageMode.NOT_APPLICABLE

    @factory.lazy_attribute
    def category_id(self):
        # Use correct category based on technology_id
        return get_technology_category(self.technology_id)


class SourceHostMetadataFactory(factory.Factory):
    class Meta:
        strategy = BUILD_STRATEGY
        model = SourceAssetDetailMetadata

    type = AssetType.HOST
    last_seen = factory.Faker(
        provider="date_time_between", start_date="-1M", end_date="now"
    )
    source_id = factory.Faker(provider="password", length=15, special_chars=False)


class HostMetadataFactory(factory.Factory):
    class Meta:
        strategy = BUILD_STRATEGY
        model = SourceAssetMetadata

    integration = factory.SubFactory(HostIntegrationMetadataFactory)
    asset = factory.SubFactory(SourceHostMetadataFactory)


class MergedSourceHostFactory(factory.Factory):
    class Meta:
        model = MergedSourceAsset
        strategy = BUILD_STRATEGY

    asset_id = factory.Faker(provider="uuid4")
    metadata = factory.SubFactory(HostMetadataFactory)
    attributes = factory.SubFactory(HostAttributesFactory)
