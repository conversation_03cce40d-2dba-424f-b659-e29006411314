layout virtualenvwrapper $(basename $PWD)

if ! has pre-commit; then
  RED=$(tput setaf 1)
  RESET=$(tput sgr0)
  echo
  echo -e "${RED}WARNING: pre-commit hooks not installed!${RESET}"
  echo "Please install pre-commit: https://criticalstart.atlassian.net/wiki/spaces/CO/pages/2865823786/Pre+Commit+Hooks"
  echo
  exit 1
else
  pre-commit install
fi

set -a
source_up_if_exists

GITHUB_TOKEN="$(gh config get -h github.com oauth_token)"

# Environment Settings
# You can overwrite these to suit your local environment in .envrc.local or .env.local
# Python/Django Settings
dotenv .env

# Load (if available) .envrc.local where developers can override settings without mucking with our shared .env file
source_env_if_exists .envrc.local
dotenv_if_exists .local.env
dotenv_if_exists .demo.env

unset PS1
