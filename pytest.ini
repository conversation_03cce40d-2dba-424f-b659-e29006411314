[pytest]
python_files = tests.py tests/*.py
addopts = --reuse-db --ignore='apps/demo' --ignore=scripts --ignore=tmp --durations=15 --tb=short --cov --cov-fail-under=100

filterwarnings =
    ; This will be updated by fastapi in a future version; it's upstream's problem
    ; They have decided that a different implementation is better than the starlette
    ; version since it is not well documented.
    error
    ignore:starlette.middleware.wsgi is deprecated and will be removed in a future release.*
env =
    RAW_ASSET_FILE_SYSTEM_URL=mem://
    # Use the test service bus client id to avoid conflicts with the local environment.
    SERVICE_BUS_CLIENT_ID=asset_inventory_test
