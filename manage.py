#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys

from ata_common.file_utils.read_env import read_env
from django.conf import settings
from django.core.management.commands.runserver import Command as runserver_Command


def main():
    """Run administrative tasks."""
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings")
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc

    # Override default port for `runserver` command if in debug
    if settings.DEBUG:
        runserver_Command.default_port = settings.PORT

    read_env()
    execute_from_command_line(sys.argv)


if __name__ == "__main__":
    main()
