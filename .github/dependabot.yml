version: 2

registries:
  py-codeartifact:
    type: python-index
    url: https://criticalstart-818476207984.d.codeartifact.us-west-2.amazonaws.com/pypi/criticalstart_global/simple/
    username: aws
    password: ${{ secrets.CODEARTIFACT_AUTH_TOKEN }}
    replaces-base: true
updates:
  - package-ecosystem: "pip"
    registries: "*"
    # This is unfortunately required in order to allow <PERSON><PERSON><PERSON><PERSON> to access a private python index.
    # I believe this is a bug and tracked by https://github.com/dependabot/dependabot-core/issues/10972
    # The risk here is that we could expose a read-only code artifact token (lives for 10 hours), but that would at worst
    # give an attacker access to some of our shared libraries (ata-common, microsoft-client, etc.) but not actual service/application code.
    insecure-external-code-execution: allow
    directory: /
    schedule:
      interval: "daily"
    # Honor version constraints
    versioning-strategy: "lockfile-only"
    # Group some dependencies together to reduce PRs
    groups:
      telemetry:
        patterns:
          - "sentry*"
          - "newrelic*"
          - "colorlog"
      django:
        patterns:
          - "django*"
      security:
        applies-to: security-updates
        patterns:
          - "*"
    assignees:
      - "AdvancedThreatAnalytics/cerberus"
    reviewers:
      - "AdvancedThreatAnalytics/cerberus"
