version: "3.9"

services:
  rabbitmq:
    image: public.ecr.aws/docker/library/rabbitmq:3.10-management
    volumes:
      - rabbitmq:/var/lib/rabbitmq/mnesia/
    ports:
      - ${CELERY_PORT}:5672
      - ${RABBIT_ADMIN_PORT}:15672

  postgres:
    image: 818476207984.dkr.ecr.us-west-2.amazonaws.com/advancedthreatanalytics/docker-postgres-dev:15
    ports:
      - ${DATABASE_PORT}:5432
    volumes:
      - postgres:/var/lib/postgresql/data

  opensearch27:
    image: public.ecr.aws/opensearchproject/opensearch:2.16.0
    volumes:
      - opensearch27:/usr/share/opensearch/data
      - opensearch27-backup:/var/snapshots
    environment:
      - bootstrap.memory_lock=true
      - cluster.name=opensearch-cluster
      - discovery.type=single-node
      - node.name=opensearch27
      - path.repo=/var/snapshots
      - "OPENSEARCH_JAVA_OPTS=-Xms1g -Xmx1g"
      - OPENSEARCH_INITIAL_ADMIN_PASSWORD=yourStrongPassword123!
      - "DISABLE_INSTALL_DEMO_CONFIG=true"
      - "DISABLE_SECURITY_PLUGIN=true"
    ports:
      - 9204:9200
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    healthcheck:
      test:
        [
          "CMD",
          "curl",
          "--fail",
          "http://localhost:9200/_cluster/health?wait_for_status=yellow&timeout=5s",
        ]
      interval: 10s # Time between each check
      timeout: 5s # Time to wait for a response before failing
      start_period: 15s # Time to wait before starting the health checks
      retries: 15 # Number of retries before marking the service as unhealthy

  opensearch-dashboards:
    image: public.ecr.aws/opensearchproject/opensearch-dashboards:2.16.0
    ports:
      - 5604:5601
    environment:
      OPENSEARCH_HOSTS: http://opensearch27:9200
      DISABLE_SECURITY_DASHBOARDS_PLUGIN: true
    depends_on:
      opensearch27:
        condition: service_healthy

volumes:
  postgres:
    name: ${VOLUME_PREFIX}_postgres15
  rabbitmq:
    name: ${VOLUME_PREFIX}_rabbitmq
  opensearch27:
    name: ${VOLUME_PREFIX}_opensearch27
  opensearch27-backup:
    name: ${VOLUME_PREFIX}_opensearch27_backup
