#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile --no-emit-index-url --strip-extras requirements.in
#
amqp==5.3.1
    # via kombu
annotated-types==0.7.0
    # via pydantic
anyio==4.7.0
    # via
    #   httpx
    #   starlette
asgiref==3.8.1
    # via
    #   django
    #   django-cors-headers
ata-common==1.1.2
    # via -r requirements.in
authlib==1.3.2
    # via criticalstart-django-admin-sso
beautifulsoup4==4.12.3
    # via ata-common
billiard==4.2.1
    # via celery
boto3==1.35.78
    # via ata-common
botocore==1.35.78
    # via
    #   boto3
    #   s3transfer
celery==5.5.2
    # via
    #   -r requirements.in
    #   django-celery-beat
    #   sentry-sdk
certifi==2024.8.30
    # via
    #   httpcore
    #   httpx
    #   opensearch-py
    #   requests
    #   sentry-sdk
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.0
    # via requests
click==8.1.7
    # via
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
    #   uvicorn
click-didyoumean==0.3.1
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.3.0
    # via celery
configcat-client==9.0.4
    # via
    #   -r requirements.in
    #   ata-common
criticalstart-audit-logs==0.0.12
    # via -r requirements.in
criticalstart-auth==0.0.31
    # via -r requirements.in
criticalstart-data-connectors-sdk==2.0.19
    # via -r requirements.in
criticalstart-django-admin-sso==0.0.11
    # via -r requirements.in
criticalstart-django-admin-themes==0.0.4
    # via -r requirements.in
criticalstart-fastapi-utils==0.0.27
    # via -r requirements.in
criticalstart-service-bus==1.3.0
    # via
    #   -r requirements.in
    #   criticalstart-audit-logs
cron-descriptor==1.4.5
    # via django-celery-beat
cryptography==44.0.1
    # via
    #   ata-common
    #   authlib
dj-database-url==2.3.0
    # via -r requirements.in
django==4.2.20
    # via
    #   -r requirements.in
    #   ata-common
    #   criticalstart-django-admin-themes
    #   dj-database-url
    #   django-celery-beat
    #   django-cors-headers
    #   django-custom-user
    #   django-extensions
    #   django-filter
    #   django-phonenumber-field
    #   django-timezone-field
    #   django-viewflow
    #   sentry-sdk
django-celery-beat==2.7.0
    # via -r requirements.in
django-cors-headers==4.7.0
    # via -r requirements.in
django-custom-user==1.1
    # via -r requirements.in
django-extensions==3.2.3
    # via -r requirements.in
django-filter==24.3
    # via django-viewflow
django-phonenumber-field==8.0.0
    # via ata-common
django-timezone-field==7.0
    # via
    #   ata-common
    #   django-celery-beat
django-viewflow==2.2.10
    # via -r requirements.in
dnspython==2.7.0
    # via email-validator
email-validator==2.2.0
    # via -r requirements.in
enum-compat==0.0.3
    # via configcat-client
events==0.5
    # via opensearch-py
fastapi==0.115.12
    # via
    #   -r requirements.in
    #   criticalstart-auth
    #   criticalstart-fastapi-utils
    #   sentry-sdk
gunicorn==23.0.0
    # via -r requirements.in
h11==0.14.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.7
    # via httpx
httpx==0.28.1
    # via
    #   criticalstart-audit-logs
    #   criticalstart-auth
idna==3.10
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   requests
iniconfig==2.0.0
    # via pytest
isodate==0.7.2
    # via -r requirements.in
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
kombu==5.5.3
    # via
    #   ata-common
    #   celery
    #   criticalstart-service-bus
networkx==3.4.2
    # via -r requirements.in
newrelic==10.7.0
    # via -r requirements.in
opensearch-py==2.8.0
    # via -r requirements.in
packaging==24.2
    # via
    #   gunicorn
    #   pytest
phonenumbers==8.13.51
    # via ata-common
pluggy==1.5.0
    # via pytest
prompt-toolkit==3.0.48
    # via click-repl
psycopg2-binary==2.9.10
    # via -r requirements.in
pycparser==2.22
    # via cffi
pydantic==2.10.3
    # via
    #   criticalstart-audit-logs
    #   criticalstart-auth
    #   criticalstart-data-connectors-sdk
    #   criticalstart-service-bus
    #   fastapi
    #   pydantic-extra-types
pydantic-core==2.27.1
    # via pydantic
pydantic-extra-types==2.10.3
    # via -r requirements.in
pydash==8.0.4
    # via criticalstart-fastapi-utils
pyjwt==2.10.1
    # via
    #   ata-common
    #   criticalstart-auth
    #   criticalstart-django-admin-sso
pyotp==2.9.0
    # via ata-common
pyparsing==3.2.3
    # via -r requirements.in
pytest==8.3.5
    # via
    #   pytest-django
    #   pytest-mock
pytest-django==4.11.1
    # via -r requirements.in
pytest-mock==3.14.0
    # via -r requirements.in
python-crontab==3.2.0
    # via django-celery-beat
python-dateutil==2.9.0.post0
    # via
    #   ata-common
    #   botocore
    #   celery
    #   criticalstart-data-connectors-sdk
    #   opensearch-py
    #   python-crontab
pyyaml==6.0.2
    # via responses
qualname==0.1.0
    # via configcat-client
requests==2.32.3
    # via
    #   ata-common
    #   configcat-client
    #   criticalstart-django-admin-sso
    #   opensearch-py
    #   responses
responses==0.25.7
    # via -r requirements.in
s3transfer==0.10.4
    # via boto3
semver==3.0.2
    # via configcat-client
sentry-sdk==2.19.2
    # via -r requirements.in
six==1.17.0
    # via
    #   python-dateutil
    #   treelib
sniffio==1.3.1
    # via anyio
soupsieve==2.6
    # via beautifulsoup4
sqlparse==0.5.3
    # via django
starlette==0.41.3
    # via fastapi
treelib==1.7.0
    # via ata-common
typing-extensions==4.12.2
    # via
    #   anyio
    #   criticalstart-data-connectors-sdk
    #   dj-database-url
    #   fastapi
    #   pydantic
    #   pydantic-core
    #   pydantic-extra-types
    #   pydash
tzdata==2025.2
    # via
    #   django-celery-beat
    #   kombu
urllib3==2.3.0
    # via
    #   botocore
    #   criticalstart-data-connectors-sdk
    #   opensearch-py
    #   requests
    #   responses
    #   sentry-sdk
uvicorn==0.34.0
    # via -r requirements.in
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.13
    # via prompt-toolkit
whitenoise==6.9.0
    # via -r requirements.in
