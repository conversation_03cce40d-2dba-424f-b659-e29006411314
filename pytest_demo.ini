[pytest]
python_files = demo/tests/*.py
addopts = --reuse-db --ignore=scripts --ignore=tmp --durations=15 --tb=short --cov='apps/demo/' --cov-fail-under=100 --cov-config=demo.coveragerc

filterwarnings =
    ; This will be updated by fastapi in a future version; it's upstream's problem
    ; They have decided that a different implementation is better than the starlette
    ; version since it is not well documented.
    ignore:starlette.middleware.wsgi is deprecated and will be removed in a future release.*

env =
    RAW_ASSET_FILE_SYSTEM_URL=mem://
    # Use the test service bus client id to avoid conflicts with the local environment.
    SERVICE_BUS_CLIENT_ID=asset_inventory_test
    DEMO_ENVIRONMENT=True
